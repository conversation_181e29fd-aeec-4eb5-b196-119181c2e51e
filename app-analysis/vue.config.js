const { defineConfig } = require("@vue/cli-service");
module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    devServer: {
      proxy: {
        "/api": {
          target: "http://gpt-api.corpautohome.com", // 目标服务器地址
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            "^/api": "", // 重写路径（可选）
          },
        },
      },
    },
    output: {
      libraryTarget: "system",
    },
  },
});
