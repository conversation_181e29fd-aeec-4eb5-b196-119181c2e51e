{"name": "@dealer/app-demo-dashboard", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 9100", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "serve:standalone": "vue-cli-service serve --mode standalone --port 9100"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^20.0.0", "@ckeditor/ckeditor5-vue": "^1.0.3", "@fullcalendar/bootstrap": "^5.4.0", "@fullcalendar/core": "^5.4.0", "@fullcalendar/daygrid": "^5.4.0", "@fullcalendar/interaction": "^5.4.0", "@fullcalendar/list": "^5.4.0", "@fullcalendar/timegrid": "^5.4.0", "@fullcalendar/vue": "^5.4.0", "apexcharts": "^3.22.2", "bootstrap": "^4.5.3", "bootstrap-vue": "^2.19.0", "chart.js": "^2.9.4", "core-js": "^3.7.0", "echarts": "^4.9.0", "firebase": "^8.1.1", "leaflet": "^1.7.1", "metismenujs": "^1.2.1", "simplebar-vue": "^1.6.0", "single-spa-vue": "^2.1.0", "sweetalert2": "^10.10.1", "v-click-outside": "^3.1.2", "v-mask": "^2.2.3", "vue": "^2.6.12", "vue-apexcharts": "^1.6.0", "vue-chartist": "^2.3.1", "vue-chartjs": "^3.5.1", "vue-easy-lightbox": "^0.13.0", "vue-echarts": "^5.0.0-beta.0", "vue-form-wizard": "^0.8.4", "vue-i18n": "^8.22.2", "vue-meta": "^2.4.0", "vue-multiselect": "^2.1.6", "vue-number-input-spinner": "^2.2.0", "vue-router": "^3.4.9", "vue-slide-bar": "^1.2.0", "vue-star-rating": "^1.7.0", "vue-sweetalert2": "^4.1.1", "vue-switches": "^2.0.1", "vue-youtube": "^1.4.0", "vue2-datepicker": "^3.8.0", "vue2-dropzone": "^3.6.0", "vue2-google-maps": "^0.10.7", "vue2-leaflet": "^2.6.0", "vuedraggable": "^2.24.3", "vuelidate": "^0.7.6", "vuex": "^3.5.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-service": "^4.3.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.3", "sass-loader": "^8.0.2", "vue-cli-plugin-single-spa": "~3.3.0", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}