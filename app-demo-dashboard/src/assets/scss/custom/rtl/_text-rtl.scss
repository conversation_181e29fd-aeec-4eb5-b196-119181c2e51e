// stylelint-disable declaration-no-important

//
// Text
//


// Responsive alignment

@each $breakpoint in map-keys($grid-breakpoints) {
  @include media-breakpoint-up($breakpoint) {
    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

    .text#{$infix}-left   { text-align: right !important; }
    .text#{$infix}-right  { text-align: left !important; }
    .text#{$infix}-center { text-align: center !important; }
  }
}
