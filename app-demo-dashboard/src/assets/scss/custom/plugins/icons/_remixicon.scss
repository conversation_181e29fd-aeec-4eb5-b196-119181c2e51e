/*
* Remix Icon v2.4.0
* https://remixicon.com
* https://github.com/Remix-Design/RemixIcon
*
* Copyright RemixIcon.com
* Released under the Apache License Version 2.0
*
* Date: 2020-04-20
*/
@font-face {
  font-family: "remixicon";
  src: url('../fonts/remixicon.eot?t=1587359857360'); /* IE9*/
  src: url('../fonts/remixicon.eot?t=1587359857360#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("../fonts/remixicon.woff2?t=1587359857360") format("woff2"),
  url("../fonts/remixicon.woff?t=1587359857360") format("woff"),
  url('../fonts/remixicon.ttf?t=1587359857360') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('../fonts/remixicon.svg?t=1587359857360#remixicon') format('svg'); /* iOS 4.1- */
  font-display: swap;
}

[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ri-lg { font-size: 1.3333em; line-height: 0.75em; vertical-align: -.0667em; }
.ri-xl { font-size: 1.5em; line-height: 0.6666em; vertical-align: -.075em; }
.ri-xxs { font-size: .5em; }
.ri-xs { font-size: .75em; }
.ri-sm { font-size: .875em }
.ri-1x { font-size: 1em; }
.ri-2x { font-size: 2em; }
.ri-3x { font-size: 3em; }
.ri-4x { font-size: 4em; }
.ri-5x { font-size: 5em; }
.ri-6x { font-size: 6em; }
.ri-7x { font-size: 7em; }
.ri-8x { font-size: 8em; }
.ri-9x { font-size: 9em; }
.ri-10x { font-size: 10em; }
.ri-fw { text-align: center; width: 1.25em; }

.ri-4k-fill:before { content: "\ea01"; }
.ri-4k-line:before { content: "\ea02"; }
.ri-a-b:before { content: "\ea03"; }
.ri-account-box-fill:before { content: "\ea04"; }
.ri-account-box-line:before { content: "\ea05"; }
.ri-account-circle-fill:before { content: "\ea06"; }
.ri-account-circle-line:before { content: "\ea07"; }
.ri-account-pin-box-fill:before { content: "\ea08"; }
.ri-account-pin-box-line:before { content: "\ea09"; }
.ri-account-pin-circle-fill:before { content: "\ea0a"; }
.ri-account-pin-circle-line:before { content: "\ea0b"; }
.ri-add-box-fill:before { content: "\ea0c"; }
.ri-add-box-line:before { content: "\ea0d"; }
.ri-add-circle-fill:before { content: "\ea0e"; }
.ri-add-circle-line:before { content: "\ea0f"; }
.ri-add-fill:before { content: "\ea10"; }
.ri-add-line:before { content: "\ea11"; }
.ri-admin-fill:before { content: "\ea12"; }
.ri-admin-line:before { content: "\ea13"; }
.ri-advertisement-fill:before { content: "\ea14"; }
.ri-advertisement-line:before { content: "\ea15"; }
.ri-airplay-fill:before { content: "\ea16"; }
.ri-airplay-line:before { content: "\ea17"; }
.ri-alarm-fill:before { content: "\ea18"; }
.ri-alarm-line:before { content: "\ea19"; }
.ri-alarm-warning-fill:before { content: "\ea1a"; }
.ri-alarm-warning-line:before { content: "\ea1b"; }
.ri-album-fill:before { content: "\ea1c"; }
.ri-album-line:before { content: "\ea1d"; }
.ri-alert-fill:before { content: "\ea1e"; }
.ri-alert-line:before { content: "\ea1f"; }
.ri-aliens-fill:before { content: "\ea20"; }
.ri-aliens-line:before { content: "\ea21"; }
.ri-align-bottom:before { content: "\ea22"; }
.ri-align-center:before { content: "\ea23"; }
.ri-align-justify:before { content: "\ea24"; }
.ri-align-left:before { content: "\ea25"; }
.ri-align-right:before { content: "\ea26"; }
.ri-align-top:before { content: "\ea27"; }
.ri-align-vertically:before { content: "\ea28"; }
.ri-alipay-fill:before { content: "\ea29"; }
.ri-alipay-line:before { content: "\ea2a"; }
.ri-amazon-fill:before { content: "\ea2b"; }
.ri-amazon-line:before { content: "\ea2c"; }
.ri-anchor-fill:before { content: "\ea2d"; }
.ri-anchor-line:before { content: "\ea2e"; }
.ri-ancient-gate-fill:before { content: "\ea2f"; }
.ri-ancient-gate-line:before { content: "\ea30"; }
.ri-ancient-pavilion-fill:before { content: "\ea31"; }
.ri-ancient-pavilion-line:before { content: "\ea32"; }
.ri-android-fill:before { content: "\ea33"; }
.ri-android-line:before { content: "\ea34"; }
.ri-angularjs-fill:before { content: "\ea35"; }
.ri-angularjs-line:before { content: "\ea36"; }
.ri-anticlockwise-2-fill:before { content: "\ea37"; }
.ri-anticlockwise-2-line:before { content: "\ea38"; }
.ri-anticlockwise-fill:before { content: "\ea39"; }
.ri-anticlockwise-line:before { content: "\ea3a"; }
.ri-app-store-fill:before { content: "\ea3b"; }
.ri-app-store-line:before { content: "\ea3c"; }
.ri-apple-fill:before { content: "\ea3d"; }
.ri-apple-line:before { content: "\ea3e"; }
.ri-apps-2-fill:before { content: "\ea3f"; }
.ri-apps-2-line:before { content: "\ea40"; }
.ri-apps-fill:before { content: "\ea41"; }
.ri-apps-line:before { content: "\ea42"; }
.ri-archive-drawer-fill:before { content: "\ea43"; }
.ri-archive-drawer-line:before { content: "\ea44"; }
.ri-archive-fill:before { content: "\ea45"; }
.ri-archive-line:before { content: "\ea46"; }
.ri-arrow-down-circle-fill:before { content: "\ea47"; }
.ri-arrow-down-circle-line:before { content: "\ea48"; }
.ri-arrow-down-fill:before { content: "\ea49"; }
.ri-arrow-down-line:before { content: "\ea4a"; }
.ri-arrow-down-s-fill:before { content: "\ea4b"; }
.ri-arrow-down-s-line:before { content: "\ea4c"; }
.ri-arrow-drop-down-fill:before { content: "\ea4d"; }
.ri-arrow-drop-down-line:before { content: "\ea4e"; }
.ri-arrow-drop-left-fill:before { content: "\ea4f"; }
.ri-arrow-drop-left-line:before { content: "\ea50"; }
.ri-arrow-drop-right-fill:before { content: "\ea51"; }
.ri-arrow-drop-right-line:before { content: "\ea52"; }
.ri-arrow-drop-up-fill:before { content: "\ea53"; }
.ri-arrow-drop-up-line:before { content: "\ea54"; }
.ri-arrow-go-back-fill:before { content: "\ea55"; }
.ri-arrow-go-back-line:before { content: "\ea56"; }
.ri-arrow-go-forward-fill:before { content: "\ea57"; }
.ri-arrow-go-forward-line:before { content: "\ea58"; }
.ri-arrow-left-circle-fill:before { content: "\ea59"; }
.ri-arrow-left-circle-line:before { content: "\ea5a"; }
.ri-arrow-left-down-fill:before { content: "\ea5b"; }
.ri-arrow-left-down-line:before { content: "\ea5c"; }
.ri-arrow-left-fill:before { content: "\ea5d"; }
.ri-arrow-left-line:before { content: "\ea5e"; }
.ri-arrow-left-right-fill:before { content: "\ea5f"; }
.ri-arrow-left-right-line:before { content: "\ea60"; }
.ri-arrow-left-s-fill:before { content: "\ea61"; }
.ri-arrow-left-s-line:before { content: "\ea62"; }
.ri-arrow-left-up-fill:before { content: "\ea63"; }
.ri-arrow-left-up-line:before { content: "\ea64"; }
.ri-arrow-right-circle-fill:before { content: "\ea65"; }
.ri-arrow-right-circle-line:before { content: "\ea66"; }
.ri-arrow-right-down-fill:before { content: "\ea67"; }
.ri-arrow-right-down-line:before { content: "\ea68"; }
.ri-arrow-right-fill:before { content: "\ea69"; }
.ri-arrow-right-line:before { content: "\ea6a"; }
.ri-arrow-right-s-fill:before { content: "\ea6b"; }
.ri-arrow-right-s-line:before { content: "\ea6c"; }
.ri-arrow-right-up-fill:before { content: "\ea6d"; }
.ri-arrow-right-up-line:before { content: "\ea6e"; }
.ri-arrow-up-circle-fill:before { content: "\ea6f"; }
.ri-arrow-up-circle-line:before { content: "\ea70"; }
.ri-arrow-up-down-fill:before { content: "\ea71"; }
.ri-arrow-up-down-line:before { content: "\ea72"; }
.ri-arrow-up-fill:before { content: "\ea73"; }
.ri-arrow-up-line:before { content: "\ea74"; }
.ri-arrow-up-s-fill:before { content: "\ea75"; }
.ri-arrow-up-s-line:before { content: "\ea76"; }
.ri-artboard-2-fill:before { content: "\ea77"; }
.ri-artboard-2-line:before { content: "\ea78"; }
.ri-artboard-fill:before { content: "\ea79"; }
.ri-artboard-line:before { content: "\ea7a"; }
.ri-article-fill:before { content: "\ea7b"; }
.ri-article-line:before { content: "\ea7c"; }
.ri-aspect-ratio-fill:before { content: "\ea7d"; }
.ri-aspect-ratio-line:before { content: "\ea7e"; }
.ri-asterisk:before { content: "\ea7f"; }
.ri-at-fill:before { content: "\ea80"; }
.ri-at-line:before { content: "\ea81"; }
.ri-attachment-2:before { content: "\ea82"; }
.ri-attachment-fill:before { content: "\ea83"; }
.ri-attachment-line:before { content: "\ea84"; }
.ri-auction-fill:before { content: "\ea85"; }
.ri-auction-line:before { content: "\ea86"; }
.ri-award-fill:before { content: "\ea87"; }
.ri-award-line:before { content: "\ea88"; }
.ri-baidu-fill:before { content: "\ea89"; }
.ri-baidu-line:before { content: "\ea8a"; }
.ri-ball-pen-fill:before { content: "\ea8b"; }
.ri-ball-pen-line:before { content: "\ea8c"; }
.ri-bank-card-2-fill:before { content: "\ea8d"; }
.ri-bank-card-2-line:before { content: "\ea8e"; }
.ri-bank-card-fill:before { content: "\ea8f"; }
.ri-bank-card-line:before { content: "\ea90"; }
.ri-bank-fill:before { content: "\ea91"; }
.ri-bank-line:before { content: "\ea92"; }
.ri-bar-chart-2-fill:before { content: "\ea93"; }
.ri-bar-chart-2-line:before { content: "\ea94"; }
.ri-bar-chart-box-fill:before { content: "\ea95"; }
.ri-bar-chart-box-line:before { content: "\ea96"; }
.ri-bar-chart-fill:before { content: "\ea97"; }
.ri-bar-chart-grouped-fill:before { content: "\ea98"; }
.ri-bar-chart-grouped-line:before { content: "\ea99"; }
.ri-bar-chart-horizontal-fill:before { content: "\ea9a"; }
.ri-bar-chart-horizontal-line:before { content: "\ea9b"; }
.ri-bar-chart-line:before { content: "\ea9c"; }
.ri-barcode-box-fill:before { content: "\ea9d"; }
.ri-barcode-box-line:before { content: "\ea9e"; }
.ri-barcode-fill:before { content: "\ea9f"; }
.ri-barcode-line:before { content: "\eaa0"; }
.ri-barricade-fill:before { content: "\eaa1"; }
.ri-barricade-line:before { content: "\eaa2"; }
.ri-base-station-fill:before { content: "\eaa3"; }
.ri-base-station-line:before { content: "\eaa4"; }
.ri-basketball-fill:before { content: "\eaa5"; }
.ri-basketball-line:before { content: "\eaa6"; }
.ri-battery-2-charge-fill:before { content: "\eaa7"; }
.ri-battery-2-charge-line:before { content: "\eaa8"; }
.ri-battery-2-fill:before { content: "\eaa9"; }
.ri-battery-2-line:before { content: "\eaaa"; }
.ri-battery-charge-fill:before { content: "\eaab"; }
.ri-battery-charge-line:before { content: "\eaac"; }
.ri-battery-fill:before { content: "\eaad"; }
.ri-battery-line:before { content: "\eaae"; }
.ri-battery-low-fill:before { content: "\eaaf"; }
.ri-battery-low-line:before { content: "\eab0"; }
.ri-battery-saver-fill:before { content: "\eab1"; }
.ri-battery-saver-line:before { content: "\eab2"; }
.ri-battery-share-fill:before { content: "\eab3"; }
.ri-battery-share-line:before { content: "\eab4"; }
.ri-bear-smile-fill:before { content: "\eab5"; }
.ri-bear-smile-line:before { content: "\eab6"; }
.ri-behance-fill:before { content: "\eab7"; }
.ri-behance-line:before { content: "\eab8"; }
.ri-bell-fill:before { content: "\eab9"; }
.ri-bell-line:before { content: "\eaba"; }
.ri-bike-fill:before { content: "\eabb"; }
.ri-bike-line:before { content: "\eabc"; }
.ri-bilibili-fill:before { content: "\eabd"; }
.ri-bilibili-line:before { content: "\eabe"; }
.ri-bill-fill:before { content: "\eabf"; }
.ri-bill-line:before { content: "\eac0"; }
.ri-billiards-fill:before { content: "\eac1"; }
.ri-billiards-line:before { content: "\eac2"; }
.ri-bit-coin-fill:before { content: "\eac3"; }
.ri-bit-coin-line:before { content: "\eac4"; }
.ri-blaze-fill:before { content: "\eac5"; }
.ri-blaze-line:before { content: "\eac6"; }
.ri-bluetooth-connect-fill:before { content: "\eac7"; }
.ri-bluetooth-connect-line:before { content: "\eac8"; }
.ri-bluetooth-fill:before { content: "\eac9"; }
.ri-bluetooth-line:before { content: "\eaca"; }
.ri-blur-off-fill:before { content: "\eacb"; }
.ri-blur-off-line:before { content: "\eacc"; }
.ri-body-scan-fill:before { content: "\eacd"; }
.ri-body-scan-line:before { content: "\eace"; }
.ri-bold:before { content: "\eacf"; }
.ri-book-2-fill:before { content: "\ead0"; }
.ri-book-2-line:before { content: "\ead1"; }
.ri-book-3-fill:before { content: "\ead2"; }
.ri-book-3-line:before { content: "\ead3"; }
.ri-book-fill:before { content: "\ead4"; }
.ri-book-line:before { content: "\ead5"; }
.ri-book-mark-fill:before { content: "\ead6"; }
.ri-book-mark-line:before { content: "\ead7"; }
.ri-book-open-fill:before { content: "\ead8"; }
.ri-book-open-line:before { content: "\ead9"; }
.ri-book-read-fill:before { content: "\eada"; }
.ri-book-read-line:before { content: "\eadb"; }
.ri-booklet-fill:before { content: "\eadc"; }
.ri-booklet-line:before { content: "\eadd"; }
.ri-bookmark-2-fill:before { content: "\eade"; }
.ri-bookmark-2-line:before { content: "\eadf"; }
.ri-bookmark-3-fill:before { content: "\eae0"; }
.ri-bookmark-3-line:before { content: "\eae1"; }
.ri-bookmark-fill:before { content: "\eae2"; }
.ri-bookmark-line:before { content: "\eae3"; }
.ri-boxing-fill:before { content: "\eae4"; }
.ri-boxing-line:before { content: "\eae5"; }
.ri-braces-fill:before { content: "\eae6"; }
.ri-braces-line:before { content: "\eae7"; }
.ri-brackets-fill:before { content: "\eae8"; }
.ri-brackets-line:before { content: "\eae9"; }
.ri-briefcase-2-fill:before { content: "\eaea"; }
.ri-briefcase-2-line:before { content: "\eaeb"; }
.ri-briefcase-3-fill:before { content: "\eaec"; }
.ri-briefcase-3-line:before { content: "\eaed"; }
.ri-briefcase-4-fill:before { content: "\eaee"; }
.ri-briefcase-4-line:before { content: "\eaef"; }
.ri-briefcase-5-fill:before { content: "\eaf0"; }
.ri-briefcase-5-line:before { content: "\eaf1"; }
.ri-briefcase-fill:before { content: "\eaf2"; }
.ri-briefcase-line:before { content: "\eaf3"; }
.ri-bring-forward:before { content: "\eaf4"; }
.ri-bring-to-front:before { content: "\eaf5"; }
.ri-broadcast-fill:before { content: "\eaf6"; }
.ri-broadcast-line:before { content: "\eaf7"; }
.ri-brush-2-fill:before { content: "\eaf8"; }
.ri-brush-2-line:before { content: "\eaf9"; }
.ri-brush-3-fill:before { content: "\eafa"; }
.ri-brush-3-line:before { content: "\eafb"; }
.ri-brush-4-fill:before { content: "\eafc"; }
.ri-brush-4-line:before { content: "\eafd"; }
.ri-brush-fill:before { content: "\eafe"; }
.ri-brush-line:before { content: "\eaff"; }
.ri-bubble-chart-fill:before { content: "\eb00"; }
.ri-bubble-chart-line:before { content: "\eb01"; }
.ri-bug-2-fill:before { content: "\eb02"; }
.ri-bug-2-line:before { content: "\eb03"; }
.ri-bug-fill:before { content: "\eb04"; }
.ri-bug-line:before { content: "\eb05"; }
.ri-building-2-fill:before { content: "\eb06"; }
.ri-building-2-line:before { content: "\eb07"; }
.ri-building-3-fill:before { content: "\eb08"; }
.ri-building-3-line:before { content: "\eb09"; }
.ri-building-4-fill:before { content: "\eb0a"; }
.ri-building-4-line:before { content: "\eb0b"; }
.ri-building-fill:before { content: "\eb0c"; }
.ri-building-line:before { content: "\eb0d"; }
.ri-bus-2-fill:before { content: "\eb0e"; }
.ri-bus-2-line:before { content: "\eb0f"; }
.ri-bus-fill:before { content: "\eb10"; }
.ri-bus-line:before { content: "\eb11"; }
.ri-bus-wifi-fill:before { content: "\eb12"; }
.ri-bus-wifi-line:before { content: "\eb13"; }
.ri-cake-2-fill:before { content: "\eb14"; }
.ri-cake-2-line:before { content: "\eb15"; }
.ri-cake-3-fill:before { content: "\eb16"; }
.ri-cake-3-line:before { content: "\eb17"; }
.ri-cake-fill:before { content: "\eb18"; }
.ri-cake-line:before { content: "\eb19"; }
.ri-calculator-fill:before { content: "\eb1a"; }
.ri-calculator-line:before { content: "\eb1b"; }
.ri-calendar-2-fill:before { content: "\eb1c"; }
.ri-calendar-2-line:before { content: "\eb1d"; }
.ri-calendar-check-fill:before { content: "\eb1e"; }
.ri-calendar-check-line:before { content: "\eb1f"; }
.ri-calendar-event-fill:before { content: "\eb20"; }
.ri-calendar-event-line:before { content: "\eb21"; }
.ri-calendar-fill:before { content: "\eb22"; }
.ri-calendar-line:before { content: "\eb23"; }
.ri-calendar-todo-fill:before { content: "\eb24"; }
.ri-calendar-todo-line:before { content: "\eb25"; }
.ri-camera-2-fill:before { content: "\eb26"; }
.ri-camera-2-line:before { content: "\eb27"; }
.ri-camera-3-fill:before { content: "\eb28"; }
.ri-camera-3-line:before { content: "\eb29"; }
.ri-camera-fill:before { content: "\eb2a"; }
.ri-camera-lens-fill:before { content: "\eb2b"; }
.ri-camera-lens-line:before { content: "\eb2c"; }
.ri-camera-line:before { content: "\eb2d"; }
.ri-camera-off-fill:before { content: "\eb2e"; }
.ri-camera-off-line:before { content: "\eb2f"; }
.ri-camera-switch-fill:before { content: "\eb30"; }
.ri-camera-switch-line:before { content: "\eb31"; }
.ri-car-fill:before { content: "\eb32"; }
.ri-car-line:before { content: "\eb33"; }
.ri-car-washing-fill:before { content: "\eb34"; }
.ri-car-washing-line:before { content: "\eb35"; }
.ri-caravan-fill:before { content: "\eb36"; }
.ri-caravan-line:before { content: "\eb37"; }
.ri-cast-fill:before { content: "\eb38"; }
.ri-cast-line:before { content: "\eb39"; }
.ri-cellphone-fill:before { content: "\eb3a"; }
.ri-cellphone-line:before { content: "\eb3b"; }
.ri-celsius-fill:before { content: "\eb3c"; }
.ri-celsius-line:before { content: "\eb3d"; }
.ri-centos-fill:before { content: "\eb3e"; }
.ri-centos-line:before { content: "\eb3f"; }
.ri-character-recognition-fill:before { content: "\eb40"; }
.ri-character-recognition-line:before { content: "\eb41"; }
.ri-charging-pile-2-fill:before { content: "\eb42"; }
.ri-charging-pile-2-line:before { content: "\eb43"; }
.ri-charging-pile-fill:before { content: "\eb44"; }
.ri-charging-pile-line:before { content: "\eb45"; }
.ri-chat-1-fill:before { content: "\eb46"; }
.ri-chat-1-line:before { content: "\eb47"; }
.ri-chat-2-fill:before { content: "\eb48"; }
.ri-chat-2-line:before { content: "\eb49"; }
.ri-chat-3-fill:before { content: "\eb4a"; }
.ri-chat-3-line:before { content: "\eb4b"; }
.ri-chat-4-fill:before { content: "\eb4c"; }
.ri-chat-4-line:before { content: "\eb4d"; }
.ri-chat-check-fill:before { content: "\eb4e"; }
.ri-chat-check-line:before { content: "\eb4f"; }
.ri-chat-delete-fill:before { content: "\eb50"; }
.ri-chat-delete-line:before { content: "\eb51"; }
.ri-chat-download-fill:before { content: "\eb52"; }
.ri-chat-download-line:before { content: "\eb53"; }
.ri-chat-follow-up-fill:before { content: "\eb54"; }
.ri-chat-follow-up-line:before { content: "\eb55"; }
.ri-chat-forward-fill:before { content: "\eb56"; }
.ri-chat-forward-line:before { content: "\eb57"; }
.ri-chat-heart-fill:before { content: "\eb58"; }
.ri-chat-heart-line:before { content: "\eb59"; }
.ri-chat-history-fill:before { content: "\eb5a"; }
.ri-chat-history-line:before { content: "\eb5b"; }
.ri-chat-new-fill:before { content: "\eb5c"; }
.ri-chat-new-line:before { content: "\eb5d"; }
.ri-chat-off-fill:before { content: "\eb5e"; }
.ri-chat-off-line:before { content: "\eb5f"; }
.ri-chat-poll-fill:before { content: "\eb60"; }
.ri-chat-poll-line:before { content: "\eb61"; }
.ri-chat-private-fill:before { content: "\eb62"; }
.ri-chat-private-line:before { content: "\eb63"; }
.ri-chat-quote-fill:before { content: "\eb64"; }
.ri-chat-quote-line:before { content: "\eb65"; }
.ri-chat-settings-fill:before { content: "\eb66"; }
.ri-chat-settings-line:before { content: "\eb67"; }
.ri-chat-smile-2-fill:before { content: "\eb68"; }
.ri-chat-smile-2-line:before { content: "\eb69"; }
.ri-chat-smile-3-fill:before { content: "\eb6a"; }
.ri-chat-smile-3-line:before { content: "\eb6b"; }
.ri-chat-smile-fill:before { content: "\eb6c"; }
.ri-chat-smile-line:before { content: "\eb6d"; }
.ri-chat-upload-fill:before { content: "\eb6e"; }
.ri-chat-upload-line:before { content: "\eb6f"; }
.ri-chat-voice-fill:before { content: "\eb70"; }
.ri-chat-voice-line:before { content: "\eb71"; }
.ri-check-double-fill:before { content: "\eb72"; }
.ri-check-double-line:before { content: "\eb73"; }
.ri-check-fill:before { content: "\eb74"; }
.ri-check-line:before { content: "\eb75"; }
.ri-checkbox-blank-circle-fill:before { content: "\eb76"; }
.ri-checkbox-blank-circle-line:before { content: "\eb77"; }
.ri-checkbox-blank-fill:before { content: "\eb78"; }
.ri-checkbox-blank-line:before { content: "\eb79"; }
.ri-checkbox-circle-fill:before { content: "\eb7a"; }
.ri-checkbox-circle-line:before { content: "\eb7b"; }
.ri-checkbox-fill:before { content: "\eb7c"; }
.ri-checkbox-indeterminate-fill:before { content: "\eb7d"; }
.ri-checkbox-indeterminate-line:before { content: "\eb7e"; }
.ri-checkbox-line:before { content: "\eb7f"; }
.ri-checkbox-multiple-blank-fill:before { content: "\eb80"; }
.ri-checkbox-multiple-blank-line:before { content: "\eb81"; }
.ri-checkbox-multiple-fill:before { content: "\eb82"; }
.ri-checkbox-multiple-line:before { content: "\eb83"; }
.ri-china-railway-fill:before { content: "\eb84"; }
.ri-china-railway-line:before { content: "\eb85"; }
.ri-chrome-fill:before { content: "\eb86"; }
.ri-chrome-line:before { content: "\eb87"; }
.ri-clapperboard-fill:before { content: "\eb88"; }
.ri-clapperboard-line:before { content: "\eb89"; }
.ri-clipboard-fill:before { content: "\eb8a"; }
.ri-clipboard-line:before { content: "\eb8b"; }
.ri-clockwise-2-fill:before { content: "\eb8c"; }
.ri-clockwise-2-line:before { content: "\eb8d"; }
.ri-clockwise-fill:before { content: "\eb8e"; }
.ri-clockwise-line:before { content: "\eb8f"; }
.ri-close-circle-fill:before { content: "\eb90"; }
.ri-close-circle-line:before { content: "\eb91"; }
.ri-close-fill:before { content: "\eb92"; }
.ri-close-line:before { content: "\eb93"; }
.ri-cloud-fill:before { content: "\eb94"; }
.ri-cloud-line:before { content: "\eb95"; }
.ri-cloud-off-fill:before { content: "\eb96"; }
.ri-cloud-off-line:before { content: "\eb97"; }
.ri-cloud-windy-fill:before { content: "\eb98"; }
.ri-cloud-windy-line:before { content: "\eb99"; }
.ri-cloudy-2-fill:before { content: "\eb9a"; }
.ri-cloudy-2-line:before { content: "\eb9b"; }
.ri-cloudy-fill:before { content: "\eb9c"; }
.ri-cloudy-line:before { content: "\eb9d"; }
.ri-code-box-fill:before { content: "\eb9e"; }
.ri-code-box-line:before { content: "\eb9f"; }
.ri-code-fill:before { content: "\eba0"; }
.ri-code-line:before { content: "\eba1"; }
.ri-code-s-fill:before { content: "\eba2"; }
.ri-code-s-line:before { content: "\eba3"; }
.ri-code-s-slash-fill:before { content: "\eba4"; }
.ri-code-s-slash-line:before { content: "\eba5"; }
.ri-code-view:before { content: "\eba6"; }
.ri-codepen-fill:before { content: "\eba7"; }
.ri-codepen-line:before { content: "\eba8"; }
.ri-coin-fill:before { content: "\eba9"; }
.ri-coin-line:before { content: "\ebaa"; }
.ri-coins-fill:before { content: "\ebab"; }
.ri-coins-line:before { content: "\ebac"; }
.ri-command-fill:before { content: "\ebad"; }
.ri-command-line:before { content: "\ebae"; }
.ri-community-fill:before { content: "\ebaf"; }
.ri-community-line:before { content: "\ebb0"; }
.ri-compass-2-fill:before { content: "\ebb1"; }
.ri-compass-2-line:before { content: "\ebb2"; }
.ri-compass-3-fill:before { content: "\ebb3"; }
.ri-compass-3-line:before { content: "\ebb4"; }
.ri-compass-4-fill:before { content: "\ebb5"; }
.ri-compass-4-line:before { content: "\ebb6"; }
.ri-compass-discover-fill:before { content: "\ebb7"; }
.ri-compass-discover-line:before { content: "\ebb8"; }
.ri-compass-fill:before { content: "\ebb9"; }
.ri-compass-line:before { content: "\ebba"; }
.ri-compasses-2-fill:before { content: "\ebbb"; }
.ri-compasses-2-line:before { content: "\ebbc"; }
.ri-compasses-fill:before { content: "\ebbd"; }
.ri-compasses-line:before { content: "\ebbe"; }
.ri-computer-fill:before { content: "\ebbf"; }
.ri-computer-line:before { content: "\ebc0"; }
.ri-contacts-book-2-fill:before { content: "\ebc1"; }
.ri-contacts-book-2-line:before { content: "\ebc2"; }
.ri-contacts-book-fill:before { content: "\ebc3"; }
.ri-contacts-book-line:before { content: "\ebc4"; }
.ri-contacts-book-upload-fill:before { content: "\ebc5"; }
.ri-contacts-book-upload-line:before { content: "\ebc6"; }
.ri-contacts-fill:before { content: "\ebc7"; }
.ri-contacts-line:before { content: "\ebc8"; }
.ri-contrast-2-fill:before { content: "\ebc9"; }
.ri-contrast-2-line:before { content: "\ebca"; }
.ri-contrast-drop-2-fill:before { content: "\ebcb"; }
.ri-contrast-drop-2-line:before { content: "\ebcc"; }
.ri-contrast-drop-fill:before { content: "\ebcd"; }
.ri-contrast-drop-line:before { content: "\ebce"; }
.ri-contrast-fill:before { content: "\ebcf"; }
.ri-contrast-line:before { content: "\ebd0"; }
.ri-copper-coin-fill:before { content: "\ebd1"; }
.ri-copper-coin-line:before { content: "\ebd2"; }
.ri-copper-diamond-fill:before { content: "\ebd3"; }
.ri-copper-diamond-line:before { content: "\ebd4"; }
.ri-copyright-fill:before { content: "\ebd5"; }
.ri-copyright-line:before { content: "\ebd6"; }
.ri-coreos-fill:before { content: "\ebd7"; }
.ri-coreos-line:before { content: "\ebd8"; }
.ri-coupon-2-fill:before { content: "\ebd9"; }
.ri-coupon-2-line:before { content: "\ebda"; }
.ri-coupon-3-fill:before { content: "\ebdb"; }
.ri-coupon-3-line:before { content: "\ebdc"; }
.ri-coupon-4-fill:before { content: "\ebdd"; }
.ri-coupon-4-line:before { content: "\ebde"; }
.ri-coupon-5-fill:before { content: "\ebdf"; }
.ri-coupon-5-line:before { content: "\ebe0"; }
.ri-coupon-fill:before { content: "\ebe1"; }
.ri-coupon-line:before { content: "\ebe2"; }
.ri-cpu-fill:before { content: "\ebe3"; }
.ri-cpu-line:before { content: "\ebe4"; }
.ri-creative-commons-by-fill:before { content: "\ebe5"; }
.ri-creative-commons-by-line:before { content: "\ebe6"; }
.ri-creative-commons-fill:before { content: "\ebe7"; }
.ri-creative-commons-line:before { content: "\ebe8"; }
.ri-creative-commons-nc-fill:before { content: "\ebe9"; }
.ri-creative-commons-nc-line:before { content: "\ebea"; }
.ri-creative-commons-nd-fill:before { content: "\ebeb"; }
.ri-creative-commons-nd-line:before { content: "\ebec"; }
.ri-creative-commons-sa-fill:before { content: "\ebed"; }
.ri-creative-commons-sa-line:before { content: "\ebee"; }
.ri-creative-commons-zero-fill:before { content: "\ebef"; }
.ri-creative-commons-zero-line:before { content: "\ebf0"; }
.ri-criminal-fill:before { content: "\ebf1"; }
.ri-criminal-line:before { content: "\ebf2"; }
.ri-crop-2-fill:before { content: "\ebf3"; }
.ri-crop-2-line:before { content: "\ebf4"; }
.ri-crop-fill:before { content: "\ebf5"; }
.ri-crop-line:before { content: "\ebf6"; }
.ri-css3-fill:before { content: "\ebf7"; }
.ri-css3-line:before { content: "\ebf8"; }
.ri-cup-fill:before { content: "\ebf9"; }
.ri-cup-line:before { content: "\ebfa"; }
.ri-currency-fill:before { content: "\ebfb"; }
.ri-currency-line:before { content: "\ebfc"; }
.ri-cursor-fill:before { content: "\ebfd"; }
.ri-cursor-line:before { content: "\ebfe"; }
.ri-customer-service-2-fill:before { content: "\ebff"; }
.ri-customer-service-2-line:before { content: "\ec00"; }
.ri-customer-service-fill:before { content: "\ec01"; }
.ri-customer-service-line:before { content: "\ec02"; }
.ri-dashboard-fill:before { content: "\ec03"; }
.ri-dashboard-line:before { content: "\ec04"; }
.ri-database-2-fill:before { content: "\ec05"; }
.ri-database-2-line:before { content: "\ec06"; }
.ri-database-fill:before { content: "\ec07"; }
.ri-database-line:before { content: "\ec08"; }
.ri-delete-back-2-fill:before { content: "\ec09"; }
.ri-delete-back-2-line:before { content: "\ec0a"; }
.ri-delete-back-fill:before { content: "\ec0b"; }
.ri-delete-back-line:before { content: "\ec0c"; }
.ri-delete-bin-2-fill:before { content: "\ec0d"; }
.ri-delete-bin-2-line:before { content: "\ec0e"; }
.ri-delete-bin-3-fill:before { content: "\ec0f"; }
.ri-delete-bin-3-line:before { content: "\ec10"; }
.ri-delete-bin-4-fill:before { content: "\ec11"; }
.ri-delete-bin-4-line:before { content: "\ec12"; }
.ri-delete-bin-5-fill:before { content: "\ec13"; }
.ri-delete-bin-5-line:before { content: "\ec14"; }
.ri-delete-bin-6-fill:before { content: "\ec15"; }
.ri-delete-bin-6-line:before { content: "\ec16"; }
.ri-delete-bin-7-fill:before { content: "\ec17"; }
.ri-delete-bin-7-line:before { content: "\ec18"; }
.ri-delete-bin-fill:before { content: "\ec19"; }
.ri-delete-bin-line:before { content: "\ec1a"; }
.ri-device-fill:before { content: "\ec1b"; }
.ri-device-line:before { content: "\ec1c"; }
.ri-device-recover-fill:before { content: "\ec1d"; }
.ri-device-recover-line:before { content: "\ec1e"; }
.ri-dingding-fill:before { content: "\ec1f"; }
.ri-dingding-line:before { content: "\ec20"; }
.ri-direction-fill:before { content: "\ec21"; }
.ri-direction-line:before { content: "\ec22"; }
.ri-disc-fill:before { content: "\ec23"; }
.ri-disc-line:before { content: "\ec24"; }
.ri-discord-fill:before { content: "\ec25"; }
.ri-discord-line:before { content: "\ec26"; }
.ri-discuss-fill:before { content: "\ec27"; }
.ri-discuss-line:before { content: "\ec28"; }
.ri-disqus-fill:before { content: "\ec29"; }
.ri-disqus-line:before { content: "\ec2a"; }
.ri-divide-fill:before { content: "\ec2b"; }
.ri-divide-line:before { content: "\ec2c"; }
.ri-donut-chart-fill:before { content: "\ec2d"; }
.ri-donut-chart-line:before { content: "\ec2e"; }
.ri-door-lock-box-fill:before { content: "\ec2f"; }
.ri-door-lock-box-line:before { content: "\ec30"; }
.ri-door-lock-fill:before { content: "\ec31"; }
.ri-door-lock-line:before { content: "\ec32"; }
.ri-douban-fill:before { content: "\ec33"; }
.ri-douban-line:before { content: "\ec34"; }
.ri-double-quotes-l:before { content: "\ec35"; }
.ri-double-quotes-r:before { content: "\ec36"; }
.ri-download-2-fill:before { content: "\ec37"; }
.ri-download-2-line:before { content: "\ec38"; }
.ri-download-cloud-2-fill:before { content: "\ec39"; }
.ri-download-cloud-2-line:before { content: "\ec3a"; }
.ri-download-cloud-fill:before { content: "\ec3b"; }
.ri-download-cloud-line:before { content: "\ec3c"; }
.ri-download-fill:before { content: "\ec3d"; }
.ri-download-line:before { content: "\ec3e"; }
.ri-drag-drop-fill:before { content: "\ec3f"; }
.ri-drag-drop-line:before { content: "\ec40"; }
.ri-drag-move-2-fill:before { content: "\ec41"; }
.ri-drag-move-2-line:before { content: "\ec42"; }
.ri-drag-move-fill:before { content: "\ec43"; }
.ri-drag-move-line:before { content: "\ec44"; }
.ri-dribbble-fill:before { content: "\ec45"; }
.ri-dribbble-line:before { content: "\ec46"; }
.ri-drive-fill:before { content: "\ec47"; }
.ri-drive-line:before { content: "\ec48"; }
.ri-drizzle-fill:before { content: "\ec49"; }
.ri-drizzle-line:before { content: "\ec4a"; }
.ri-drop-fill:before { content: "\ec4b"; }
.ri-drop-line:before { content: "\ec4c"; }
.ri-dropbox-fill:before { content: "\ec4d"; }
.ri-dropbox-line:before { content: "\ec4e"; }
.ri-dual-sim-1-fill:before { content: "\ec4f"; }
.ri-dual-sim-1-line:before { content: "\ec50"; }
.ri-dual-sim-2-fill:before { content: "\ec51"; }
.ri-dual-sim-2-line:before { content: "\ec52"; }
.ri-dv-fill:before { content: "\ec53"; }
.ri-dv-line:before { content: "\ec54"; }
.ri-dvd-fill:before { content: "\ec55"; }
.ri-dvd-line:before { content: "\ec56"; }
.ri-e-bike-2-fill:before { content: "\ec57"; }
.ri-e-bike-2-line:before { content: "\ec58"; }
.ri-e-bike-fill:before { content: "\ec59"; }
.ri-e-bike-line:before { content: "\ec5a"; }
.ri-earth-fill:before { content: "\ec5b"; }
.ri-earth-line:before { content: "\ec5c"; }
.ri-earthquake-fill:before { content: "\ec5d"; }
.ri-earthquake-line:before { content: "\ec5e"; }
.ri-edge-fill:before { content: "\ec5f"; }
.ri-edge-line:before { content: "\ec60"; }
.ri-edit-2-fill:before { content: "\ec61"; }
.ri-edit-2-line:before { content: "\ec62"; }
.ri-edit-box-fill:before { content: "\ec63"; }
.ri-edit-box-line:before { content: "\ec64"; }
.ri-edit-circle-fill:before { content: "\ec65"; }
.ri-edit-circle-line:before { content: "\ec66"; }
.ri-edit-fill:before { content: "\ec67"; }
.ri-edit-line:before { content: "\ec68"; }
.ri-eject-fill:before { content: "\ec69"; }
.ri-eject-line:before { content: "\ec6a"; }
.ri-emotion-2-fill:before { content: "\ec6b"; }
.ri-emotion-2-line:before { content: "\ec6c"; }
.ri-emotion-fill:before { content: "\ec6d"; }
.ri-emotion-happy-fill:before { content: "\ec6e"; }
.ri-emotion-happy-line:before { content: "\ec6f"; }
.ri-emotion-laugh-fill:before { content: "\ec70"; }
.ri-emotion-laugh-line:before { content: "\ec71"; }
.ri-emotion-line:before { content: "\ec72"; }
.ri-emotion-normal-fill:before { content: "\ec73"; }
.ri-emotion-normal-line:before { content: "\ec74"; }
.ri-emotion-sad-fill:before { content: "\ec75"; }
.ri-emotion-sad-line:before { content: "\ec76"; }
.ri-emotion-unhappy-fill:before { content: "\ec77"; }
.ri-emotion-unhappy-line:before { content: "\ec78"; }
.ri-emphasis-cn:before { content: "\ec79"; }
.ri-emphasis:before { content: "\ec7a"; }
.ri-english-input:before { content: "\ec7b"; }
.ri-equalizer-fill:before { content: "\ec7c"; }
.ri-equalizer-line:before { content: "\ec7d"; }
.ri-eraser-fill:before { content: "\ec7e"; }
.ri-eraser-line:before { content: "\ec7f"; }
.ri-error-warning-fill:before { content: "\ec80"; }
.ri-error-warning-line:before { content: "\ec81"; }
.ri-evernote-fill:before { content: "\ec82"; }
.ri-evernote-line:before { content: "\ec83"; }
.ri-exchange-box-fill:before { content: "\ec84"; }
.ri-exchange-box-line:before { content: "\ec85"; }
.ri-exchange-cny-fill:before { content: "\ec86"; }
.ri-exchange-cny-line:before { content: "\ec87"; }
.ri-exchange-dollar-fill:before { content: "\ec88"; }
.ri-exchange-dollar-line:before { content: "\ec89"; }
.ri-exchange-fill:before { content: "\ec8a"; }
.ri-exchange-funds-fill:before { content: "\ec8b"; }
.ri-exchange-funds-line:before { content: "\ec8c"; }
.ri-exchange-line:before { content: "\ec8d"; }
.ri-external-link-fill:before { content: "\ec8e"; }
.ri-external-link-line:before { content: "\ec8f"; }
.ri-eye-2-fill:before { content: "\ec90"; }
.ri-eye-2-line:before { content: "\ec91"; }
.ri-eye-close-fill:before { content: "\ec92"; }
.ri-eye-close-line:before { content: "\ec93"; }
.ri-eye-fill:before { content: "\ec94"; }
.ri-eye-line:before { content: "\ec95"; }
.ri-eye-off-fill:before { content: "\ec96"; }
.ri-eye-off-line:before { content: "\ec97"; }
.ri-facebook-box-fill:before { content: "\ec98"; }
.ri-facebook-box-line:before { content: "\ec99"; }
.ri-facebook-circle-fill:before { content: "\ec9a"; }
.ri-facebook-circle-line:before { content: "\ec9b"; }
.ri-facebook-fill:before { content: "\ec9c"; }
.ri-facebook-line:before { content: "\ec9d"; }
.ri-fahrenheit-fill:before { content: "\ec9e"; }
.ri-fahrenheit-line:before { content: "\ec9f"; }
.ri-feedback-fill:before { content: "\eca0"; }
.ri-feedback-line:before { content: "\eca1"; }
.ri-file-2-fill:before { content: "\eca2"; }
.ri-file-2-line:before { content: "\eca3"; }
.ri-file-3-fill:before { content: "\eca4"; }
.ri-file-3-line:before { content: "\eca5"; }
.ri-file-4-fill:before { content: "\eca6"; }
.ri-file-4-line:before { content: "\eca7"; }
.ri-file-add-fill:before { content: "\eca8"; }
.ri-file-add-line:before { content: "\eca9"; }
.ri-file-chart-2-fill:before { content: "\ecaa"; }
.ri-file-chart-2-line:before { content: "\ecab"; }
.ri-file-chart-fill:before { content: "\ecac"; }
.ri-file-chart-line:before { content: "\ecad"; }
.ri-file-cloud-fill:before { content: "\ecae"; }
.ri-file-cloud-line:before { content: "\ecaf"; }
.ri-file-code-fill:before { content: "\ecb0"; }
.ri-file-code-line:before { content: "\ecb1"; }
.ri-file-copy-2-fill:before { content: "\ecb2"; }
.ri-file-copy-2-line:before { content: "\ecb3"; }
.ri-file-copy-fill:before { content: "\ecb4"; }
.ri-file-copy-line:before { content: "\ecb5"; }
.ri-file-damage-fill:before { content: "\ecb6"; }
.ri-file-damage-line:before { content: "\ecb7"; }
.ri-file-download-fill:before { content: "\ecb8"; }
.ri-file-download-line:before { content: "\ecb9"; }
.ri-file-edit-fill:before { content: "\ecba"; }
.ri-file-edit-line:before { content: "\ecbb"; }
.ri-file-excel-2-fill:before { content: "\ecbc"; }
.ri-file-excel-2-line:before { content: "\ecbd"; }
.ri-file-excel-fill:before { content: "\ecbe"; }
.ri-file-excel-line:before { content: "\ecbf"; }
.ri-file-fill:before { content: "\ecc0"; }
.ri-file-forbid-fill:before { content: "\ecc1"; }
.ri-file-forbid-line:before { content: "\ecc2"; }
.ri-file-gif-fill:before { content: "\ecc3"; }
.ri-file-gif-line:before { content: "\ecc4"; }
.ri-file-history-fill:before { content: "\ecc5"; }
.ri-file-history-line:before { content: "\ecc6"; }
.ri-file-hwp-fill:before { content: "\ecc7"; }
.ri-file-hwp-line:before { content: "\ecc8"; }
.ri-file-info-fill:before { content: "\ecc9"; }
.ri-file-info-line:before { content: "\ecca"; }
.ri-file-line:before { content: "\eccb"; }
.ri-file-list-2-fill:before { content: "\eccc"; }
.ri-file-list-2-line:before { content: "\eccd"; }
.ri-file-list-3-fill:before { content: "\ecce"; }
.ri-file-list-3-line:before { content: "\eccf"; }
.ri-file-list-fill:before { content: "\ecd0"; }
.ri-file-list-line:before { content: "\ecd1"; }
.ri-file-lock-fill:before { content: "\ecd2"; }
.ri-file-lock-line:before { content: "\ecd3"; }
.ri-file-mark-fill:before { content: "\ecd4"; }
.ri-file-mark-line:before { content: "\ecd5"; }
.ri-file-music-fill:before { content: "\ecd6"; }
.ri-file-music-line:before { content: "\ecd7"; }
.ri-file-paper-2-fill:before { content: "\ecd8"; }
.ri-file-paper-2-line:before { content: "\ecd9"; }
.ri-file-paper-fill:before { content: "\ecda"; }
.ri-file-paper-line:before { content: "\ecdb"; }
.ri-file-pdf-fill:before { content: "\ecdc"; }
.ri-file-pdf-line:before { content: "\ecdd"; }
.ri-file-ppt-2-fill:before { content: "\ecde"; }
.ri-file-ppt-2-line:before { content: "\ecdf"; }
.ri-file-ppt-fill:before { content: "\ece0"; }
.ri-file-ppt-line:before { content: "\ece1"; }
.ri-file-reduce-fill:before { content: "\ece2"; }
.ri-file-reduce-line:before { content: "\ece3"; }
.ri-file-search-fill:before { content: "\ece4"; }
.ri-file-search-line:before { content: "\ece5"; }
.ri-file-settings-fill:before { content: "\ece6"; }
.ri-file-settings-line:before { content: "\ece7"; }
.ri-file-shield-2-fill:before { content: "\ece8"; }
.ri-file-shield-2-line:before { content: "\ece9"; }
.ri-file-shield-fill:before { content: "\ecea"; }
.ri-file-shield-line:before { content: "\eceb"; }
.ri-file-shred-fill:before { content: "\ecec"; }
.ri-file-shred-line:before { content: "\eced"; }
.ri-file-text-fill:before { content: "\ecee"; }
.ri-file-text-line:before { content: "\ecef"; }
.ri-file-transfer-fill:before { content: "\ecf0"; }
.ri-file-transfer-line:before { content: "\ecf1"; }
.ri-file-unknow-fill:before { content: "\ecf2"; }
.ri-file-unknow-line:before { content: "\ecf3"; }
.ri-file-upload-fill:before { content: "\ecf4"; }
.ri-file-upload-line:before { content: "\ecf5"; }
.ri-file-user-fill:before { content: "\ecf6"; }
.ri-file-user-line:before { content: "\ecf7"; }
.ri-file-warning-fill:before { content: "\ecf8"; }
.ri-file-warning-line:before { content: "\ecf9"; }
.ri-file-word-2-fill:before { content: "\ecfa"; }
.ri-file-word-2-line:before { content: "\ecfb"; }
.ri-file-word-fill:before { content: "\ecfc"; }
.ri-file-word-line:before { content: "\ecfd"; }
.ri-file-zip-fill:before { content: "\ecfe"; }
.ri-file-zip-line:before { content: "\ecff"; }
.ri-film-fill:before { content: "\ed00"; }
.ri-film-line:before { content: "\ed01"; }
.ri-filter-2-fill:before { content: "\ed02"; }
.ri-filter-2-line:before { content: "\ed03"; }
.ri-filter-3-fill:before { content: "\ed04"; }
.ri-filter-3-line:before { content: "\ed05"; }
.ri-filter-fill:before { content: "\ed06"; }
.ri-filter-line:before { content: "\ed07"; }
.ri-find-replace-fill:before { content: "\ed08"; }
.ri-find-replace-line:before { content: "\ed09"; }
.ri-fingerprint-2-fill:before { content: "\ed0a"; }
.ri-fingerprint-2-line:before { content: "\ed0b"; }
.ri-fingerprint-fill:before { content: "\ed0c"; }
.ri-fingerprint-line:before { content: "\ed0d"; }
.ri-fire-fill:before { content: "\ed0e"; }
.ri-fire-line:before { content: "\ed0f"; }
.ri-firefox-fill:before { content: "\ed10"; }
.ri-firefox-line:before { content: "\ed11"; }
.ri-flag-2-fill:before { content: "\ed12"; }
.ri-flag-2-line:before { content: "\ed13"; }
.ri-flag-fill:before { content: "\ed14"; }
.ri-flag-line:before { content: "\ed15"; }
.ri-flashlight-fill:before { content: "\ed16"; }
.ri-flashlight-line:before { content: "\ed17"; }
.ri-flask-fill:before { content: "\ed18"; }
.ri-flask-line:before { content: "\ed19"; }
.ri-flight-land-fill:before { content: "\ed1a"; }
.ri-flight-land-line:before { content: "\ed1b"; }
.ri-flight-takeoff-fill:before { content: "\ed1c"; }
.ri-flight-takeoff-line:before { content: "\ed1d"; }
.ri-flood-fill:before { content: "\ed1e"; }
.ri-flood-line:before { content: "\ed1f"; }
.ri-flutter-fill:before { content: "\ed20"; }
.ri-flutter-line:before { content: "\ed21"; }
.ri-focus-2-fill:before { content: "\ed22"; }
.ri-focus-2-line:before { content: "\ed23"; }
.ri-focus-3-fill:before { content: "\ed24"; }
.ri-focus-3-line:before { content: "\ed25"; }
.ri-focus-fill:before { content: "\ed26"; }
.ri-focus-line:before { content: "\ed27"; }
.ri-foggy-fill:before { content: "\ed28"; }
.ri-foggy-line:before { content: "\ed29"; }
.ri-folder-2-fill:before { content: "\ed2a"; }
.ri-folder-2-line:before { content: "\ed2b"; }
.ri-folder-3-fill:before { content: "\ed2c"; }
.ri-folder-3-line:before { content: "\ed2d"; }
.ri-folder-4-fill:before { content: "\ed2e"; }
.ri-folder-4-line:before { content: "\ed2f"; }
.ri-folder-5-fill:before { content: "\ed30"; }
.ri-folder-5-line:before { content: "\ed31"; }
.ri-folder-add-fill:before { content: "\ed32"; }
.ri-folder-add-line:before { content: "\ed33"; }
.ri-folder-chart-2-fill:before { content: "\ed34"; }
.ri-folder-chart-2-line:before { content: "\ed35"; }
.ri-folder-chart-fill:before { content: "\ed36"; }
.ri-folder-chart-line:before { content: "\ed37"; }
.ri-folder-download-fill:before { content: "\ed38"; }
.ri-folder-download-line:before { content: "\ed39"; }
.ri-folder-fill:before { content: "\ed3a"; }
.ri-folder-forbid-fill:before { content: "\ed3b"; }
.ri-folder-forbid-line:before { content: "\ed3c"; }
.ri-folder-history-fill:before { content: "\ed3d"; }
.ri-folder-history-line:before { content: "\ed3e"; }
.ri-folder-info-fill:before { content: "\ed3f"; }
.ri-folder-info-line:before { content: "\ed40"; }
.ri-folder-keyhole-fill:before { content: "\ed41"; }
.ri-folder-keyhole-line:before { content: "\ed42"; }
.ri-folder-line:before { content: "\ed43"; }
.ri-folder-lock-fill:before { content: "\ed44"; }
.ri-folder-lock-line:before { content: "\ed45"; }
.ri-folder-music-fill:before { content: "\ed46"; }
.ri-folder-music-line:before { content: "\ed47"; }
.ri-folder-open-fill:before { content: "\ed48"; }
.ri-folder-open-line:before { content: "\ed49"; }
.ri-folder-received-fill:before { content: "\ed4a"; }
.ri-folder-received-line:before { content: "\ed4b"; }
.ri-folder-reduce-fill:before { content: "\ed4c"; }
.ri-folder-reduce-line:before { content: "\ed4d"; }
.ri-folder-settings-fill:before { content: "\ed4e"; }
.ri-folder-settings-line:before { content: "\ed4f"; }
.ri-folder-shared-fill:before { content: "\ed50"; }
.ri-folder-shared-line:before { content: "\ed51"; }
.ri-folder-shield-2-fill:before { content: "\ed52"; }
.ri-folder-shield-2-line:before { content: "\ed53"; }
.ri-folder-shield-fill:before { content: "\ed54"; }
.ri-folder-shield-line:before { content: "\ed55"; }
.ri-folder-transfer-fill:before { content: "\ed56"; }
.ri-folder-transfer-line:before { content: "\ed57"; }
.ri-folder-unknow-fill:before { content: "\ed58"; }
.ri-folder-unknow-line:before { content: "\ed59"; }
.ri-folder-upload-fill:before { content: "\ed5a"; }
.ri-folder-upload-line:before { content: "\ed5b"; }
.ri-folder-user-fill:before { content: "\ed5c"; }
.ri-folder-user-line:before { content: "\ed5d"; }
.ri-folder-warning-fill:before { content: "\ed5e"; }
.ri-folder-warning-line:before { content: "\ed5f"; }
.ri-folder-zip-fill:before { content: "\ed60"; }
.ri-folder-zip-line:before { content: "\ed61"; }
.ri-folders-fill:before { content: "\ed62"; }
.ri-folders-line:before { content: "\ed63"; }
.ri-font-color:before { content: "\ed64"; }
.ri-font-size-2:before { content: "\ed65"; }
.ri-font-size:before { content: "\ed66"; }
.ri-football-fill:before { content: "\ed67"; }
.ri-football-line:before { content: "\ed68"; }
.ri-footprint-fill:before { content: "\ed69"; }
.ri-footprint-line:before { content: "\ed6a"; }
.ri-forbid-2-fill:before { content: "\ed6b"; }
.ri-forbid-2-line:before { content: "\ed6c"; }
.ri-forbid-fill:before { content: "\ed6d"; }
.ri-forbid-line:before { content: "\ed6e"; }
.ri-format-clear:before { content: "\ed6f"; }
.ri-fridge-fill:before { content: "\ed70"; }
.ri-fridge-line:before { content: "\ed71"; }
.ri-fullscreen-exit-fill:before { content: "\ed72"; }
.ri-fullscreen-exit-line:before { content: "\ed73"; }
.ri-fullscreen-fill:before { content: "\ed74"; }
.ri-fullscreen-line:before { content: "\ed75"; }
.ri-function-fill:before { content: "\ed76"; }
.ri-function-line:before { content: "\ed77"; }
.ri-functions:before { content: "\ed78"; }
.ri-funds-box-fill:before { content: "\ed79"; }
.ri-funds-box-line:before { content: "\ed7a"; }
.ri-funds-fill:before { content: "\ed7b"; }
.ri-funds-line:before { content: "\ed7c"; }
.ri-gallery-fill:before { content: "\ed7d"; }
.ri-gallery-line:before { content: "\ed7e"; }
.ri-gallery-upload-fill:before { content: "\ed7f"; }
.ri-gallery-upload-line:before { content: "\ed80"; }
.ri-game-fill:before { content: "\ed81"; }
.ri-game-line:before { content: "\ed82"; }
.ri-gamepad-fill:before { content: "\ed83"; }
.ri-gamepad-line:before { content: "\ed84"; }
.ri-gas-station-fill:before { content: "\ed85"; }
.ri-gas-station-line:before { content: "\ed86"; }
.ri-gatsby-fill:before { content: "\ed87"; }
.ri-gatsby-line:before { content: "\ed88"; }
.ri-genderless-fill:before { content: "\ed89"; }
.ri-genderless-line:before { content: "\ed8a"; }
.ri-ghost-2-fill:before { content: "\ed8b"; }
.ri-ghost-2-line:before { content: "\ed8c"; }
.ri-ghost-fill:before { content: "\ed8d"; }
.ri-ghost-line:before { content: "\ed8e"; }
.ri-ghost-smile-fill:before { content: "\ed8f"; }
.ri-ghost-smile-line:before { content: "\ed90"; }
.ri-gift-2-fill:before { content: "\ed91"; }
.ri-gift-2-line:before { content: "\ed92"; }
.ri-gift-fill:before { content: "\ed93"; }
.ri-gift-line:before { content: "\ed94"; }
.ri-git-branch-fill:before { content: "\ed95"; }
.ri-git-branch-line:before { content: "\ed96"; }
.ri-git-commit-fill:before { content: "\ed97"; }
.ri-git-commit-line:before { content: "\ed98"; }
.ri-git-merge-fill:before { content: "\ed99"; }
.ri-git-merge-line:before { content: "\ed9a"; }
.ri-git-pull-request-fill:before { content: "\ed9b"; }
.ri-git-pull-request-line:before { content: "\ed9c"; }
.ri-git-repository-commits-fill:before { content: "\ed9d"; }
.ri-git-repository-commits-line:before { content: "\ed9e"; }
.ri-git-repository-fill:before { content: "\ed9f"; }
.ri-git-repository-line:before { content: "\eda0"; }
.ri-git-repository-private-fill:before { content: "\eda1"; }
.ri-git-repository-private-line:before { content: "\eda2"; }
.ri-github-fill:before { content: "\eda3"; }
.ri-github-line:before { content: "\eda4"; }
.ri-gitlab-fill:before { content: "\eda5"; }
.ri-gitlab-line:before { content: "\eda6"; }
.ri-global-fill:before { content: "\eda7"; }
.ri-global-line:before { content: "\eda8"; }
.ri-globe-fill:before { content: "\eda9"; }
.ri-globe-line:before { content: "\edaa"; }
.ri-goblet-fill:before { content: "\edab"; }
.ri-goblet-line:before { content: "\edac"; }
.ri-google-fill:before { content: "\edad"; }
.ri-google-line:before { content: "\edae"; }
.ri-google-play-fill:before { content: "\edaf"; }
.ri-google-play-line:before { content: "\edb0"; }
.ri-government-fill:before { content: "\edb1"; }
.ri-government-line:before { content: "\edb2"; }
.ri-gps-fill:before { content: "\edb3"; }
.ri-gps-line:before { content: "\edb4"; }
.ri-gradienter-fill:before { content: "\edb5"; }
.ri-gradienter-line:before { content: "\edb6"; }
.ri-grid-fill:before { content: "\edb7"; }
.ri-grid-line:before { content: "\edb8"; }
.ri-group-2-fill:before { content: "\edb9"; }
.ri-group-2-line:before { content: "\edba"; }
.ri-group-fill:before { content: "\edbb"; }
.ri-group-line:before { content: "\edbc"; }
.ri-guide-fill:before { content: "\edbd"; }
.ri-guide-line:before { content: "\edbe"; }
.ri-hail-fill:before { content: "\edbf"; }
.ri-hail-line:before { content: "\edc0"; }
.ri-hammer-fill:before { content: "\edc1"; }
.ri-hammer-line:before { content: "\edc2"; }
.ri-hand-coin-fill:before { content: "\edc3"; }
.ri-hand-coin-line:before { content: "\edc4"; }
.ri-hand-heart-fill:before { content: "\edc5"; }
.ri-hand-heart-line:before { content: "\edc6"; }
.ri-handbag-fill:before { content: "\edc7"; }
.ri-handbag-line:before { content: "\edc8"; }
.ri-hard-drive-2-fill:before { content: "\edc9"; }
.ri-hard-drive-2-line:before { content: "\edca"; }
.ri-hard-drive-fill:before { content: "\edcb"; }
.ri-hard-drive-line:before { content: "\edcc"; }
.ri-hashtag:before { content: "\edcd"; }
.ri-haze-2-fill:before { content: "\edce"; }
.ri-haze-2-line:before { content: "\edcf"; }
.ri-haze-fill:before { content: "\edd0"; }
.ri-haze-line:before { content: "\edd1"; }
.ri-hd-fill:before { content: "\edd2"; }
.ri-hd-line:before { content: "\edd3"; }
.ri-heading:before { content: "\edd4"; }
.ri-headphone-fill:before { content: "\edd5"; }
.ri-headphone-line:before { content: "\edd6"; }
.ri-heart-2-fill:before { content: "\edd7"; }
.ri-heart-2-line:before { content: "\edd8"; }
.ri-heart-add-fill:before { content: "\edd9"; }
.ri-heart-add-line:before { content: "\edda"; }
.ri-heart-fill:before { content: "\eddb"; }
.ri-heart-line:before { content: "\eddc"; }
.ri-hearts-fill:before { content: "\eddd"; }
.ri-hearts-line:before { content: "\edde"; }
.ri-heavy-showers-fill:before { content: "\eddf"; }
.ri-heavy-showers-line:before { content: "\ede0"; }
.ri-history-fill:before { content: "\ede1"; }
.ri-history-line:before { content: "\ede2"; }
.ri-home-2-fill:before { content: "\ede3"; }
.ri-home-2-line:before { content: "\ede4"; }
.ri-home-3-fill:before { content: "\ede5"; }
.ri-home-3-line:before { content: "\ede6"; }
.ri-home-4-fill:before { content: "\ede7"; }
.ri-home-4-line:before { content: "\ede8"; }
.ri-home-5-fill:before { content: "\ede9"; }
.ri-home-5-line:before { content: "\edea"; }
.ri-home-6-fill:before { content: "\edeb"; }
.ri-home-6-line:before { content: "\edec"; }
.ri-home-7-fill:before { content: "\eded"; }
.ri-home-7-line:before { content: "\edee"; }
.ri-home-8-fill:before { content: "\edef"; }
.ri-home-8-line:before { content: "\edf0"; }
.ri-home-fill:before { content: "\edf1"; }
.ri-home-gear-fill:before { content: "\edf2"; }
.ri-home-gear-line:before { content: "\edf3"; }
.ri-home-heart-fill:before { content: "\edf4"; }
.ri-home-heart-line:before { content: "\edf5"; }
.ri-home-line:before { content: "\edf6"; }
.ri-home-smile-2-fill:before { content: "\edf7"; }
.ri-home-smile-2-line:before { content: "\edf8"; }
.ri-home-smile-fill:before { content: "\edf9"; }
.ri-home-smile-line:before { content: "\edfa"; }
.ri-home-wifi-fill:before { content: "\edfb"; }
.ri-home-wifi-line:before { content: "\edfc"; }
.ri-honor-of-kings-fill:before { content: "\edfd"; }
.ri-honor-of-kings-line:before { content: "\edfe"; }
.ri-honour-fill:before { content: "\edff"; }
.ri-honour-line:before { content: "\ee00"; }
.ri-hospital-fill:before { content: "\ee01"; }
.ri-hospital-line:before { content: "\ee02"; }
.ri-hotel-bed-fill:before { content: "\ee03"; }
.ri-hotel-bed-line:before { content: "\ee04"; }
.ri-hotel-fill:before { content: "\ee05"; }
.ri-hotel-line:before { content: "\ee06"; }
.ri-hotspot-fill:before { content: "\ee07"; }
.ri-hotspot-line:before { content: "\ee08"; }
.ri-hq-fill:before { content: "\ee09"; }
.ri-hq-line:before { content: "\ee0a"; }
.ri-html5-fill:before { content: "\ee0b"; }
.ri-html5-line:before { content: "\ee0c"; }
.ri-ie-fill:before { content: "\ee0d"; }
.ri-ie-line:before { content: "\ee0e"; }
.ri-image-2-fill:before { content: "\ee0f"; }
.ri-image-2-line:before { content: "\ee10"; }
.ri-image-add-fill:before { content: "\ee11"; }
.ri-image-add-line:before { content: "\ee12"; }
.ri-image-fill:before { content: "\ee13"; }
.ri-image-line:before { content: "\ee14"; }
.ri-inbox-archive-fill:before { content: "\ee15"; }
.ri-inbox-archive-line:before { content: "\ee16"; }
.ri-inbox-fill:before { content: "\ee17"; }
.ri-inbox-line:before { content: "\ee18"; }
.ri-inbox-unarchive-fill:before { content: "\ee19"; }
.ri-inbox-unarchive-line:before { content: "\ee1a"; }
.ri-increase-decrease-fill:before { content: "\ee1b"; }
.ri-increase-decrease-line:before { content: "\ee1c"; }
.ri-indent-decrease:before { content: "\ee1d"; }
.ri-indent-increase:before { content: "\ee1e"; }
.ri-indeterminate-circle-fill:before { content: "\ee1f"; }
.ri-indeterminate-circle-line:before { content: "\ee20"; }
.ri-information-fill:before { content: "\ee21"; }
.ri-information-line:before { content: "\ee22"; }
.ri-input-cursor-move:before { content: "\ee23"; }
.ri-input-method-fill:before { content: "\ee24"; }
.ri-input-method-line:before { content: "\ee25"; }
.ri-instagram-fill:before { content: "\ee26"; }
.ri-instagram-line:before { content: "\ee27"; }
.ri-install-fill:before { content: "\ee28"; }
.ri-install-line:before { content: "\ee29"; }
.ri-invision-fill:before { content: "\ee2a"; }
.ri-invision-line:before { content: "\ee2b"; }
.ri-italic:before { content: "\ee2c"; }
.ri-kakao-talk-fill:before { content: "\ee2d"; }
.ri-kakao-talk-line:before { content: "\ee2e"; }
.ri-key-2-fill:before { content: "\ee2f"; }
.ri-key-2-line:before { content: "\ee30"; }
.ri-key-fill:before { content: "\ee31"; }
.ri-key-line:before { content: "\ee32"; }
.ri-keyboard-box-fill:before { content: "\ee33"; }
.ri-keyboard-box-line:before { content: "\ee34"; }
.ri-keyboard-fill:before { content: "\ee35"; }
.ri-keyboard-line:before { content: "\ee36"; }
.ri-keynote-fill:before { content: "\ee37"; }
.ri-keynote-line:before { content: "\ee38"; }
.ri-knife-blood-fill:before { content: "\ee39"; }
.ri-knife-blood-line:before { content: "\ee3a"; }
.ri-knife-fill:before { content: "\ee3b"; }
.ri-knife-line:before { content: "\ee3c"; }
.ri-landscape-fill:before { content: "\ee3d"; }
.ri-landscape-line:before { content: "\ee3e"; }
.ri-layout-2-fill:before { content: "\ee3f"; }
.ri-layout-2-line:before { content: "\ee40"; }
.ri-layout-3-fill:before { content: "\ee41"; }
.ri-layout-3-line:before { content: "\ee42"; }
.ri-layout-4-fill:before { content: "\ee43"; }
.ri-layout-4-line:before { content: "\ee44"; }
.ri-layout-5-fill:before { content: "\ee45"; }
.ri-layout-5-line:before { content: "\ee46"; }
.ri-layout-6-fill:before { content: "\ee47"; }
.ri-layout-6-line:before { content: "\ee48"; }
.ri-layout-bottom-2-fill:before { content: "\ee49"; }
.ri-layout-bottom-2-line:before { content: "\ee4a"; }
.ri-layout-bottom-fill:before { content: "\ee4b"; }
.ri-layout-bottom-line:before { content: "\ee4c"; }
.ri-layout-column-fill:before { content: "\ee4d"; }
.ri-layout-column-line:before { content: "\ee4e"; }
.ri-layout-fill:before { content: "\ee4f"; }
.ri-layout-grid-fill:before { content: "\ee50"; }
.ri-layout-grid-line:before { content: "\ee51"; }
.ri-layout-left-2-fill:before { content: "\ee52"; }
.ri-layout-left-2-line:before { content: "\ee53"; }
.ri-layout-left-fill:before { content: "\ee54"; }
.ri-layout-left-line:before { content: "\ee55"; }
.ri-layout-line:before { content: "\ee56"; }
.ri-layout-masonry-fill:before { content: "\ee57"; }
.ri-layout-masonry-line:before { content: "\ee58"; }
.ri-layout-right-2-fill:before { content: "\ee59"; }
.ri-layout-right-2-line:before { content: "\ee5a"; }
.ri-layout-right-fill:before { content: "\ee5b"; }
.ri-layout-right-line:before { content: "\ee5c"; }
.ri-layout-row-fill:before { content: "\ee5d"; }
.ri-layout-row-line:before { content: "\ee5e"; }
.ri-layout-top-2-fill:before { content: "\ee5f"; }
.ri-layout-top-2-line:before { content: "\ee60"; }
.ri-layout-top-fill:before { content: "\ee61"; }
.ri-layout-top-line:before { content: "\ee62"; }
.ri-leaf-fill:before { content: "\ee63"; }
.ri-leaf-line:before { content: "\ee64"; }
.ri-lifebuoy-fill:before { content: "\ee65"; }
.ri-lifebuoy-line:before { content: "\ee66"; }
.ri-lightbulb-fill:before { content: "\ee67"; }
.ri-lightbulb-flash-fill:before { content: "\ee68"; }
.ri-lightbulb-flash-line:before { content: "\ee69"; }
.ri-lightbulb-line:before { content: "\ee6a"; }
.ri-line-chart-fill:before { content: "\ee6b"; }
.ri-line-chart-line:before { content: "\ee6c"; }
.ri-line-fill:before { content: "\ee6d"; }
.ri-line-height:before { content: "\ee6e"; }
.ri-line-line:before { content: "\ee6f"; }
.ri-link-m:before { content: "\ee70"; }
.ri-link-unlink-m:before { content: "\ee71"; }
.ri-link-unlink:before { content: "\ee72"; }
.ri-link:before { content: "\ee73"; }
.ri-linkedin-box-fill:before { content: "\ee74"; }
.ri-linkedin-box-line:before { content: "\ee75"; }
.ri-linkedin-fill:before { content: "\ee76"; }
.ri-linkedin-line:before { content: "\ee77"; }
.ri-links-fill:before { content: "\ee78"; }
.ri-links-line:before { content: "\ee79"; }
.ri-list-check-2:before { content: "\ee7a"; }
.ri-list-check:before { content: "\ee7b"; }
.ri-list-ordered:before { content: "\ee7c"; }
.ri-list-settings-fill:before { content: "\ee7d"; }
.ri-list-settings-line:before { content: "\ee7e"; }
.ri-list-unordered:before { content: "\ee7f"; }
.ri-live-fill:before { content: "\ee80"; }
.ri-live-line:before { content: "\ee81"; }
.ri-loader-2-fill:before { content: "\ee82"; }
.ri-loader-2-line:before { content: "\ee83"; }
.ri-loader-3-fill:before { content: "\ee84"; }
.ri-loader-3-line:before { content: "\ee85"; }
.ri-loader-4-fill:before { content: "\ee86"; }
.ri-loader-4-line:before { content: "\ee87"; }
.ri-loader-5-fill:before { content: "\ee88"; }
.ri-loader-5-line:before { content: "\ee89"; }
.ri-loader-fill:before { content: "\ee8a"; }
.ri-loader-line:before { content: "\ee8b"; }
.ri-lock-2-fill:before { content: "\ee8c"; }
.ri-lock-2-line:before { content: "\ee8d"; }
.ri-lock-fill:before { content: "\ee8e"; }
.ri-lock-line:before { content: "\ee8f"; }
.ri-lock-password-fill:before { content: "\ee90"; }
.ri-lock-password-line:before { content: "\ee91"; }
.ri-lock-unlock-fill:before { content: "\ee92"; }
.ri-lock-unlock-line:before { content: "\ee93"; }
.ri-login-box-fill:before { content: "\ee94"; }
.ri-login-box-line:before { content: "\ee95"; }
.ri-login-circle-fill:before { content: "\ee96"; }
.ri-login-circle-line:before { content: "\ee97"; }
.ri-logout-box-fill:before { content: "\ee98"; }
.ri-logout-box-line:before { content: "\ee99"; }
.ri-logout-box-r-fill:before { content: "\ee9a"; }
.ri-logout-box-r-line:before { content: "\ee9b"; }
.ri-logout-circle-fill:before { content: "\ee9c"; }
.ri-logout-circle-line:before { content: "\ee9d"; }
.ri-logout-circle-r-fill:before { content: "\ee9e"; }
.ri-logout-circle-r-line:before { content: "\ee9f"; }
.ri-luggage-cart-fill:before { content: "\eea0"; }
.ri-luggage-cart-line:before { content: "\eea1"; }
.ri-luggage-deposit-fill:before { content: "\eea2"; }
.ri-luggage-deposit-line:before { content: "\eea3"; }
.ri-mac-fill:before { content: "\eea4"; }
.ri-mac-line:before { content: "\eea5"; }
.ri-macbook-fill:before { content: "\eea6"; }
.ri-macbook-line:before { content: "\eea7"; }
.ri-magic-fill:before { content: "\eea8"; }
.ri-magic-line:before { content: "\eea9"; }
.ri-mail-add-fill:before { content: "\eeaa"; }
.ri-mail-add-line:before { content: "\eeab"; }
.ri-mail-check-fill:before { content: "\eeac"; }
.ri-mail-check-line:before { content: "\eead"; }
.ri-mail-close-fill:before { content: "\eeae"; }
.ri-mail-close-line:before { content: "\eeaf"; }
.ri-mail-download-fill:before { content: "\eeb0"; }
.ri-mail-download-line:before { content: "\eeb1"; }
.ri-mail-fill:before { content: "\eeb2"; }
.ri-mail-forbid-fill:before { content: "\eeb3"; }
.ri-mail-forbid-line:before { content: "\eeb4"; }
.ri-mail-line:before { content: "\eeb5"; }
.ri-mail-lock-fill:before { content: "\eeb6"; }
.ri-mail-lock-line:before { content: "\eeb7"; }
.ri-mail-open-fill:before { content: "\eeb8"; }
.ri-mail-open-line:before { content: "\eeb9"; }
.ri-mail-send-fill:before { content: "\eeba"; }
.ri-mail-send-line:before { content: "\eebb"; }
.ri-mail-settings-fill:before { content: "\eebc"; }
.ri-mail-settings-line:before { content: "\eebd"; }
.ri-mail-star-fill:before { content: "\eebe"; }
.ri-mail-star-line:before { content: "\eebf"; }
.ri-mail-unread-fill:before { content: "\eec0"; }
.ri-mail-unread-line:before { content: "\eec1"; }
.ri-mail-volume-fill:before { content: "\eec2"; }
.ri-mail-volume-line:before { content: "\eec3"; }
.ri-map-2-fill:before { content: "\eec4"; }
.ri-map-2-line:before { content: "\eec5"; }
.ri-map-fill:before { content: "\eec6"; }
.ri-map-line:before { content: "\eec7"; }
.ri-map-pin-2-fill:before { content: "\eec8"; }
.ri-map-pin-2-line:before { content: "\eec9"; }
.ri-map-pin-3-fill:before { content: "\eeca"; }
.ri-map-pin-3-line:before { content: "\eecb"; }
.ri-map-pin-4-fill:before { content: "\eecc"; }
.ri-map-pin-4-line:before { content: "\eecd"; }
.ri-map-pin-5-fill:before { content: "\eece"; }
.ri-map-pin-5-line:before { content: "\eecf"; }
.ri-map-pin-add-fill:before { content: "\eed0"; }
.ri-map-pin-add-line:before { content: "\eed1"; }
.ri-map-pin-fill:before { content: "\eed2"; }
.ri-map-pin-line:before { content: "\eed3"; }
.ri-map-pin-range-fill:before { content: "\eed4"; }
.ri-map-pin-range-line:before { content: "\eed5"; }
.ri-map-pin-time-fill:before { content: "\eed6"; }
.ri-map-pin-time-line:before { content: "\eed7"; }
.ri-map-pin-user-fill:before { content: "\eed8"; }
.ri-map-pin-user-line:before { content: "\eed9"; }
.ri-mark-pen-fill:before { content: "\eeda"; }
.ri-mark-pen-line:before { content: "\eedb"; }
.ri-markdown-fill:before { content: "\eedc"; }
.ri-markdown-line:before { content: "\eedd"; }
.ri-markup-fill:before { content: "\eede"; }
.ri-markup-line:before { content: "\eedf"; }
.ri-mastercard-fill:before { content: "\eee0"; }
.ri-mastercard-line:before { content: "\eee1"; }
.ri-mastodon-fill:before { content: "\eee2"; }
.ri-mastodon-line:before { content: "\eee3"; }
.ri-medal-2-fill:before { content: "\eee4"; }
.ri-medal-2-line:before { content: "\eee5"; }
.ri-medal-fill:before { content: "\eee6"; }
.ri-medal-line:before { content: "\eee7"; }
.ri-medium-fill:before { content: "\eee8"; }
.ri-medium-line:before { content: "\eee9"; }
.ri-men-fill:before { content: "\eeea"; }
.ri-men-line:before { content: "\eeeb"; }
.ri-menu-2-fill:before { content: "\eeec"; }
.ri-menu-2-line:before { content: "\eeed"; }
.ri-menu-3-fill:before { content: "\eeee"; }
.ri-menu-3-line:before { content: "\eeef"; }
.ri-menu-4-fill:before { content: "\eef0"; }
.ri-menu-4-line:before { content: "\eef1"; }
.ri-menu-5-fill:before { content: "\eef2"; }
.ri-menu-5-line:before { content: "\eef3"; }
.ri-menu-add-fill:before { content: "\eef4"; }
.ri-menu-add-line:before { content: "\eef5"; }
.ri-menu-fill:before { content: "\eef6"; }
.ri-menu-line:before { content: "\eef7"; }
.ri-message-2-fill:before { content: "\eef8"; }
.ri-message-2-line:before { content: "\eef9"; }
.ri-message-3-fill:before { content: "\eefa"; }
.ri-message-3-line:before { content: "\eefb"; }
.ri-message-fill:before { content: "\eefc"; }
.ri-message-line:before { content: "\eefd"; }
.ri-messenger-fill:before { content: "\eefe"; }
.ri-messenger-line:before { content: "\eeff"; }
.ri-meteor-fill:before { content: "\ef00"; }
.ri-meteor-line:before { content: "\ef01"; }
.ri-mic-2-fill:before { content: "\ef02"; }
.ri-mic-2-line:before { content: "\ef03"; }
.ri-mic-fill:before { content: "\ef04"; }
.ri-mic-line:before { content: "\ef05"; }
.ri-mic-off-fill:before { content: "\ef06"; }
.ri-mic-off-line:before { content: "\ef07"; }
.ri-mickey-fill:before { content: "\ef08"; }
.ri-mickey-line:before { content: "\ef09"; }
.ri-mini-program-fill:before { content: "\ef0a"; }
.ri-mini-program-line:before { content: "\ef0b"; }
.ri-mist-fill:before { content: "\ef0c"; }
.ri-mist-line:before { content: "\ef0d"; }
.ri-money-cny-box-fill:before { content: "\ef0e"; }
.ri-money-cny-box-line:before { content: "\ef0f"; }
.ri-money-cny-circle-fill:before { content: "\ef10"; }
.ri-money-cny-circle-line:before { content: "\ef11"; }
.ri-money-dollar-box-fill:before { content: "\ef12"; }
.ri-money-dollar-box-line:before { content: "\ef13"; }
.ri-money-dollar-circle-fill:before { content: "\ef14"; }
.ri-money-dollar-circle-line:before { content: "\ef15"; }
.ri-money-euro-box-fill:before { content: "\ef16"; }
.ri-money-euro-box-line:before { content: "\ef17"; }
.ri-money-euro-circle-fill:before { content: "\ef18"; }
.ri-money-euro-circle-line:before { content: "\ef19"; }
.ri-money-pound-box-fill:before { content: "\ef1a"; }
.ri-money-pound-box-line:before { content: "\ef1b"; }
.ri-money-pound-circle-fill:before { content: "\ef1c"; }
.ri-money-pound-circle-line:before { content: "\ef1d"; }
.ri-moon-clear-fill:before { content: "\ef1e"; }
.ri-moon-clear-line:before { content: "\ef1f"; }
.ri-moon-cloudy-fill:before { content: "\ef20"; }
.ri-moon-cloudy-line:before { content: "\ef21"; }
.ri-moon-fill:before { content: "\ef22"; }
.ri-moon-foggy-fill:before { content: "\ef23"; }
.ri-moon-foggy-line:before { content: "\ef24"; }
.ri-moon-line:before { content: "\ef25"; }
.ri-more-2-fill:before { content: "\ef26"; }
.ri-more-2-line:before { content: "\ef27"; }
.ri-more-fill:before { content: "\ef28"; }
.ri-more-line:before { content: "\ef29"; }
.ri-motorbike-fill:before { content: "\ef2a"; }
.ri-motorbike-line:before { content: "\ef2b"; }
.ri-mouse-fill:before { content: "\ef2c"; }
.ri-mouse-line:before { content: "\ef2d"; }
.ri-movie-2-fill:before { content: "\ef2e"; }
.ri-movie-2-line:before { content: "\ef2f"; }
.ri-movie-fill:before { content: "\ef30"; }
.ri-movie-line:before { content: "\ef31"; }
.ri-music-2-fill:before { content: "\ef32"; }
.ri-music-2-line:before { content: "\ef33"; }
.ri-music-fill:before { content: "\ef34"; }
.ri-music-line:before { content: "\ef35"; }
.ri-mv-fill:before { content: "\ef36"; }
.ri-mv-line:before { content: "\ef37"; }
.ri-navigation-fill:before { content: "\ef38"; }
.ri-navigation-line:before { content: "\ef39"; }
.ri-netease-cloud-music-fill:before { content: "\ef3a"; }
.ri-netease-cloud-music-line:before { content: "\ef3b"; }
.ri-netflix-fill:before { content: "\ef3c"; }
.ri-netflix-line:before { content: "\ef3d"; }
.ri-newspaper-fill:before { content: "\ef3e"; }
.ri-newspaper-line:before { content: "\ef3f"; }
.ri-notification-2-fill:before { content: "\ef40"; }
.ri-notification-2-line:before { content: "\ef41"; }
.ri-notification-3-fill:before { content: "\ef42"; }
.ri-notification-3-line:before { content: "\ef43"; }
.ri-notification-4-fill:before { content: "\ef44"; }
.ri-notification-4-line:before { content: "\ef45"; }
.ri-notification-badge-fill:before { content: "\ef46"; }
.ri-notification-badge-line:before { content: "\ef47"; }
.ri-notification-fill:before { content: "\ef48"; }
.ri-notification-line:before { content: "\ef49"; }
.ri-notification-off-fill:before { content: "\ef4a"; }
.ri-notification-off-line:before { content: "\ef4b"; }
.ri-npmjs-fill:before { content: "\ef4c"; }
.ri-npmjs-line:before { content: "\ef4d"; }
.ri-number-0:before { content: "\ef4e"; }
.ri-number-1:before { content: "\ef4f"; }
.ri-number-2:before { content: "\ef50"; }
.ri-number-3:before { content: "\ef51"; }
.ri-number-4:before { content: "\ef52"; }
.ri-number-5:before { content: "\ef53"; }
.ri-number-6:before { content: "\ef54"; }
.ri-number-7:before { content: "\ef55"; }
.ri-number-8:before { content: "\ef56"; }
.ri-number-9:before { content: "\ef57"; }
.ri-numbers-fill:before { content: "\ef58"; }
.ri-numbers-line:before { content: "\ef59"; }
.ri-oil-fill:before { content: "\ef5a"; }
.ri-oil-line:before { content: "\ef5b"; }
.ri-omega:before { content: "\ef5c"; }
.ri-open-arm-fill:before { content: "\ef5d"; }
.ri-open-arm-line:before { content: "\ef5e"; }
.ri-open-source-fill:before { content: "\ef5f"; }
.ri-open-source-line:before { content: "\ef60"; }
.ri-opera-fill:before { content: "\ef61"; }
.ri-opera-line:before { content: "\ef62"; }
.ri-order-play-fill:before { content: "\ef63"; }
.ri-order-play-line:before { content: "\ef64"; }
.ri-outlet-2-fill:before { content: "\ef65"; }
.ri-outlet-2-line:before { content: "\ef66"; }
.ri-outlet-fill:before { content: "\ef67"; }
.ri-outlet-line:before { content: "\ef68"; }
.ri-page-separator:before { content: "\ef69"; }
.ri-pages-fill:before { content: "\ef6a"; }
.ri-pages-line:before { content: "\ef6b"; }
.ri-paint-brush-fill:before { content: "\ef6c"; }
.ri-paint-brush-line:before { content: "\ef6d"; }
.ri-paint-fill:before { content: "\ef6e"; }
.ri-paint-line:before { content: "\ef6f"; }
.ri-palette-fill:before { content: "\ef70"; }
.ri-palette-line:before { content: "\ef71"; }
.ri-pantone-fill:before { content: "\ef72"; }
.ri-pantone-line:before { content: "\ef73"; }
.ri-paragraph:before { content: "\ef74"; }
.ri-parent-fill:before { content: "\ef75"; }
.ri-parent-line:before { content: "\ef76"; }
.ri-parentheses-fill:before { content: "\ef77"; }
.ri-parentheses-line:before { content: "\ef78"; }
.ri-parking-box-fill:before { content: "\ef79"; }
.ri-parking-box-line:before { content: "\ef7a"; }
.ri-parking-fill:before { content: "\ef7b"; }
.ri-parking-line:before { content: "\ef7c"; }
.ri-passport-fill:before { content: "\ef7d"; }
.ri-passport-line:before { content: "\ef7e"; }
.ri-patreon-fill:before { content: "\ef7f"; }
.ri-patreon-line:before { content: "\ef80"; }
.ri-pause-circle-fill:before { content: "\ef81"; }
.ri-pause-circle-line:before { content: "\ef82"; }
.ri-pause-fill:before { content: "\ef83"; }
.ri-pause-line:before { content: "\ef84"; }
.ri-pause-mini-fill:before { content: "\ef85"; }
.ri-pause-mini-line:before { content: "\ef86"; }
.ri-paypal-fill:before { content: "\ef87"; }
.ri-paypal-line:before { content: "\ef88"; }
.ri-pen-nib-fill:before { content: "\ef89"; }
.ri-pen-nib-line:before { content: "\ef8a"; }
.ri-pencil-fill:before { content: "\ef8b"; }
.ri-pencil-line:before { content: "\ef8c"; }
.ri-pencil-ruler-2-fill:before { content: "\ef8d"; }
.ri-pencil-ruler-2-line:before { content: "\ef8e"; }
.ri-pencil-ruler-fill:before { content: "\ef8f"; }
.ri-pencil-ruler-line:before { content: "\ef90"; }
.ri-percent-fill:before { content: "\ef91"; }
.ri-percent-line:before { content: "\ef92"; }
.ri-phone-camera-fill:before { content: "\ef93"; }
.ri-phone-camera-line:before { content: "\ef94"; }
.ri-phone-fill:before { content: "\ef95"; }
.ri-phone-find-fill:before { content: "\ef96"; }
.ri-phone-find-line:before { content: "\ef97"; }
.ri-phone-line:before { content: "\ef98"; }
.ri-phone-lock-fill:before { content: "\ef99"; }
.ri-phone-lock-line:before { content: "\ef9a"; }
.ri-picture-in-picture-2-fill:before { content: "\ef9b"; }
.ri-picture-in-picture-2-line:before { content: "\ef9c"; }
.ri-picture-in-picture-exit-fill:before { content: "\ef9d"; }
.ri-picture-in-picture-exit-line:before { content: "\ef9e"; }
.ri-picture-in-picture-fill:before { content: "\ef9f"; }
.ri-picture-in-picture-line:before { content: "\efa0"; }
.ri-pie-chart-2-fill:before { content: "\efa1"; }
.ri-pie-chart-2-line:before { content: "\efa2"; }
.ri-pie-chart-box-fill:before { content: "\efa3"; }
.ri-pie-chart-box-line:before { content: "\efa4"; }
.ri-pie-chart-fill:before { content: "\efa5"; }
.ri-pie-chart-line:before { content: "\efa6"; }
.ri-pin-distance-fill:before { content: "\efa7"; }
.ri-pin-distance-line:before { content: "\efa8"; }
.ri-ping-pong-fill:before { content: "\efa9"; }
.ri-ping-pong-line:before { content: "\efaa"; }
.ri-pinterest-fill:before { content: "\efab"; }
.ri-pinterest-line:before { content: "\efac"; }
.ri-pinyin-input:before { content: "\efad"; }
.ri-pixelfed-fill:before { content: "\efae"; }
.ri-pixelfed-line:before { content: "\efaf"; }
.ri-plane-fill:before { content: "\efb0"; }
.ri-plane-line:before { content: "\efb1"; }
.ri-plant-fill:before { content: "\efb2"; }
.ri-plant-line:before { content: "\efb3"; }
.ri-play-circle-fill:before { content: "\efb4"; }
.ri-play-circle-line:before { content: "\efb5"; }
.ri-play-fill:before { content: "\efb6"; }
.ri-play-line:before { content: "\efb7"; }
.ri-play-list-add-fill:before { content: "\efb8"; }
.ri-play-list-add-line:before { content: "\efb9"; }
.ri-play-list-fill:before { content: "\efba"; }
.ri-play-list-line:before { content: "\efbb"; }
.ri-play-mini-fill:before { content: "\efbc"; }
.ri-play-mini-line:before { content: "\efbd"; }
.ri-playstation-fill:before { content: "\efbe"; }
.ri-playstation-line:before { content: "\efbf"; }
.ri-plug-2-fill:before { content: "\efc0"; }
.ri-plug-2-line:before { content: "\efc1"; }
.ri-plug-fill:before { content: "\efc2"; }
.ri-plug-line:before { content: "\efc3"; }
.ri-polaroid-2-fill:before { content: "\efc4"; }
.ri-polaroid-2-line:before { content: "\efc5"; }
.ri-polaroid-fill:before { content: "\efc6"; }
.ri-polaroid-line:before { content: "\efc7"; }
.ri-police-car-fill:before { content: "\efc8"; }
.ri-police-car-line:before { content: "\efc9"; }
.ri-price-tag-2-fill:before { content: "\efca"; }
.ri-price-tag-2-line:before { content: "\efcb"; }
.ri-price-tag-3-fill:before { content: "\efcc"; }
.ri-price-tag-3-line:before { content: "\efcd"; }
.ri-price-tag-fill:before { content: "\efce"; }
.ri-price-tag-line:before { content: "\efcf"; }
.ri-printer-cloud-fill:before { content: "\efd0"; }
.ri-printer-cloud-line:before { content: "\efd1"; }
.ri-printer-fill:before { content: "\efd2"; }
.ri-printer-line:before { content: "\efd3"; }
.ri-product-hunt-fill:before { content: "\efd4"; }
.ri-product-hunt-line:before { content: "\efd5"; }
.ri-profile-fill:before { content: "\efd6"; }
.ri-profile-line:before { content: "\efd7"; }
.ri-projector-2-fill:before { content: "\efd8"; }
.ri-projector-2-line:before { content: "\efd9"; }
.ri-projector-fill:before { content: "\efda"; }
.ri-projector-line:before { content: "\efdb"; }
.ri-pushpin-2-fill:before { content: "\efdc"; }
.ri-pushpin-2-line:before { content: "\efdd"; }
.ri-pushpin-fill:before { content: "\efde"; }
.ri-pushpin-line:before { content: "\efdf"; }
.ri-qq-fill:before { content: "\efe0"; }
.ri-qq-line:before { content: "\efe1"; }
.ri-qr-code-fill:before { content: "\efe2"; }
.ri-qr-code-line:before { content: "\efe3"; }
.ri-qr-scan-2-fill:before { content: "\efe4"; }
.ri-qr-scan-2-line:before { content: "\efe5"; }
.ri-qr-scan-fill:before { content: "\efe6"; }
.ri-qr-scan-line:before { content: "\efe7"; }
.ri-question-answer-fill:before { content: "\efe8"; }
.ri-question-answer-line:before { content: "\efe9"; }
.ri-question-fill:before { content: "\efea"; }
.ri-question-line:before { content: "\efeb"; }
.ri-questionnaire-fill:before { content: "\efec"; }
.ri-questionnaire-line:before { content: "\efed"; }
.ri-quill-pen-fill:before { content: "\efee"; }
.ri-quill-pen-line:before { content: "\efef"; }
.ri-radar-fill:before { content: "\eff0"; }
.ri-radar-line:before { content: "\eff1"; }
.ri-radio-2-fill:before { content: "\eff2"; }
.ri-radio-2-line:before { content: "\eff3"; }
.ri-radio-button-fill:before { content: "\eff4"; }
.ri-radio-button-line:before { content: "\eff5"; }
.ri-radio-fill:before { content: "\eff6"; }
.ri-radio-line:before { content: "\eff7"; }
.ri-rainbow-fill:before { content: "\eff8"; }
.ri-rainbow-line:before { content: "\eff9"; }
.ri-rainy-fill:before { content: "\effa"; }
.ri-rainy-line:before { content: "\effb"; }
.ri-reactjs-fill:before { content: "\effc"; }
.ri-reactjs-line:before { content: "\effd"; }
.ri-record-circle-fill:before { content: "\effe"; }
.ri-record-circle-line:before { content: "\efff"; }
.ri-record-mail-fill:before { content: "\f000"; }
.ri-record-mail-line:before { content: "\f001"; }
.ri-recycle-fill:before { content: "\f002"; }
.ri-recycle-line:before { content: "\f003"; }
.ri-red-packet-fill:before { content: "\f004"; }
.ri-red-packet-line:before { content: "\f005"; }
.ri-reddit-fill:before { content: "\f006"; }
.ri-reddit-line:before { content: "\f007"; }
.ri-refresh-fill:before { content: "\f008"; }
.ri-refresh-line:before { content: "\f009"; }
.ri-refund-2-fill:before { content: "\f00a"; }
.ri-refund-2-line:before { content: "\f00b"; }
.ri-refund-fill:before { content: "\f00c"; }
.ri-refund-line:before { content: "\f00d"; }
.ri-registered-fill:before { content: "\f00e"; }
.ri-registered-line:before { content: "\f00f"; }
.ri-remixicon-fill:before { content: "\f010"; }
.ri-remixicon-line:before { content: "\f011"; }
.ri-remote-control-2-fill:before { content: "\f012"; }
.ri-remote-control-2-line:before { content: "\f013"; }
.ri-remote-control-fill:before { content: "\f014"; }
.ri-remote-control-line:before { content: "\f015"; }
.ri-repeat-2-fill:before { content: "\f016"; }
.ri-repeat-2-line:before { content: "\f017"; }
.ri-repeat-fill:before { content: "\f018"; }
.ri-repeat-line:before { content: "\f019"; }
.ri-repeat-one-fill:before { content: "\f01a"; }
.ri-repeat-one-line:before { content: "\f01b"; }
.ri-reply-fill:before { content: "\f01c"; }
.ri-reply-line:before { content: "\f01d"; }
.ri-reserved-fill:before { content: "\f01e"; }
.ri-reserved-line:before { content: "\f01f"; }
.ri-restart-fill:before { content: "\f020"; }
.ri-restart-line:before { content: "\f021"; }
.ri-restaurant-2-fill:before { content: "\f022"; }
.ri-restaurant-2-line:before { content: "\f023"; }
.ri-restaurant-fill:before { content: "\f024"; }
.ri-restaurant-line:before { content: "\f025"; }
.ri-rewind-fill:before { content: "\f026"; }
.ri-rewind-line:before { content: "\f027"; }
.ri-rewind-mini-fill:before { content: "\f028"; }
.ri-rewind-mini-line:before { content: "\f029"; }
.ri-rhythm-fill:before { content: "\f02a"; }
.ri-rhythm-line:before { content: "\f02b"; }
.ri-riding-fill:before { content: "\f02c"; }
.ri-riding-line:before { content: "\f02d"; }
.ri-road-map-fill:before { content: "\f02e"; }
.ri-road-map-line:before { content: "\f02f"; }
.ri-roadster-fill:before { content: "\f030"; }
.ri-roadster-line:before { content: "\f031"; }
.ri-robot-fill:before { content: "\f032"; }
.ri-robot-line:before { content: "\f033"; }
.ri-rocket-2-fill:before { content: "\f034"; }
.ri-rocket-2-line:before { content: "\f035"; }
.ri-rocket-fill:before { content: "\f036"; }
.ri-rocket-line:before { content: "\f037"; }
.ri-rotate-lock-fill:before { content: "\f038"; }
.ri-rotate-lock-line:before { content: "\f039"; }
.ri-route-fill:before { content: "\f03a"; }
.ri-route-line:before { content: "\f03b"; }
.ri-router-fill:before { content: "\f03c"; }
.ri-router-line:before { content: "\f03d"; }
.ri-rss-fill:before { content: "\f03e"; }
.ri-rss-line:before { content: "\f03f"; }
.ri-ruler-2-fill:before { content: "\f040"; }
.ri-ruler-2-line:before { content: "\f041"; }
.ri-ruler-fill:before { content: "\f042"; }
.ri-ruler-line:before { content: "\f043"; }
.ri-run-fill:before { content: "\f044"; }
.ri-run-line:before { content: "\f045"; }
.ri-safari-fill:before { content: "\f046"; }
.ri-safari-line:before { content: "\f047"; }
.ri-safe-2-fill:before { content: "\f048"; }
.ri-safe-2-line:before { content: "\f049"; }
.ri-safe-fill:before { content: "\f04a"; }
.ri-safe-line:before { content: "\f04b"; }
.ri-sailboat-fill:before { content: "\f04c"; }
.ri-sailboat-line:before { content: "\f04d"; }
.ri-save-2-fill:before { content: "\f04e"; }
.ri-save-2-line:before { content: "\f04f"; }
.ri-save-3-fill:before { content: "\f050"; }
.ri-save-3-line:before { content: "\f051"; }
.ri-save-fill:before { content: "\f052"; }
.ri-save-line:before { content: "\f053"; }
.ri-scales-2-fill:before { content: "\f054"; }
.ri-scales-2-line:before { content: "\f055"; }
.ri-scales-fill:before { content: "\f056"; }
.ri-scales-line:before { content: "\f057"; }
.ri-scan-2-fill:before { content: "\f058"; }
.ri-scan-2-line:before { content: "\f059"; }
.ri-scan-fill:before { content: "\f05a"; }
.ri-scan-line:before { content: "\f05b"; }
.ri-scissors-2-fill:before { content: "\f05c"; }
.ri-scissors-2-line:before { content: "\f05d"; }
.ri-scissors-cut-fill:before { content: "\f05e"; }
.ri-scissors-cut-line:before { content: "\f05f"; }
.ri-scissors-fill:before { content: "\f060"; }
.ri-scissors-line:before { content: "\f061"; }
.ri-screenshot-2-fill:before { content: "\f062"; }
.ri-screenshot-2-line:before { content: "\f063"; }
.ri-screenshot-fill:before { content: "\f064"; }
.ri-screenshot-line:before { content: "\f065"; }
.ri-sd-card-fill:before { content: "\f066"; }
.ri-sd-card-line:before { content: "\f067"; }
.ri-sd-card-mini-fill:before { content: "\f068"; }
.ri-sd-card-mini-line:before { content: "\f069"; }
.ri-search-2-fill:before { content: "\f06a"; }
.ri-search-2-line:before { content: "\f06b"; }
.ri-search-eye-fill:before { content: "\f06c"; }
.ri-search-eye-line:before { content: "\f06d"; }
.ri-search-fill:before { content: "\f06e"; }
.ri-search-line:before { content: "\f06f"; }
.ri-secure-payment-fill:before { content: "\f070"; }
.ri-secure-payment-line:before { content: "\f071"; }
.ri-send-backward:before { content: "\f072"; }
.ri-send-plane-2-fill:before { content: "\f073"; }
.ri-send-plane-2-line:before { content: "\f074"; }
.ri-send-plane-fill:before { content: "\f075"; }
.ri-send-plane-line:before { content: "\f076"; }
.ri-send-to-back:before { content: "\f077"; }
.ri-sensor-fill:before { content: "\f078"; }
.ri-sensor-line:before { content: "\f079"; }
.ri-separator:before { content: "\f07a"; }
.ri-server-fill:before { content: "\f07b"; }
.ri-server-line:before { content: "\f07c"; }
.ri-service-fill:before { content: "\f07d"; }
.ri-service-line:before { content: "\f07e"; }
.ri-settings-2-fill:before { content: "\f07f"; }
.ri-settings-2-line:before { content: "\f080"; }
.ri-settings-3-fill:before { content: "\f081"; }
.ri-settings-3-line:before { content: "\f082"; }
.ri-settings-4-fill:before { content: "\f083"; }
.ri-settings-4-line:before { content: "\f084"; }
.ri-settings-5-fill:before { content: "\f085"; }
.ri-settings-5-line:before { content: "\f086"; }
.ri-settings-6-fill:before { content: "\f087"; }
.ri-settings-6-line:before { content: "\f088"; }
.ri-settings-fill:before { content: "\f089"; }
.ri-settings-line:before { content: "\f08a"; }
.ri-shape-2-fill:before { content: "\f08b"; }
.ri-shape-2-line:before { content: "\f08c"; }
.ri-shape-fill:before { content: "\f08d"; }
.ri-shape-line:before { content: "\f08e"; }
.ri-share-box-fill:before { content: "\f08f"; }
.ri-share-box-line:before { content: "\f090"; }
.ri-share-circle-fill:before { content: "\f091"; }
.ri-share-circle-line:before { content: "\f092"; }
.ri-share-fill:before { content: "\f093"; }
.ri-share-forward-2-fill:before { content: "\f094"; }
.ri-share-forward-2-line:before { content: "\f095"; }
.ri-share-forward-box-fill:before { content: "\f096"; }
.ri-share-forward-box-line:before { content: "\f097"; }
.ri-share-forward-fill:before { content: "\f098"; }
.ri-share-forward-line:before { content: "\f099"; }
.ri-share-line:before { content: "\f09a"; }
.ri-shield-cross-fill:before { content: "\f09b"; }
.ri-shield-cross-line:before { content: "\f09c"; }
.ri-shield-fill:before { content: "\f09d"; }
.ri-shield-flash-fill:before { content: "\f09e"; }
.ri-shield-flash-line:before { content: "\f09f"; }
.ri-shield-keyhole-fill:before { content: "\f0a0"; }
.ri-shield-keyhole-line:before { content: "\f0a1"; }
.ri-shield-line:before { content: "\f0a2"; }
.ri-shield-star-fill:before { content: "\f0a3"; }
.ri-shield-star-line:before { content: "\f0a4"; }
.ri-shield-user-fill:before { content: "\f0a5"; }
.ri-shield-user-line:before { content: "\f0a6"; }
.ri-ship-2-fill:before { content: "\f0a7"; }
.ri-ship-2-line:before { content: "\f0a8"; }
.ri-ship-fill:before { content: "\f0a9"; }
.ri-ship-line:before { content: "\f0aa"; }
.ri-shirt-fill:before { content: "\f0ab"; }
.ri-shirt-line:before { content: "\f0ac"; }
.ri-shopping-bag-2-fill:before { content: "\f0ad"; }
.ri-shopping-bag-2-line:before { content: "\f0ae"; }
.ri-shopping-bag-3-fill:before { content: "\f0af"; }
.ri-shopping-bag-3-line:before { content: "\f0b0"; }
.ri-shopping-bag-fill:before { content: "\f0b1"; }
.ri-shopping-bag-line:before { content: "\f0b2"; }
.ri-shopping-basket-2-fill:before { content: "\f0b3"; }
.ri-shopping-basket-2-line:before { content: "\f0b4"; }
.ri-shopping-basket-fill:before { content: "\f0b5"; }
.ri-shopping-basket-line:before { content: "\f0b6"; }
.ri-shopping-cart-2-fill:before { content: "\f0b7"; }
.ri-shopping-cart-2-line:before { content: "\f0b8"; }
.ri-shopping-cart-fill:before { content: "\f0b9"; }
.ri-shopping-cart-line:before { content: "\f0ba"; }
.ri-showers-fill:before { content: "\f0bb"; }
.ri-showers-line:before { content: "\f0bc"; }
.ri-shuffle-fill:before { content: "\f0bd"; }
.ri-shuffle-line:before { content: "\f0be"; }
.ri-shut-down-fill:before { content: "\f0bf"; }
.ri-shut-down-line:before { content: "\f0c0"; }
.ri-side-bar-fill:before { content: "\f0c1"; }
.ri-side-bar-line:before { content: "\f0c2"; }
.ri-signal-tower-fill:before { content: "\f0c3"; }
.ri-signal-tower-line:before { content: "\f0c4"; }
.ri-signal-wifi-1-fill:before { content: "\f0c5"; }
.ri-signal-wifi-1-line:before { content: "\f0c6"; }
.ri-signal-wifi-2-fill:before { content: "\f0c7"; }
.ri-signal-wifi-2-line:before { content: "\f0c8"; }
.ri-signal-wifi-3-fill:before { content: "\f0c9"; }
.ri-signal-wifi-3-line:before { content: "\f0ca"; }
.ri-signal-wifi-error-fill:before { content: "\f0cb"; }
.ri-signal-wifi-error-line:before { content: "\f0cc"; }
.ri-signal-wifi-fill:before { content: "\f0cd"; }
.ri-signal-wifi-line:before { content: "\f0ce"; }
.ri-signal-wifi-off-fill:before { content: "\f0cf"; }
.ri-signal-wifi-off-line:before { content: "\f0d0"; }
.ri-sim-card-2-fill:before { content: "\f0d1"; }
.ri-sim-card-2-line:before { content: "\f0d2"; }
.ri-sim-card-fill:before { content: "\f0d3"; }
.ri-sim-card-line:before { content: "\f0d4"; }
.ri-single-quotes-l:before { content: "\f0d5"; }
.ri-single-quotes-r:before { content: "\f0d6"; }
.ri-sip-fill:before { content: "\f0d7"; }
.ri-sip-line:before { content: "\f0d8"; }
.ri-skip-back-fill:before { content: "\f0d9"; }
.ri-skip-back-line:before { content: "\f0da"; }
.ri-skip-back-mini-fill:before { content: "\f0db"; }
.ri-skip-back-mini-line:before { content: "\f0dc"; }
.ri-skip-forward-fill:before { content: "\f0dd"; }
.ri-skip-forward-line:before { content: "\f0de"; }
.ri-skip-forward-mini-fill:before { content: "\f0df"; }
.ri-skip-forward-mini-line:before { content: "\f0e0"; }
.ri-skull-2-fill:before { content: "\f0e1"; }
.ri-skull-2-line:before { content: "\f0e2"; }
.ri-skull-fill:before { content: "\f0e3"; }
.ri-skull-line:before { content: "\f0e4"; }
.ri-skype-fill:before { content: "\f0e5"; }
.ri-skype-line:before { content: "\f0e6"; }
.ri-slack-fill:before { content: "\f0e7"; }
.ri-slack-line:before { content: "\f0e8"; }
.ri-slice-fill:before { content: "\f0e9"; }
.ri-slice-line:before { content: "\f0ea"; }
.ri-slideshow-2-fill:before { content: "\f0eb"; }
.ri-slideshow-2-line:before { content: "\f0ec"; }
.ri-slideshow-3-fill:before { content: "\f0ed"; }
.ri-slideshow-3-line:before { content: "\f0ee"; }
.ri-slideshow-4-fill:before { content: "\f0ef"; }
.ri-slideshow-4-line:before { content: "\f0f0"; }
.ri-slideshow-fill:before { content: "\f0f1"; }
.ri-slideshow-line:before { content: "\f0f2"; }
.ri-smartphone-fill:before { content: "\f0f3"; }
.ri-smartphone-line:before { content: "\f0f4"; }
.ri-snapchat-fill:before { content: "\f0f5"; }
.ri-snapchat-line:before { content: "\f0f6"; }
.ri-snowy-fill:before { content: "\f0f7"; }
.ri-snowy-line:before { content: "\f0f8"; }
.ri-sort-asc:before { content: "\f0f9"; }
.ri-sort-desc:before { content: "\f0fa"; }
.ri-sound-module-fill:before { content: "\f0fb"; }
.ri-sound-module-line:before { content: "\f0fc"; }
.ri-soundcloud-fill:before { content: "\f0fd"; }
.ri-soundcloud-line:before { content: "\f0fe"; }
.ri-space-ship-fill:before { content: "\f0ff"; }
.ri-space-ship-line:before { content: "\f100"; }
.ri-space:before { content: "\f101"; }
.ri-spam-2-fill:before { content: "\f102"; }
.ri-spam-2-line:before { content: "\f103"; }
.ri-spam-3-fill:before { content: "\f104"; }
.ri-spam-3-line:before { content: "\f105"; }
.ri-spam-fill:before { content: "\f106"; }
.ri-spam-line:before { content: "\f107"; }
.ri-speaker-2-fill:before { content: "\f108"; }
.ri-speaker-2-line:before { content: "\f109"; }
.ri-speaker-3-fill:before { content: "\f10a"; }
.ri-speaker-3-line:before { content: "\f10b"; }
.ri-speaker-fill:before { content: "\f10c"; }
.ri-speaker-line:before { content: "\f10d"; }
.ri-spectrum-fill:before { content: "\f10e"; }
.ri-spectrum-line:before { content: "\f10f"; }
.ri-speed-fill:before { content: "\f110"; }
.ri-speed-line:before { content: "\f111"; }
.ri-speed-mini-fill:before { content: "\f112"; }
.ri-speed-mini-line:before { content: "\f113"; }
.ri-spotify-fill:before { content: "\f114"; }
.ri-spotify-line:before { content: "\f115"; }
.ri-spy-fill:before { content: "\f116"; }
.ri-spy-line:before { content: "\f117"; }
.ri-stack-fill:before { content: "\f118"; }
.ri-stack-line:before { content: "\f119"; }
.ri-stack-overflow-fill:before { content: "\f11a"; }
.ri-stack-overflow-line:before { content: "\f11b"; }
.ri-stackshare-fill:before { content: "\f11c"; }
.ri-stackshare-line:before { content: "\f11d"; }
.ri-star-fill:before { content: "\f11e"; }
.ri-star-half-fill:before { content: "\f11f"; }
.ri-star-half-line:before { content: "\f120"; }
.ri-star-half-s-fill:before { content: "\f121"; }
.ri-star-half-s-line:before { content: "\f122"; }
.ri-star-line:before { content: "\f123"; }
.ri-star-s-fill:before { content: "\f124"; }
.ri-star-s-line:before { content: "\f125"; }
.ri-star-smile-fill:before { content: "\f126"; }
.ri-star-smile-line:before { content: "\f127"; }
.ri-steam-fill:before { content: "\f128"; }
.ri-steam-line:before { content: "\f129"; }
.ri-steering-2-fill:before { content: "\f12a"; }
.ri-steering-2-line:before { content: "\f12b"; }
.ri-steering-fill:before { content: "\f12c"; }
.ri-steering-line:before { content: "\f12d"; }
.ri-sticky-note-2-fill:before { content: "\f12e"; }
.ri-sticky-note-2-line:before { content: "\f12f"; }
.ri-sticky-note-fill:before { content: "\f130"; }
.ri-sticky-note-line:before { content: "\f131"; }
.ri-stock-fill:before { content: "\f132"; }
.ri-stock-line:before { content: "\f133"; }
.ri-stop-circle-fill:before { content: "\f134"; }
.ri-stop-circle-line:before { content: "\f135"; }
.ri-stop-fill:before { content: "\f136"; }
.ri-stop-line:before { content: "\f137"; }
.ri-stop-mini-fill:before { content: "\f138"; }
.ri-stop-mini-line:before { content: "\f139"; }
.ri-store-2-fill:before { content: "\f13a"; }
.ri-store-2-line:before { content: "\f13b"; }
.ri-store-3-fill:before { content: "\f13c"; }
.ri-store-3-line:before { content: "\f13d"; }
.ri-store-fill:before { content: "\f13e"; }
.ri-store-line:before { content: "\f13f"; }
.ri-strikethrough-2:before { content: "\f140"; }
.ri-strikethrough:before { content: "\f141"; }
.ri-subscript-2:before { content: "\f142"; }
.ri-subscript:before { content: "\f143"; }
.ri-subtract-fill:before { content: "\f144"; }
.ri-subtract-line:before { content: "\f145"; }
.ri-subway-fill:before { content: "\f146"; }
.ri-subway-line:before { content: "\f147"; }
.ri-subway-wifi-fill:before { content: "\f148"; }
.ri-subway-wifi-line:before { content: "\f149"; }
.ri-suitcase-2-fill:before { content: "\f14a"; }
.ri-suitcase-2-line:before { content: "\f14b"; }
.ri-suitcase-3-fill:before { content: "\f14c"; }
.ri-suitcase-3-line:before { content: "\f14d"; }
.ri-suitcase-fill:before { content: "\f14e"; }
.ri-suitcase-line:before { content: "\f14f"; }
.ri-sun-cloudy-fill:before { content: "\f150"; }
.ri-sun-cloudy-line:before { content: "\f151"; }
.ri-sun-fill:before { content: "\f152"; }
.ri-sun-foggy-fill:before { content: "\f153"; }
.ri-sun-foggy-line:before { content: "\f154"; }
.ri-sun-line:before { content: "\f155"; }
.ri-superscript-2:before { content: "\f156"; }
.ri-superscript:before { content: "\f157"; }
.ri-surround-sound-fill:before { content: "\f158"; }
.ri-surround-sound-line:before { content: "\f159"; }
.ri-survey-fill:before { content: "\f15a"; }
.ri-survey-line:before { content: "\f15b"; }
.ri-swap-box-fill:before { content: "\f15c"; }
.ri-swap-box-line:before { content: "\f15d"; }
.ri-swap-fill:before { content: "\f15e"; }
.ri-swap-line:before { content: "\f15f"; }
.ri-switch-fill:before { content: "\f160"; }
.ri-switch-line:before { content: "\f161"; }
.ri-sword-fill:before { content: "\f162"; }
.ri-sword-line:before { content: "\f163"; }
.ri-t-box-fill:before { content: "\f164"; }
.ri-t-box-line:before { content: "\f165"; }
.ri-t-shirt-2-fill:before { content: "\f166"; }
.ri-t-shirt-2-line:before { content: "\f167"; }
.ri-t-shirt-air-fill:before { content: "\f168"; }
.ri-t-shirt-air-line:before { content: "\f169"; }
.ri-t-shirt-fill:before { content: "\f16a"; }
.ri-t-shirt-line:before { content: "\f16b"; }
.ri-table-2:before { content: "\f16c"; }
.ri-table-alt-fill:before { content: "\f16d"; }
.ri-table-alt-line:before { content: "\f16e"; }
.ri-table-fill:before { content: "\f16f"; }
.ri-table-line:before { content: "\f170"; }
.ri-tablet-fill:before { content: "\f171"; }
.ri-tablet-line:before { content: "\f172"; }
.ri-takeaway-fill:before { content: "\f173"; }
.ri-takeaway-line:before { content: "\f174"; }
.ri-taobao-fill:before { content: "\f175"; }
.ri-taobao-line:before { content: "\f176"; }
.ri-tape-fill:before { content: "\f177"; }
.ri-tape-line:before { content: "\f178"; }
.ri-task-fill:before { content: "\f179"; }
.ri-task-line:before { content: "\f17a"; }
.ri-taxi-fill:before { content: "\f17b"; }
.ri-taxi-line:before { content: "\f17c"; }
.ri-taxi-wifi-fill:before { content: "\f17d"; }
.ri-taxi-wifi-line:before { content: "\f17e"; }
.ri-team-fill:before { content: "\f17f"; }
.ri-team-line:before { content: "\f180"; }
.ri-telegram-fill:before { content: "\f181"; }
.ri-telegram-line:before { content: "\f182"; }
.ri-temp-cold-fill:before { content: "\f183"; }
.ri-temp-cold-line:before { content: "\f184"; }
.ri-temp-hot-fill:before { content: "\f185"; }
.ri-temp-hot-line:before { content: "\f186"; }
.ri-terminal-box-fill:before { content: "\f187"; }
.ri-terminal-box-line:before { content: "\f188"; }
.ri-terminal-fill:before { content: "\f189"; }
.ri-terminal-line:before { content: "\f18a"; }
.ri-terminal-window-fill:before { content: "\f18b"; }
.ri-terminal-window-line:before { content: "\f18c"; }
.ri-text-direction-l:before { content: "\f18d"; }
.ri-text-direction-r:before { content: "\f18e"; }
.ri-text-spacing:before { content: "\f18f"; }
.ri-text-wrap:before { content: "\f190"; }
.ri-text:before { content: "\f191"; }
.ri-thumb-down-fill:before { content: "\f192"; }
.ri-thumb-down-line:before { content: "\f193"; }
.ri-thumb-up-fill:before { content: "\f194"; }
.ri-thumb-up-line:before { content: "\f195"; }
.ri-thunderstorms-fill:before { content: "\f196"; }
.ri-thunderstorms-line:before { content: "\f197"; }
.ri-ticket-2-fill:before { content: "\f198"; }
.ri-ticket-2-line:before { content: "\f199"; }
.ri-ticket-fill:before { content: "\f19a"; }
.ri-ticket-line:before { content: "\f19b"; }
.ri-time-fill:before { content: "\f19c"; }
.ri-time-line:before { content: "\f19d"; }
.ri-timer-2-fill:before { content: "\f19e"; }
.ri-timer-2-line:before { content: "\f19f"; }
.ri-timer-fill:before { content: "\f1a0"; }
.ri-timer-flash-fill:before { content: "\f1a1"; }
.ri-timer-flash-line:before { content: "\f1a2"; }
.ri-timer-line:before { content: "\f1a3"; }
.ri-todo-fill:before { content: "\f1a4"; }
.ri-todo-line:before { content: "\f1a5"; }
.ri-toggle-fill:before { content: "\f1a6"; }
.ri-toggle-line:before { content: "\f1a7"; }
.ri-tools-fill:before { content: "\f1a8"; }
.ri-tools-line:before { content: "\f1a9"; }
.ri-tornado-fill:before { content: "\f1aa"; }
.ri-tornado-line:before { content: "\f1ab"; }
.ri-trademark-fill:before { content: "\f1ac"; }
.ri-trademark-line:before { content: "\f1ad"; }
.ri-traffic-light-fill:before { content: "\f1ae"; }
.ri-traffic-light-line:before { content: "\f1af"; }
.ri-train-fill:before { content: "\f1b0"; }
.ri-train-line:before { content: "\f1b1"; }
.ri-train-wifi-fill:before { content: "\f1b2"; }
.ri-train-wifi-line:before { content: "\f1b3"; }
.ri-translate-2:before { content: "\f1b4"; }
.ri-translate:before { content: "\f1b5"; }
.ri-travesti-fill:before { content: "\f1b6"; }
.ri-travesti-line:before { content: "\f1b7"; }
.ri-treasure-map-fill:before { content: "\f1b8"; }
.ri-treasure-map-line:before { content: "\f1b9"; }
.ri-trello-fill:before { content: "\f1ba"; }
.ri-trello-line:before { content: "\f1bb"; }
.ri-trophy-fill:before { content: "\f1bc"; }
.ri-trophy-line:before { content: "\f1bd"; }
.ri-truck-fill:before { content: "\f1be"; }
.ri-truck-line:before { content: "\f1bf"; }
.ri-tumblr-fill:before { content: "\f1c0"; }
.ri-tumblr-line:before { content: "\f1c1"; }
.ri-tv-2-fill:before { content: "\f1c2"; }
.ri-tv-2-line:before { content: "\f1c3"; }
.ri-tv-fill:before { content: "\f1c4"; }
.ri-tv-line:before { content: "\f1c5"; }
.ri-twitch-fill:before { content: "\f1c6"; }
.ri-twitch-line:before { content: "\f1c7"; }
.ri-twitter-fill:before { content: "\f1c8"; }
.ri-twitter-line:before { content: "\f1c9"; }
.ri-typhoon-fill:before { content: "\f1ca"; }
.ri-typhoon-line:before { content: "\f1cb"; }
.ri-u-disk-fill:before { content: "\f1cc"; }
.ri-u-disk-line:before { content: "\f1cd"; }
.ri-ubuntu-fill:before { content: "\f1ce"; }
.ri-ubuntu-line:before { content: "\f1cf"; }
.ri-umbrella-fill:before { content: "\f1d0"; }
.ri-umbrella-line:before { content: "\f1d1"; }
.ri-underline:before { content: "\f1d2"; }
.ri-uninstall-fill:before { content: "\f1d3"; }
.ri-uninstall-line:before { content: "\f1d4"; }
.ri-unsplash-fill:before { content: "\f1d5"; }
.ri-unsplash-line:before { content: "\f1d6"; }
.ri-upload-2-fill:before { content: "\f1d7"; }
.ri-upload-2-line:before { content: "\f1d8"; }
.ri-upload-cloud-2-fill:before { content: "\f1d9"; }
.ri-upload-cloud-2-line:before { content: "\f1da"; }
.ri-upload-cloud-fill:before { content: "\f1db"; }
.ri-upload-cloud-line:before { content: "\f1dc"; }
.ri-upload-fill:before { content: "\f1dd"; }
.ri-upload-line:before { content: "\f1de"; }
.ri-user-2-fill:before { content: "\f1df"; }
.ri-user-2-line:before { content: "\f1e0"; }
.ri-user-3-fill:before { content: "\f1e1"; }
.ri-user-3-line:before { content: "\f1e2"; }
.ri-user-4-fill:before { content: "\f1e3"; }
.ri-user-4-line:before { content: "\f1e4"; }
.ri-user-5-fill:before { content: "\f1e5"; }
.ri-user-5-line:before { content: "\f1e6"; }
.ri-user-6-fill:before { content: "\f1e7"; }
.ri-user-6-line:before { content: "\f1e8"; }
.ri-user-add-fill:before { content: "\f1e9"; }
.ri-user-add-line:before { content: "\f1ea"; }
.ri-user-fill:before { content: "\f1eb"; }
.ri-user-follow-fill:before { content: "\f1ec"; }
.ri-user-follow-line:before { content: "\f1ed"; }
.ri-user-heart-fill:before { content: "\f1ee"; }
.ri-user-heart-line:before { content: "\f1ef"; }
.ri-user-line:before { content: "\f1f0"; }
.ri-user-location-fill:before { content: "\f1f1"; }
.ri-user-location-line:before { content: "\f1f2"; }
.ri-user-received-2-fill:before { content: "\f1f3"; }
.ri-user-received-2-line:before { content: "\f1f4"; }
.ri-user-received-fill:before { content: "\f1f5"; }
.ri-user-received-line:before { content: "\f1f6"; }
.ri-user-search-fill:before { content: "\f1f7"; }
.ri-user-search-line:before { content: "\f1f8"; }
.ri-user-settings-fill:before { content: "\f1f9"; }
.ri-user-settings-line:before { content: "\f1fa"; }
.ri-user-shared-2-fill:before { content: "\f1fb"; }
.ri-user-shared-2-line:before { content: "\f1fc"; }
.ri-user-shared-fill:before { content: "\f1fd"; }
.ri-user-shared-line:before { content: "\f1fe"; }
.ri-user-smile-fill:before { content: "\f1ff"; }
.ri-user-smile-line:before { content: "\f200"; }
.ri-user-star-fill:before { content: "\f201"; }
.ri-user-star-line:before { content: "\f202"; }
.ri-user-unfollow-fill:before { content: "\f203"; }
.ri-user-unfollow-line:before { content: "\f204"; }
.ri-user-voice-fill:before { content: "\f205"; }
.ri-user-voice-line:before { content: "\f206"; }
.ri-video-add-fill:before { content: "\f207"; }
.ri-video-add-line:before { content: "\f208"; }
.ri-video-chat-fill:before { content: "\f209"; }
.ri-video-chat-line:before { content: "\f20a"; }
.ri-video-download-fill:before { content: "\f20b"; }
.ri-video-download-line:before { content: "\f20c"; }
.ri-video-fill:before { content: "\f20d"; }
.ri-video-line:before { content: "\f20e"; }
.ri-video-upload-fill:before { content: "\f20f"; }
.ri-video-upload-line:before { content: "\f210"; }
.ri-vidicon-2-fill:before { content: "\f211"; }
.ri-vidicon-2-line:before { content: "\f212"; }
.ri-vidicon-fill:before { content: "\f213"; }
.ri-vidicon-line:before { content: "\f214"; }
.ri-vip-crown-2-fill:before { content: "\f215"; }
.ri-vip-crown-2-line:before { content: "\f216"; }
.ri-vip-crown-fill:before { content: "\f217"; }
.ri-vip-crown-line:before { content: "\f218"; }
.ri-vip-diamond-fill:before { content: "\f219"; }
.ri-vip-diamond-line:before { content: "\f21a"; }
.ri-vip-fill:before { content: "\f21b"; }
.ri-vip-line:before { content: "\f21c"; }
.ri-visa-fill:before { content: "\f21d"; }
.ri-visa-line:before { content: "\f21e"; }
.ri-voice-recognition-fill:before { content: "\f21f"; }
.ri-voice-recognition-line:before { content: "\f220"; }
.ri-voiceprint-fill:before { content: "\f221"; }
.ri-voiceprint-line:before { content: "\f222"; }
.ri-volume-down-fill:before { content: "\f223"; }
.ri-volume-down-line:before { content: "\f224"; }
.ri-volume-mute-fill:before { content: "\f225"; }
.ri-volume-mute-line:before { content: "\f226"; }
.ri-volume-off-vibrate-fill:before { content: "\f227"; }
.ri-volume-off-vibrate-line:before { content: "\f228"; }
.ri-volume-up-fill:before { content: "\f229"; }
.ri-volume-up-line:before { content: "\f22a"; }
.ri-volume-vibrate-fill:before { content: "\f22b"; }
.ri-volume-vibrate-line:before { content: "\f22c"; }
.ri-vuejs-fill:before { content: "\f22d"; }
.ri-vuejs-line:before { content: "\f22e"; }
.ri-walk-fill:before { content: "\f22f"; }
.ri-walk-line:before { content: "\f230"; }
.ri-wallet-2-fill:before { content: "\f231"; }
.ri-wallet-2-line:before { content: "\f232"; }
.ri-wallet-3-fill:before { content: "\f233"; }
.ri-wallet-3-line:before { content: "\f234"; }
.ri-wallet-fill:before { content: "\f235"; }
.ri-wallet-line:before { content: "\f236"; }
.ri-water-flash-fill:before { content: "\f237"; }
.ri-water-flash-line:before { content: "\f238"; }
.ri-webcam-fill:before { content: "\f239"; }
.ri-webcam-line:before { content: "\f23a"; }
.ri-wechat-2-fill:before { content: "\f23b"; }
.ri-wechat-2-line:before { content: "\f23c"; }
.ri-wechat-fill:before { content: "\f23d"; }
.ri-wechat-line:before { content: "\f23e"; }
.ri-wechat-pay-fill:before { content: "\f23f"; }
.ri-wechat-pay-line:before { content: "\f240"; }
.ri-weibo-fill:before { content: "\f241"; }
.ri-weibo-line:before { content: "\f242"; }
.ri-whatsapp-fill:before { content: "\f243"; }
.ri-whatsapp-line:before { content: "\f244"; }
.ri-wheelchair-fill:before { content: "\f245"; }
.ri-wheelchair-line:before { content: "\f246"; }
.ri-wifi-fill:before { content: "\f247"; }
.ri-wifi-line:before { content: "\f248"; }
.ri-wifi-off-fill:before { content: "\f249"; }
.ri-wifi-off-line:before { content: "\f24a"; }
.ri-window-2-fill:before { content: "\f24b"; }
.ri-window-2-line:before { content: "\f24c"; }
.ri-window-fill:before { content: "\f24d"; }
.ri-window-line:before { content: "\f24e"; }
.ri-windows-fill:before { content: "\f24f"; }
.ri-windows-line:before { content: "\f250"; }
.ri-windy-fill:before { content: "\f251"; }
.ri-windy-line:before { content: "\f252"; }
.ri-wireless-charging-fill:before { content: "\f253"; }
.ri-wireless-charging-line:before { content: "\f254"; }
.ri-women-fill:before { content: "\f255"; }
.ri-women-line:before { content: "\f256"; }
.ri-wubi-input:before { content: "\f257"; }
.ri-xbox-fill:before { content: "\f258"; }
.ri-xbox-line:before { content: "\f259"; }
.ri-xing-fill:before { content: "\f25a"; }
.ri-xing-line:before { content: "\f25b"; }
.ri-youtube-fill:before { content: "\f25c"; }
.ri-youtube-line:before { content: "\f25d"; }
.ri-zcool-fill:before { content: "\f25e"; }
.ri-zcool-line:before { content: "\f25f"; }
.ri-zhihu-fill:before { content: "\f260"; }
.ri-zhihu-line:before { content: "\f261"; }
.ri-zoom-in-fill:before { content: "\f262"; }
.ri-zoom-in-line:before { content: "\f263"; }
.ri-zoom-out-fill:before { content: "\f264"; }
.ri-zoom-out-line:before { content: "\f265"; }

