{"navbar": {"search": {"text": "Search..."}, "dropdown": {"megamenu": {"text": "Mega Menu", "uicontent": {"title": "UI Components", "list": {"lightbox": "Lightbox", "rangeslider": "Range Slider", "sweetalert": "<PERSON> Alert", "rating": "Rating", "forms": "Forms", "tables": "Tables", "charts": "Charts"}}, "application": {"title": "Applications", "list": {"ecommerce": "Ecommece", "calendar": "Calendar", "email": "Email", "projects": "Projects", "tasks": "Tasks", "contacts": "Contacts"}}, "extrapages": {"title": "Extra Pages", "list": {"lightsidebar": "Light Sidebar", "compactsidebar": "Compact Sidebar", "horizontallayout": "Horizontal Layout", "maintenance": "Maintenance", "comingsoon": "Coming Soon", "timeline": "Timeline", "faqs": "FAQs"}}}, "site": {"list": {"github": "GitHub", "bitbucket": "Bitbucket", "dribbble": "<PERSON><PERSON><PERSON>", "dropbox": "Dropbox", "mailchimp": "Mail Chimp", "slack": "<PERSON><PERSON>ck"}}, "notification": {"text": "Notifications", "subtext": "View All", "order": {"title": "Your order is placed", "text": "If several languages coalesce the grammar", "time": "3 min ago"}, "james": {"title": "<PERSON>", "text": "It will seem like simplified English.", "time": "1 hours ago"}, "item": {"title": "Your item is shipped", "text": "If several languages coalesce the grammar", "time": "3 min ago"}, "salena": {"title": "<PERSON><PERSON>", "text": "As a skeptical Cambridge friend of mine occidental.", "time": "1 hours ago"}, "button": "Load More.."}, "kevin": {"text": "<PERSON>", "list": {"profile": "Profile", "mywallet": "My Wallet", "settings": "Settings", "lockscreen": "Lock screen", "logout": "Logout"}}}}, "menuitems": {"menu": {"text": "<PERSON><PERSON>"}, "dashboard": {"text": "Dashboard", "badge": "3"}, "layouts": {"text": "Layouts", "list": {"horizontal": "Horizontal", "lightsidebar": "Light Sidebar", "compactsidebar": "Compact Sidebar", "iconsidebar": "Icons Sidebar", "boxed": "Boxed Layout", "vertical": "Vertical", "lighttopbar": "Light Topbar"}}, "apps": {"text": "Apps"}, "calendar": {"text": "Calendar"}, "chat": {"text": "Cha<PERSON>", "badge": "New"}, "ecommerce": {"text": "Ecommerce", "list": {"products": "Products", "productdetail": "Product Detail", "orders": "Orders", "customers": "Customers", "cart": "<PERSON><PERSON>", "checkout": "Checkout", "shops": "Shops", "addproduct": "Add Product"}}, "email": {"text": "Email", "list": {"inbox": "Inbox", "reademail": "Read Email"}}, "kanban": {"text": "Kanban Board"}, "pages": {"text": "Pages"}, "authentication": {"text": "Authentication", "list": {"login": "<PERSON><PERSON>", "register": "Register", "recoverpwd": "Recover Password", "lockscreen": "Lock screen"}}, "utility": {"text": "Utility", "list": {"starter": "Starter <PERSON>", "maintenance": "Maintenance", "comingsoon": "Coming Soon", "timeline": "Timeline", "faqs": "FAQs", "pricing": "Pricing", "error404": "Error 404", "error500": "Error 500"}}, "components": {"text": "Components"}, "uielements": {"text": "UI Elements", "list": {"alerts": "<PERSON><PERSON><PERSON>", "buttons": "Buttons", "cards": "Cards", "carousel": "Carousel", "dropdowns": "Dropdowns", "grid": "Grid", "images": "Images", "modals": "Modals", "rangeslider": "Range Slider", "progressbar": "Progress Bars", "sweetalert": "<PERSON> Alert", "tabs": "Tabs & Accordions", "typography": "Typography", "video": "Video", "general": "General", "lightbox": "Lightbox", "sessiontimeout": "Session Timeout", "rating": "Rating", "notifications": "Notifications"}}, "forms": {"text": "Forms", "badge": "8", "list": {"elements": "Form Elements", "validation": "Form Validation", "advanced": "Form Advanced", "editor": "Form Editor", "fileupload": "Form File Upload", "wizard": "Form Wizard", "mask": "Form Mask"}}, "tables": {"text": "Tables", "list": {"basic": "Basic Tables", "advanced": "Advanced Table"}}, "charts": {"text": "Charts", "list": {"apex": "Apex Chart", "chartjs": "Chartjs Chart", "chartist": "Chartist Chart", "echart": "E Chart"}}, "icons": {"text": "Icons", "list": {"remix": "Remix Icons", "materialdesign": "Material Design", "dripicons": "Dripicons", "fontawesome": "Font Awesome 5"}}, "maps": {"text": "Maps", "list": {"googlemap": "Google Maps", "leaflet": "Leaflet Maps"}}, "multilevel": {"text": "Multi Level", "list": {"level1": {"1": "Level 1.1", "2": "Level 1.2", "level2": {"1": "Level 2.1", "2": "Level 2.2"}}}}}}