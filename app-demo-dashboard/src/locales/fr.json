{"navbar": {"search": {"text": "Chercher..."}, "dropdown": {"megamenu": {"text": "Mega Menu", "uicontent": {"title": "Composants de l'interface utilisateur", "list": {"lightbox": "Boite à lumière", "rangeslider": "<PERSON><PERSON> de plage", "sweetalert": "<PERSON> Alert", "rating": "Évaluation", "forms": "Formes", "tables": "les tables", "charts": "Graphiques"}}, "application": {"title": "Applications", "list": {"ecommerce": "Commerce électronique", "calendar": "<PERSON><PERSON><PERSON>", "email": "Email", "projects": "Projets", "tasks": "Tâches", "contacts": "Contacts"}}, "extrapages": {"title": "Pages supplémentaires", "list": {"lightsidebar": "Barre latérale légère", "compactsidebar": "Barre latérale compacte", "horizontallayout": "Disposition horizontale", "maintenance": "<PERSON><PERSON><PERSON>", "comingsoon": "Bientôt disponible", "timeline": "Chronologie", "faqs": "FAQ"}}}, "site": {"list": {"github": "GitHub", "bitbucket": "Bitbucket", "dribbble": "Dribble", "dropbox": "Dropbox", "mailchimp": "Mail Chimp", "slack": "<PERSON><PERSON>"}}, "notification": {"text": "Notifications", "subtext": "Voir tout", "order": {"title": "Votre commande est passée", "text": "Si plusieurs langues fusionnent la grammaire", "time": "Il y a 3 minutes"}, "james": {"title": "<PERSON>", "text": "It will seem like simplified English.", "time": "Il y a 1 heure"}, "item": {"title": "Votre article est expédié", "text": "Si plusieurs langues fusionnent la grammaire", "time": "Il y a 3 minutes"}, "salena": {"title": "<PERSON><PERSON>", "text": "As a skeptical Cambridge friend of mine occidental.", "time": "Il y a 1 heure"}, "button": "Charger plus.."}, "kevin": {"text": "<PERSON>", "list": {"profile": "Profil", "mywallet": "Mon portefeuille", "settings": "Réglages", "lockscreen": "<PERSON><PERSON><PERSON>", "logout": "Se déconnecter"}}}}, "menuitems": {"menu": {"text": "<PERSON><PERSON>"}, "dashboard": {"text": "Tableaux de bord", "badge": "3"}, "layouts": {"text": "Disposition", "list": {"horizontal": "Horizontale", "lightsidebar": "Barre latérale légère", "compactsidebar": "Barre latérale compacte", "iconsidebar": "Barre latérale des icônes", "boxed": "Disposition en boîte", "vertical": "Verticale", "lighttopbar": "Barre supérieure légère"}}, "apps": {"text": "Apps"}, "calendar": {"text": "<PERSON><PERSON><PERSON>"}, "chat": {"text": "<PERSON><PERSON><PERSON>", "badge": "Nouvelle"}, "ecommerce": {"text": "Commerce électronique", "list": {"products": "Des produits", "productdetail": "Product Detail", "orders": "<PERSON><PERSON><PERSON>", "customers": "Les clients", "cart": "Chariot", "checkout": "Check-out", "shops": "Ma<PERSON>ins", "addproduct": "Ajouter un produit"}}, "email": {"text": "Email", "list": {"inbox": "<PERSON><PERSON><PERSON>", "reademail": "Lire l'e-mail"}}, "kanban": {"text": "Tableau Ka<PERSON>ban"}, "pages": {"text": "Pages"}, "authentication": {"text": "Authentification", "list": {"login": "S'identifier", "register": "S'inscrire", "recoverpwd": "Récupérer mot de passe", "lockscreen": "<PERSON><PERSON><PERSON>"}}, "utility": {"text": "Utilitaire", "list": {"starter": "Page de d<PERSON>", "maintenance": "<PERSON><PERSON><PERSON>", "comingsoon": "Bientôt disponible", "timeline": "Chronologie", "faqs": "FAQ", "pricing": "Tarification", "error404": "Erreur 404", "error500": "Erreur 500"}}, "components": {"text": "Composantes"}, "uielements": {"text": "Éléments de l'interface utilisateur", "list": {"alerts": "<PERSON><PERSON><PERSON>", "buttons": "Boutons", "cards": "<PERSON><PERSON>", "carousel": "Carrousel", "dropdowns": "Liste déroulante", "grid": "la grille", "images": "Images", "modals": "Modals", "rangeslider": "<PERSON><PERSON> de plage", "progressbar": "Barres de progression", "sweetalert": "<PERSON> Alert", "tabs": "Tablatures et accordéons", "typography": "Typographie", "video": "Vidéo", "general": "G<PERSON><PERSON><PERSON>", "lightbox": "Boite à lumière", "sessiontimeout": "Expiration de la session", "rating": "Évaluation", "notifications": "Notifications"}}, "forms": {"text": "Formes", "badge": "8", "list": {"elements": "Éléments de formulaire", "validation": "Validation de formulaire", "advanced": "Formulaire avancé", "editor": "Éditeur de formulaires", "fileupload": "Téléchargement de fichier de formulaire", "wizard": "Assistant de formulaire", "mask": "Masque de forme"}}, "tables": {"text": "les tables", "list": {"basic": "Tableaux de base", "advanced": "Tableau avancé"}}, "charts": {"text": "Graphiques", "list": {"apex": "Graphique Apex", "chartjs": "G<PERSON><PERSON><PERSON>", "chartist": "Graphique Chartist", "echart": "Graphique E"}}, "icons": {"text": "Icônes", "list": {"remix": "Remix Icons", "materialdesign": "Conception matérielle", "dripicons": "Dripicons", "fontawesome": "Police géniale"}}, "maps": {"text": "Plans", "list": {"googlemap": "Google Maps", "leaflet": "Cartes des dépliants"}}, "multilevel": {"text": "Multi niveaux", "list": {"level1": {"1": "Niveau 1.1", "2": "Niveau 1.2", "level2": {"1": "Niveau 2.1", "2": "Niveau 2.2"}}}}}}