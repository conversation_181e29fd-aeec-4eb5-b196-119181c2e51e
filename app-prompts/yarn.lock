# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: ada901b9e7c680d190f1d012c84217ce0063d8f5c5a7725bb91ec3c5ed99bb7572680eb2d2938a531ccbaec39a95422fcd8a6b4a13110c7d98dd75402f66a0cd
  languageName: node
  linkType: hard

"@achrinza/node-ipc@npm:^9.2.5":
  version: 9.2.7
  resolution: "@achrinza/node-ipc@npm:9.2.7"
  dependencies:
    "@node-ipc/js-queue": 2.0.3
    event-pubsub: 4.3.0
    js-message: 1.0.7
  checksum: 6a2c8e309873757acdf9542465ae959515ce14786eb83d125d9162f6d44ea42e6a15a552a12c3d39efc1793be80a11e050e836b2af85831e13f6316615813d68
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 03c04fd526acc64a1f4df22651186f3e5ef0a9d6d6530ce4482ec9841269cf7a11dbb8af79237c282d721c5312024ff17529cd72cc4768c11e999b58e2302079
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.12.11":
  version: 7.12.11
  resolution: "@babel/code-frame@npm:7.12.11"
  dependencies:
    "@babel/highlight": ^7.10.4
  checksum: 3963eff3ebfb0e091c7e6f99596ef4b258683e4ba8a134e4e95f77afe85be5c931e184fff6435fb4885d12eba04a5e25532f7fbc292ca13b48e7da943474e2f3
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.22.13":
  version: 7.22.13
  resolution: "@babel/code-frame@npm:7.22.13"
  dependencies:
    "@babel/highlight": ^7.22.13
    chalk: ^2.4.2
  checksum: 22e342c8077c8b77eeb11f554ecca2ba14153f707b85294fcf6070b6f6150aae88a7b7436dd88d8c9289970585f3fe5b9b941c5aa3aa26a6d5a8ef3f292da058
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/compat-data@npm:7.22.9"
  checksum: bed77d9044ce948b4327b30dd0de0779fa9f3a7ed1f2d31638714ed00229fa71fc4d1617ae0eb1fad419338d3658d0e9a5a083297451e09e73e078d0347ff808
  languageName: node
  linkType: hard

"@babel/core@npm:^7.12.16":
  version: 7.22.17
  resolution: "@babel/core@npm:7.22.17"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.22.13
    "@babel/generator": ^7.22.15
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-module-transforms": ^7.22.17
    "@babel/helpers": ^7.22.15
    "@babel/parser": ^7.22.16
    "@babel/template": ^7.22.15
    "@babel/traverse": ^7.22.17
    "@babel/types": ^7.22.17
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 355216a342d1b3952d7c040dd4c99ecef6b3501ba99a713703c1fec1ae73bc92a48a0c1234562bdbb4fd334b2e452f5a6c3bb282f0e613fa89e1518c91d1aea1
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.12.16":
  version: 7.22.15
  resolution: "@babel/eslint-parser@npm:7.22.15"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": 5.1.1-v1
    eslint-visitor-keys: ^2.1.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.11.0
    eslint: ^7.5.0 || ^8.0.0
  checksum: efdc749164a40de1b68e3ed395f441dfb7864c85d0a2ee3e4bc4f06dd0b7f675acb9be97cdc9025b88b3e80d38749a2b30e392ce7f6a79313c3aaf82ba8ccd68
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/generator@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
    "@jridgewell/gen-mapping": ^0.3.2
    "@jridgewell/trace-mapping": ^0.3.17
    jsesc: ^2.5.1
  checksum: 5b2a3ccdc3634f6ea86e0a442722bcd430238369432d31f15b428a4ee8013c2f4f917b5b135bf4fc1d0a3e2f87f10fd4ce5d07955ecc2d3b9400a05c2a481374
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 53da330f1835c46f26b7bf4da31f7a496dee9fd8696cca12366b94ba19d97421ce519a74a837f687749318f94d1a37f8d1abcbf35e8ed22c32d16373b2f6198d
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
  checksum: 639c697a1c729f9fafa2dd4c9af2e18568190299b5907bd4c2d0bc818fcbd1e83ffeecc2af24327a7faa7ac4c34edd9d7940510a5e66296c19bad17001cf5c7a
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.12.16, @babel/helper-compilation-targets@npm:^7.22.15, @babel/helper-compilation-targets@npm:^7.22.5, @babel/helper-compilation-targets@npm:^7.22.6":
  version: 7.22.15
  resolution: "@babel/helper-compilation-targets@npm:7.22.15"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-validator-option": ^7.22.15
    browserslist: ^4.21.9
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: ce85196769e091ae54dd39e4a80c2a9df1793da8588e335c383d536d54f06baf648d0a08fc873044f226398c4ded15c4ae9120ee18e7dfd7c639a68e3cdc9980
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.22.11, @babel/helper-create-class-features-plugin@npm:^7.22.15, @babel/helper-create-class-features-plugin@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-create-class-features-plugin@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.15
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 52c500d8d164abb3a360b1b7c4b8fff77bc4a5920d3a2b41ae6e1d30617b0dc0b972c1f5db35b1752007e04a748908b4a99bc872b73549ae837e87dcdde005a3
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    regexpu-core: ^5.3.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 0243b8d4854f1dc8861b1029a46d3f6393ad72f366a5a08e36a4648aa682044f06da4c6e87a456260e1e1b33c999f898ba591a0760842c1387bcc93fbf2151a6
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.4.2":
  version: 0.4.2
  resolution: "@babel/helper-define-polyfill-provider@npm:0.4.2"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-plugin-utils": ^7.22.5
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 1f6dec0c5d0876d278fe15b71238eccc5f74c4e2efa2c78aaafa8bc2cc96336b8e68d94cd1a78497356c96e8b91b8c1f4452179820624d1702aee2f9832e6569
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-environment-visitor@npm:7.22.5"
  checksum: 248532077d732a34cd0844eb7b078ff917c3a8ec81a7f133593f71a860a582f05b60f818dc5049c2212e5baa12289c27889a4b81d56ef409b4863db49646c4b1
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-function-name@npm:7.22.5"
  dependencies:
    "@babel/template": ^7.22.5
    "@babel/types": ^7.22.5
  checksum: 6b1f6ce1b1f4e513bf2c8385a557ea0dd7fa37971b9002ad19268ca4384bbe90c09681fe4c076013f33deabc63a53b341ed91e792de741b4b35e01c00238177a
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 394ca191b4ac908a76e7c50ab52102669efe3a1c277033e49467913c7ed6f7c64d7eacbeabf3bed39ea1f41731e22993f763b1edce0f74ff8563fd1f380d92cc
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.22.15, @babel/helper-member-expression-to-functions@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-member-expression-to-functions@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
  checksum: c7c5d01c402dd8902c2ec3093f203ed0fc3bc5f669328a084d2e663c4c06dd0415480ee8220c6f96ba9b2dc49545c0078f221fc3900ab1e65de69a12fe7b361f
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.12.13, @babel/helper-module-imports@npm:^7.22.15, @babel/helper-module-imports@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-module-imports@npm:7.22.15"
  dependencies:
    "@babel/types": ^7.22.15
  checksum: ecd7e457df0a46f889228f943ef9b4a47d485d82e030676767e6a2fdcbdaa63594d8124d4b55fd160b41c201025aec01fc27580352b1c87a37c9c6f33d116702
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.22.15, @babel/helper-module-transforms@npm:^7.22.17, @babel/helper-module-transforms@npm:^7.22.5, @babel/helper-module-transforms@npm:^7.22.9":
  version: 7.22.17
  resolution: "@babel/helper-module-transforms@npm:7.22.17"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-module-imports": ^7.22.15
    "@babel/helper-simple-access": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/helper-validator-identifier": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 458021c74093e66179765fcc9d1c1cb694f7bdf98656f23486901d35636495c38aab4661547fac2142e13d887987d1ea30cc9fe42968376a51a99bcd207b4989
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: c70ef6cc6b6ed32eeeec4482127e8be5451d0e5282d5495d5d569d39eb04d7f1d66ec99b327f45d1d5842a9ad8c22d48567e93fc502003a47de78d122e355f7c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: c0fc7227076b6041acd2f0e818145d2e8c41968cc52fb5ca70eed48e21b8fe6dd88a0a91cbddf4951e33647336eb5ae184747ca706817ca3bef5e9e905151ff5
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.22.5, @babel/helper-remap-async-to-generator@npm:^7.22.9":
  version: 7.22.17
  resolution: "@babel/helper-remap-async-to-generator@npm:7.22.17"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-wrap-function": ^7.22.17
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 59307e623d00b6f5fa7f974e29081b2243e3f7bc3a89df331e8c1f8815d83f97bd092404a28b8bef5299028e3259450b5a943f34e1b32c7c55350436d218ab13
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.22.5, @babel/helper-replace-supers@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-replace-supers@npm:7.22.9"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d41471f56ff2616459d35a5df1900d5f0756ae78b1027040365325ef332d66e08e3be02a9489756d870887585ff222403a228546e93dd7019e19e59c0c0fe586
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: fe9686714caf7d70aedb46c3cce090f8b915b206e09225f1e4dbc416786c2fdbbee40b38b23c268b7ccef749dd2db35f255338fb4f2444429874d900dede5ad2
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 1012ef2295eb12dc073f2b9edf3425661e9b8432a3387e62a8bc27c42963f1f216ab3124228015c748770b2257b4f1fda882ca8fa34c0bf485e929ae5bc45244
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-string-parser@npm:7.22.5"
  checksum: 836851ca5ec813077bbb303acc992d75a360267aa3b5de7134d220411c852a6f17de7c0d0b8c8dcc0f567f67874c00f4528672b2a4f1bc978a3ada64c8c78467
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.15, @babel/helper-validator-identifier@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-validator-identifier@npm:7.22.15"
  checksum: eb0bee4bda664c0959924bc1ad5611eacfce806f46612202dd164fef1df8fef1a11682a1e7615288987100e9fb304982b6e2a4ff07ffe842ab8765b95ed1118c
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/helper-validator-option@npm:7.22.15"
  checksum: 68da52b1e10002a543161494c4bc0f4d0398c8fdf361d5f7f4272e95c45d5b32d974896d44f6a0ea7378c9204988879d73613ca683e13bd1304e46d25ff67a8d
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.22.17":
  version: 7.22.17
  resolution: "@babel/helper-wrap-function@npm:7.22.17"
  dependencies:
    "@babel/helper-function-name": ^7.22.5
    "@babel/template": ^7.22.15
    "@babel/types": ^7.22.17
  checksum: 95328b508049b6edd9cadd2ac89b4d4812ebdfa54a2ae77791939d795d88d561b31fd3669eea5d13558372cf2422eda05177d7f742690b5023c712bc3f0aec8e
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/helpers@npm:7.22.15"
  dependencies:
    "@babel/template": ^7.22.15
    "@babel/traverse": ^7.22.15
    "@babel/types": ^7.22.15
  checksum: 49f61a93cbae4df3328bda67af5db743fead659ae4242571226c3596b7df78546189cdf991fed1eca33b559de8abf396a90a001f474a1bab351418f07b7ae6ef
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4, @babel/highlight@npm:^7.22.13":
  version: 7.22.13
  resolution: "@babel/highlight@npm:7.22.13"
  dependencies:
    "@babel/helper-validator-identifier": ^7.22.5
    chalk: ^2.4.2
    js-tokens: ^4.0.0
  checksum: 7266d2bff8aa8fc78eb65b6e92a8211e12897a731126a282d2f9bb50d8fcaa4c1b02af2284f990ac7e3ab8d892d448a2cab8f5ed0ea8a90bce2c025b11ebe802
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.20.15, @babel/parser@npm:^7.21.3, @babel/parser@npm:^7.22.15, @babel/parser@npm:^7.22.16":
  version: 7.22.16
  resolution: "@babel/parser@npm:7.22.16"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 944c756b5bdeb07b9fec16ecef6b3c61aff9d4c4b924abadcf01afa1840a740b8e2357ae00482b5b37daad6d2bfd848c947f27ad65138d687b6fdc924bc59edd
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 8910ca21a7ec7c06f7b247d4b86c97c5aa15ef321518f44f6f490c5912fdf82c605aaa02b90892e375d82ccbedeadfdeadd922c1b836c9dd4c596871bf654753
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-transform-optional-chaining": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: fbefedc0da014c37f1a50a8094ce7dbbf2181ae93243f23d6ecba2499b5b20196c2124d6a4dfe3e9e0125798e80593103e456352a4beb4e5c6f7c75efb80fdac
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.12.13":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 49a78a2773ec0db56e915d9797e44fd079ab8a9b2e1716e0df07c92532f2c65d76aeda9543883916b8e0ff13606afeffa67c5b93d05b607bc87653ad18a91422
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.12.13":
  version: 7.22.15
  resolution: "@babel/plugin-proposal-decorators@npm:7.22.15"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/plugin-syntax-decorators": ^7.22.10
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbe8ebf46fa21035db88b751bc4b7becaf3ac87ad2f8d82838794b7396d4c9b198a3906f2409ea063c08834292a15b048cfaf38dea609939840dc53f32ac0e47
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.22.10":
  version: 7.22.10
  resolution: "@babel/plugin-syntax-decorators@npm:7.22.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: baaa10fa52d76ee8b9447f7aedb1c8df7cf2ef83ae29c085c07444e691685aa8b1a326dfb7a3a0e3ae4d5f9fd083175e46ea5e2316d8200f0278f3fd54a58696
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b8b5572db04a7bef1e6cd20debf447e4eef7cb012616f5eceb8fa3e23ce469b8f76ee74fd6d1e158ba17a8f58b0aec579d092fb67c5a30e83ccfbc5754916c1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 197b3c5ea2a9649347f033342cb222ab47f4645633695205c0250c6bf2af29e643753b8bb24a2db39948bef08e7c540babfd365591eb57fc110cb30b425ffc47
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.12.13, @babel/plugin-syntax-jsx@npm:^7.2.0, @babel/plugin-syntax-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8829d30c2617ab31393d99cec2978e41f014f4ac6f01a1cecf4c4dd8320c3ec12fdc3ce121126b2d8d32f6887e99ca1a0bad53dedb1e6ad165640b92b24980ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35abb6c57062802c7ce8bd96b2ef2883e3124370c688bbd67609f7d2453802fb73944df8808f893b6c67de978eb2bcf87bbfe325e46d6f39b5fcb09ece11d01a
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.22.15"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.9
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fad98786b446ce63bde0d14a221e2617eef5a7bbca62b49d96f16ab5e1694521234cfba6145b830fbf9af16d60a8a3dbf148e8694830bd91796fe333b0599e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.22.5"
  dependencies:
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b95f23f99dcb379a9f0a1c2a3bbea3f8dc0e1b16dc1ac8b484fe378370169290a7a63d520959a9ba1232837cf74a80e23f6facbe14fd42a3cda6d3c2d7168e62
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 416b1341858e8ca4e524dee66044735956ced5f478b2c3b9bc11ec2285b0c25d7dbb96d79887169eb938084c95d0a89338c8b2fe70d473bd9dc92e5d9db1732c
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-block-scoping@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c7091dc000b854ce0c471588ca0704ef1ce78cff954584a9f21c1668fd0669e7c8d5396fb72fe49a2216d9b96a400d435f424f27e41a097ef6c855f9c57df195
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-class-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b830152dfc2ff2f647f0abe76e6251babdfbef54d18c4b2c73a6bf76b1a00050a5d998dac80dc901a48514e95604324943a9dd39317073fe0928b559e0e0c579
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-class-static-block@npm:7.22.11"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.11
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 69f040506fad66f1c6918d288d0e0edbc5c8a07c8b4462c1184ad2f9f08995d68b057126c213871c0853ae0c72afc60ec87492049dfacb20902e32346a448bcb
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-classes@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-split-export-declaration": ^7.22.6
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d3f4d0c107dd8a3557ea3575cc777fab27efa92958b41e4a9822f7499725c1f554beae58855de16ddec0a7b694e45f59a26cea8fbde4275563f72f09c6e039a0
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-computed-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/template": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c2a77a0f94ec71efbc569109ec14ea2aa925b333289272ced8b33c6108bdbb02caf01830ffc7e49486b62dec51911924d13f3a76f1149f40daace1898009e131
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-destructuring@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4bccb4765e5287f1d36119d930afb9941ea8f4f001bddb8febff716bac0e09dc58576624f3ec59470630513044dd342075fe11af16d8c1b234cb7406cffca9f0
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 409b658d11e3082c8f69e9cdef2d96e4d6d11256f005772425fb230cc48fd05945edbfbcb709dab293a1a2f01f9c8a5bb7b4131e632b23264039d9f95864b453
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bb1280fbabaab6fab2ede585df34900712698210a3bd413f4df5bae6d8c24be36b496c92722ae676a7a67d060a4624f4d6c23b923485f906bfba8773c69f55b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 78fc9c532210bf9e8f231747f542318568ac360ee6c27e80853962c984283c73da3f8f8aebe83c2096090a435b356b092ed85de617a156cbe0729d847632be45
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.22.5"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f2d660c1b1d51ad5fec1cd5ad426a52187204068c4158f8c4aa977b31535c61b66898d532603eef21c15756827be8277f724c869b888d560f26d7fe848bb5eae
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 73af5883a321ed56a4bfd43c8a7de0164faebe619287706896fc6ee2f7a4e69042adaa1338c0b8b4bdb9f7e5fdceb016fb1d40694cb43ca3b8827429e8aac4bf
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-for-of@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f395ae7bce31e14961460f56cf751b5d6e37dd27d7df5b1f4e49fec1c11b6f9cf71991c7ffbe6549878591e87df0d66af798cf26edfa4bfa6b4c3dba1fb2f73a
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-function-name@npm:7.22.5"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cff3b876357999cb8ae30e439c3ec6b0491a53b0aa6f722920a4675a6dd5b53af97a833051df4b34791fe5b3dd326ccf769d5c8e45b322aa50ee11a660b17845
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-json-strings@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 50665e5979e66358c50e90a26db53c55917f78175127ac2fa05c7888d156d418ffb930ec0a109353db0a7c5f57c756ce01bfc9825d24cbfd2b3ec453f2ed8cba
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec37cc2ffb32667af935ab32fe28f00920ec8a1eb999aa6dc6602f2bebd8ba205a558aeedcdccdebf334381d5c57106c61f52332045730393e73410892a9735b
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c664e9798e85afa7f92f07b867682dee7392046181d82f5d21bae6f2ca26dfe9c8375cdc52b7483c3fc09a983c1989f60eff9fbc4f373b0c0a74090553d05739
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec4b0e07915ddd4fda0142fd104ee61015c208608a84cfa13643a95d18760b1dc1ceb6c6e0548898b8c49e5959a994e46367260176dbabc4467f729b21868504
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-amd@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7da4c4ebbbcf7d182abb59b2046b22d86eee340caf8a22a39ef6a727da2d8acfec1f714fcdcd5054110b280e4934f735e80a6848d192b6834c5d4459a014f04d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.22.15"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f8fc85fefa6be8626a378ca38fb84c7359043e7c692c854e9ee250a05121553b7f4a58e127099efe12662ec6bebbfd304ce638a0b4563d7cbd5982f3d877321c
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.22.11"
  dependencies:
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-module-transforms": ^7.22.9
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d0991e4bdc3352b6a9f4d12b6662e3645d892cd5c3c005ba5f14e65f1e218c6a8f7f4497e64a51d82a046e507aaa7db3143b800b0270dca1824cbd214ff3363d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-umd@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46622834c54c551b231963b867adbc80854881b3e516ff29984a8da989bd81665bd70e8cba6710345248e97166689310f544aee1a5773e262845a8f1b3e5b8b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3ee564ddee620c035b928fdc942c5d17e9c4b98329b76f9cefac65c111135d925eb94ed324064cd7556d4f5123beec79abea1d4b97d1c8a2a5c748887a2eb623
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-new-target@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b72112773487a881a1d6ffa680afde08bad699252020e86122180ee7a88854d5da3f15d9bca3331cf2e025df045604494a8208a2e63b486266b07c14e2ffbf3
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 167babecc8b8fe70796a7b7d34af667ebbf43da166c21689502e5e8cc93180b7a85979c77c9f64b7cce431b36718bd0a6df9e5e0ffea4ae22afb22cfef886372
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: af064d06a4a041767ec396a5f258103f64785df290e038bba9f0ef454e6c914f2ac45d862bbdad8fac2c7ad47fa4e95356f29053c60c100a0160b02a995fe2a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.22.15"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.22.15
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 62197a6f12289c1c1bd57f3bed9f0f765ca32390bfe91e0b5561dd94dd9770f4480c4162dec98da094bc0ba99d2c2ebba68de47c019454041b0b7a68ba2ec66d
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-object-super@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b71887877d74cb64dbccb5c0324fa67e31171e6a5311991f626650e44a4083e5436a1eaa89da78c0474fb095d4ec322d63ee778b202d33aa2e4194e1ed8e62d7
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.22.11"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f17abd90e1de67c84d63afea29c8021c74abb2794d3a6eeafb0bbe7372d3db32aefca386e392116ec63884537a4a2815d090d26264d259bacc08f6e3ed05294c
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b97abe0e50ca2dd8684fcef2c8d12607637e707aa9d513b7035f5e812efbde9305736b438d422103a7844e04124cad5efa4ff0e6226a57afa1210a1c7485c8e
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-parameters@npm:7.22.15"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 541188bb7d1876cad87687b5c7daf90f63d8208ae83df24acb1e2b05020ad1c78786b2723ca4054a83fcb74fb6509f30c4cacc5b538ee684224261ad5fb047c1
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-private-methods@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 321479b4fcb6d3b3ef622ab22fd24001e43d46e680e8e41324c033d5810c84646e470f81b44cbcbef5c22e99030784f7cac92f1829974da7a47a60a7139082c3
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.22.11":
  version: 7.22.11
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.22.11"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-create-class-features-plugin": ^7.22.11
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d029d84901e53c46dead7a46e2990a7bc62470f4e4ca58a0d063394f86652fd58fe4eea1eb941da3669cd536b559b9d058b342b59300026346b7a2a51badac8
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-property-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 796176a3176106f77fcb8cd04eb34a8475ce82d6d03a88db089531b8f0453a2fb8b0c6ec9a52c27948bc0ea478becec449893741fc546dfc3930ab927e3f9f2e
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.22.10":
  version: 7.22.10
  resolution: "@babel/plugin-transform-regenerator@npm:7.22.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    regenerator-transform: ^0.15.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e13678d62d6fa96f11cb8b863f00e8693491e7adc88bfca3f2820f80cbac8336e7dec3a596eee6a1c4663b7ececc3564f2cd7fb44ed6d4ce84ac2bb7f39ecc6e
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-reserved-words@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3ffd7dbc425fe8132bfec118b9817572799cab1473113a635d25ab606c1f5a2341a636c04cf6b22df3813320365ed5a965b5eeb3192320a10e4cc2c137bd8bfc
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.12.15":
  version: 7.22.15
  resolution: "@babel/plugin-transform-runtime@npm:7.22.15"
  dependencies:
    "@babel/helper-module-imports": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    babel-plugin-polyfill-corejs2: ^0.4.5
    babel-plugin-polyfill-corejs3: ^0.8.3
    babel-plugin-polyfill-regenerator: ^0.5.2
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7edf20b13d02f856276221624abf3b8084daa3f265a6e5c70ee0d0c63087fcf726dc8756a9c8bb3d25a1ce8697ab66ec8cdd15be992c21aed9971cb5bfe80a5b
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a5ac902c56ea8effa99f681340ee61bac21094588f7aef0bc01dff98246651702e677552fa6d10e548c4ac22a3ffad047dd2f8c8f0540b68316c2c203e56818b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-spread@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5587f0deb60b3dfc9b274e269031cc45ec75facccf1933ea2ea71ced9fd3ce98ed91bb36d6cd26817c14474b90ed998c5078415f0eab531caf301496ce24c95c
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 63b2c575e3e7f96c32d52ed45ee098fb7d354b35c2223b8c8e76840b32cc529ee0c0ceb5742fd082e56e91e3d82842a367ce177e82b05039af3d602c9627a729
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-template-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 27e9bb030654cb425381c69754be4abe6a7c75b45cd7f962cd8d604b841b2f0fb7b024f2efc1c25cc53f5b16d79d5e8cfc47cacbdaa983895b3aeefa3e7e24ff
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 82a53a63ffc3010b689ca9a54e5f53b2718b9f4b4a9818f36f9b7dba234f38a01876680553d2716a645a61920b5e6e4aaf8d4a0064add379b27ca0b403049512
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.22.10":
  version: 7.22.10
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.22.10"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 807f40ed1324c8cb107c45358f1903384ca3f0ef1d01c5a3c5c9b271c8d8eec66936a3dcc8d75ddfceea9421420368c2e77ae3adef0a50557e778dfe296bf382
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2495e5f663cb388e3d888b4ba3df419ac436a5012144ac170b622ddfc221f9ea9bdba839fa2bc0185cb776b578030666406452ec7791cbf0e7a3d4c88ae9574c
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b5d1404c8c623b0ec9bd436c00d885a17d6a34f3f2597996343ddb9d94f6379705b21582dfd4cec2c47fd34068872e74ab6b9580116c0566b3f9447e2a7fa06
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c042070f980b139547f8b0179efbc049ac5930abec7fc26ed7a41d89a048d8ab17d362200e204b6f71c3c20d6991a0e74415e1a412a49adc8131c2a40c04822e
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.12.16":
  version: 7.22.15
  resolution: "@babel/preset-env@npm:7.22.15"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.15
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.15
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.22.15
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.22.15
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-import-assertions": ^7.22.5
    "@babel/plugin-syntax-import-attributes": ^7.22.5
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.22.5
    "@babel/plugin-transform-async-generator-functions": ^7.22.15
    "@babel/plugin-transform-async-to-generator": ^7.22.5
    "@babel/plugin-transform-block-scoped-functions": ^7.22.5
    "@babel/plugin-transform-block-scoping": ^7.22.15
    "@babel/plugin-transform-class-properties": ^7.22.5
    "@babel/plugin-transform-class-static-block": ^7.22.11
    "@babel/plugin-transform-classes": ^7.22.15
    "@babel/plugin-transform-computed-properties": ^7.22.5
    "@babel/plugin-transform-destructuring": ^7.22.15
    "@babel/plugin-transform-dotall-regex": ^7.22.5
    "@babel/plugin-transform-duplicate-keys": ^7.22.5
    "@babel/plugin-transform-dynamic-import": ^7.22.11
    "@babel/plugin-transform-exponentiation-operator": ^7.22.5
    "@babel/plugin-transform-export-namespace-from": ^7.22.11
    "@babel/plugin-transform-for-of": ^7.22.15
    "@babel/plugin-transform-function-name": ^7.22.5
    "@babel/plugin-transform-json-strings": ^7.22.11
    "@babel/plugin-transform-literals": ^7.22.5
    "@babel/plugin-transform-logical-assignment-operators": ^7.22.11
    "@babel/plugin-transform-member-expression-literals": ^7.22.5
    "@babel/plugin-transform-modules-amd": ^7.22.5
    "@babel/plugin-transform-modules-commonjs": ^7.22.15
    "@babel/plugin-transform-modules-systemjs": ^7.22.11
    "@babel/plugin-transform-modules-umd": ^7.22.5
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.22.5
    "@babel/plugin-transform-new-target": ^7.22.5
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.22.11
    "@babel/plugin-transform-numeric-separator": ^7.22.11
    "@babel/plugin-transform-object-rest-spread": ^7.22.15
    "@babel/plugin-transform-object-super": ^7.22.5
    "@babel/plugin-transform-optional-catch-binding": ^7.22.11
    "@babel/plugin-transform-optional-chaining": ^7.22.15
    "@babel/plugin-transform-parameters": ^7.22.15
    "@babel/plugin-transform-private-methods": ^7.22.5
    "@babel/plugin-transform-private-property-in-object": ^7.22.11
    "@babel/plugin-transform-property-literals": ^7.22.5
    "@babel/plugin-transform-regenerator": ^7.22.10
    "@babel/plugin-transform-reserved-words": ^7.22.5
    "@babel/plugin-transform-shorthand-properties": ^7.22.5
    "@babel/plugin-transform-spread": ^7.22.5
    "@babel/plugin-transform-sticky-regex": ^7.22.5
    "@babel/plugin-transform-template-literals": ^7.22.5
    "@babel/plugin-transform-typeof-symbol": ^7.22.5
    "@babel/plugin-transform-unicode-escapes": ^7.22.10
    "@babel/plugin-transform-unicode-property-regex": ^7.22.5
    "@babel/plugin-transform-unicode-regex": ^7.22.5
    "@babel/plugin-transform-unicode-sets-regex": ^7.22.5
    "@babel/preset-modules": 0.1.6-no-external-plugins
    "@babel/types": ^7.22.15
    babel-plugin-polyfill-corejs2: ^0.4.5
    babel-plugin-polyfill-corejs3: ^0.8.3
    babel-plugin-polyfill-regenerator: ^0.5.2
    core-js-compat: ^3.31.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c3cf0223cab006cbf0c563a49a5076caa0b62e3b61b4f10ba857347fcd4f85dbb662a78e6f289e4f29f72c36974696737ae86c23da114617f5b00ab2c1c66126
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: 89c338fee774770e5a487382170711014d49a68eb281e74f2b5eac88f38300a4ad545516a7786a8dd5702e9cf009c94c2f582d200f077ac5decd74c56b973730
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.13, @babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.8.4":
  version: 7.22.15
  resolution: "@babel/runtime@npm:7.22.15"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 793296df1e41599a935a3d77ec01eb6088410d3fd4dbe4e92f06c6b7bb2f8355024e6d78621a3a35f44e0e23b0b59107f23d585384df4f3123256a1e1492040e
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.15, @babel/template@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/template@npm:7.22.15"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/parser": ^7.22.15
    "@babel/types": ^7.22.15
  checksum: 1f3e7dcd6c44f5904c184b3f7fe280394b191f2fed819919ffa1e529c259d5b197da8981b6ca491c235aee8dbad4a50b7e31304aa531271cb823a4a24a0dd8fd
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.22.15, @babel/traverse@npm:^7.22.17, @babel/traverse@npm:^7.22.5":
  version: 7.22.17
  resolution: "@babel/traverse@npm:7.22.17"
  dependencies:
    "@babel/code-frame": ^7.22.13
    "@babel/generator": ^7.22.15
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/parser": ^7.22.16
    "@babel/types": ^7.22.17
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: 1153ca166a0a9b3fddf67f7f7c8c5b4f88aa2c2c00261ff2fc8424a63bc93250ed3fd08b04bd526ad19e797aeb6f22161120646a570cbfe5ff2a5d2f5d28af01
  languageName: node
  linkType: hard

"@babel/types@npm:^7.22.15, @babel/types@npm:^7.22.17, @babel/types@npm:^7.22.5, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.22.17
  resolution: "@babel/types@npm:7.22.17"
  dependencies:
    "@babel/helper-string-parser": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.15
    to-fast-properties: ^2.0.0
  checksum: 7382220f6eb2548f2c867a98916c3aa8a6063498d5372e5d21d8d184ba354033defb72aeba5858c1b2b42177058b896a34a7dcbae5eccd47fb0104721efa909d
  languageName: node
  linkType: hard

"@css-render/plugin-bem@npm:^0.15.10":
  version: 0.15.12
  resolution: "@css-render/plugin-bem@npm:0.15.12"
  peerDependencies:
    css-render: ~0.15.12
  checksum: 9fa7ddd62b19beefa1280d731bc45f26f016e0f4f4535025247b4de831b3a37f72f7eaa7098c10fac784a5f1eb723078ee293068e77c04e6e40953f260e4fc14
  languageName: node
  linkType: hard

"@css-render/vue3-ssr@npm:^0.15.10":
  version: 0.15.12
  resolution: "@css-render/vue3-ssr@npm:0.15.12"
  peerDependencies:
    vue: ^3.0.11
  checksum: a5505ae1619827dd2f2d1e294f58c927e8e292c93aa6750ffcb8690cc57e797a01c23c828dd4c9a3bde3add500c2385bd85905bb7f3280c76be2010f1c15537d
  languageName: node
  linkType: hard

"@dealer/app-prompts@workspace:.":
  version: 0.0.0-use.local
  resolution: "@dealer/app-prompts@workspace:."
  dependencies:
    "@babel/core": ^7.12.16
    "@babel/eslint-parser": ^7.12.16
    "@vue/cli-plugin-babel": ~5.0.0
    "@vue/cli-plugin-eslint": ~5.0.0
    "@vue/cli-service": ~5.0.0
    core-js: ^3.8.3
    eslint: ^7.32.0
    eslint-plugin-vue: ^8.0.3
    naive-ui: ^2.34.4
    sass: ^1.64.0
    sass-loader: ^13.3.2
    single-spa-vue: ^2.1.0
    vue: ^3.2.13
    vue-cli-plugin-single-spa: ~3.3.0
    vue-router: next
  languageName: unknown
  linkType: soft

"@discoveryjs/json-ext@npm:0.5.7":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@emotion/hash@npm:~0.8.0":
  version: 0.8.0
  resolution: "@emotion/hash@npm:0.8.0"
  checksum: 4b35d88a97e67275c1d990c96d3b0450451d089d1508619488fc0acb882cb1ac91e93246d471346ebd1b5402215941ef4162efe5b51534859b39d8b3a0e3ffaa
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^0.4.3":
  version: 0.4.3
  resolution: "@eslint/eslintrc@npm:0.4.3"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.1.1
    espree: ^7.3.0
    globals: ^13.9.0
    ignore: ^4.0.6
    import-fresh: ^3.2.1
    js-yaml: ^3.13.1
    minimatch: ^3.0.4
    strip-json-comments: ^3.1.1
  checksum: 03a7704150b868c318aab6a94d87a33d30dc2ec579d27374575014f06237ba1370ae11178db772f985ef680d469dc237e7b16a1c5d8edaaeb8c3733e7a95a6d3
  languageName: node
  linkType: hard

"@hapi/hoek@npm:^9.0.0":
  version: 9.3.0
  resolution: "@hapi/hoek@npm:9.3.0"
  checksum: 4771c7a776242c3c022b168046af4e324d116a9d2e1d60631ee64f474c6e38d1bb07092d898bf95c7bc5d334c5582798a1456321b2e53ca817d4e7c88bc25b43
  languageName: node
  linkType: hard

"@hapi/topo@npm:^5.0.0":
  version: 5.1.0
  resolution: "@hapi/topo@npm:5.1.0"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: 604dfd5dde76d5c334bd03f9001fce69c7ce529883acf92da96f4fe7e51221bf5e5110e964caca287a6a616ba027c071748ab636ff178ad750547fba611d6014
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.5.0":
  version: 0.5.0
  resolution: "@humanwhocodes/config-array@npm:0.5.0"
  dependencies:
    "@humanwhocodes/object-schema": ^1.2.0
    debug: ^4.1.1
    minimatch: ^3.0.4
  checksum: 44ee6a9f05d93dd9d5935a006b17572328ba9caff8002442f601736cbda79c580cc0f5a49ce9eb88fbacc5c3a6b62098357c2e95326cd17bb9f1a6c61d6e95e7
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.0":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: a824a1ec31591231e4bad5787641f59e9633827d0a2eaae131a288d33c9ef0290bd16fda8da6f7c0fcb014147865d12118df10db57f27f41e20da92369fcb3f1
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.2":
  version: 0.3.3
  resolution: "@jridgewell/gen-mapping@npm:0.3.3"
  dependencies:
    "@jridgewell/set-array": ^1.0.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 4a74944bd31f22354fc01c3da32e83c19e519e3bbadafa114f6da4522ea77dd0c2842607e923a591d60a76699d819a2fbb6f3552e277efdb9b58b081390b60ab
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.1
  resolution: "@jridgewell/resolve-uri@npm:3.1.1"
  checksum: f5b441fe7900eab4f9155b3b93f9800a916257f4e8563afbcd3b5a5337b55e52bd8ae6735453b1b745457d9f6cdb16d74cd6220bbdd98cf153239e13f6cbb653
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.5
  resolution: "@jridgewell/source-map@npm:0.3.5"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 1ad4dec0bdafbade57920a50acec6634f88a0eb735851e0dda906fa9894e7f0549c492678aad1a10f8e144bfe87f238307bf2a914a1bc85b7781d345417e9f6f
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.4.15":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: b881c7e503db3fc7f3c1f35a1dd2655a188cc51a3612d76efc8a6eb74728bef5606e6758ee77423e564092b4a518aba569bbb21c9bac5ab7a35b0c6ae7e344c8
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.17, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.19
  resolution: "@jridgewell/trace-mapping@npm:0.3.19"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 956a6f0f6fec060fb48c6bf1f5ec2064e13cd38c8be3873877d4b92b4a27ba58289a34071752671262a3e3c202abcc3fa2aac64d8447b4b0fa1ba3c9047f1c20
  languageName: node
  linkType: hard

"@juggle/resize-observer@npm:^3.3.1":
  version: 3.4.0
  resolution: "@juggle/resize-observer@npm:3.4.0"
  checksum: 2505028c05cc2e17639fcad06218b1c4b60f932a4ebb4b41ab546ef8c157031ae377e3f560903801f6d01706dbefd4943b6c4704bf19ed86dfa1c62f1473a570
  languageName: node
  linkType: hard

"@leichtgewicht/ip-codec@npm:^2.0.1":
  version: 2.0.4
  resolution: "@leichtgewicht/ip-codec@npm:2.0.4"
  checksum: 468de1f04d33de6d300892683d7c8aecbf96d1e2c5fe084f95f816e50a054d45b7c1ebfb141a1447d844b86a948733f6eebd92234da8581c84a1ad4de2946a2d
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: 5.1.1
  checksum: f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@node-ipc/js-queue@npm:2.0.3":
  version: 2.0.3
  resolution: "@node-ipc/js-queue@npm:2.0.3"
  dependencies:
    easy-stack: 1.0.1
  checksum: 0f79768a81b99353460fd8e13e541691fb73177899ffba2c61edd24c262176d7be5ad575c84de7c104523151c6bbd3bd6d3bb7b0d57ba83845df38d2cf008002
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: ^7.3.5
  checksum: a50a6818de5fc557d0b0e6f50ec780a7a02ab8ad07e5ac8b16bf519e0ad60a144ac64f97d05c443c3367235d337182e1d012bbac0eb8dbae8dc7b40b193efd0e
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.20":
  version: 1.0.0-next.23
  resolution: "@polka/url@npm:1.0.0-next.23"
  checksum: 4b0330de1ceecd1002c7e7449094d0c41f2ed0e21765f4835ccc7b003f2f024ac557d503b9ffdf0918cf50b80d5b8c99dfc5a91927e7b3c468b09c6bb42a3c41
  languageName: node
  linkType: hard

"@sideway/address@npm:^4.1.3":
  version: 4.1.4
  resolution: "@sideway/address@npm:4.1.4"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: b9fca2a93ac2c975ba12e0a6d97853832fb1f4fb02393015e012b47fa916a75ca95102d77214b2a29a2784740df2407951af8c5dde054824c65577fd293c4cdb
  languageName: node
  linkType: hard

"@sideway/formula@npm:^3.0.1":
  version: 3.0.1
  resolution: "@sideway/formula@npm:3.0.1"
  checksum: e4beeebc9dbe2ff4ef0def15cec0165e00d1612e3d7cea0bc9ce5175c3263fc2c818b679bd558957f49400ee7be9d4e5ac90487e1625b4932e15c4aa7919c57a
  languageName: node
  linkType: hard

"@sideway/pinpoint@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sideway/pinpoint@npm:2.0.0"
  checksum: 0f4491e5897fcf5bf02c46f5c359c56a314e90ba243f42f0c100437935daa2488f20482f0f77186bd6bf43345095a95d8143ecf8b1f4d876a7bc0806aba9c3d2
  languageName: node
  linkType: hard

"@soda/friendly-errors-webpack-plugin@npm:^1.8.0":
  version: 1.8.1
  resolution: "@soda/friendly-errors-webpack-plugin@npm:1.8.1"
  dependencies:
    chalk: ^3.0.0
    error-stack-parser: ^2.0.6
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 397f31580f088460472abbb61a49551d4ff60c0bf949f3cb4667e013a3b7f4575aa2b5a7dcf8a3d5a3862f043eef93a19ad60fd3f4b7cfd0cc8128d95a202479
  languageName: node
  linkType: hard

"@soda/get-current-script@npm:^1.0.2":
  version: 1.0.2
  resolution: "@soda/get-current-script@npm:1.0.2"
  checksum: 60c13475db06e2ece83df4c0f64bf3ca4f69f5fcd462512f1be2f5b6e5c3e1dbd75b0209cc2b21bb3ff9bc5fefd7ba60c2626cdda2f7067cffe985d9af439622
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.2
  resolution: "@types/body-parser@npm:1.19.2"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: e17840c7d747a549f00aebe72c89313d09fbc4b632b949b2470c5cb3b1cb73863901ae84d9335b567a79ec5efcfb8a28ff8e3f36bc8748a9686756b6d5681f40
  languageName: node
  linkType: hard

"@types/bonjour@npm:^3.5.9":
  version: 3.5.10
  resolution: "@types/bonjour@npm:3.5.10"
  dependencies:
    "@types/node": "*"
  checksum: bfcadb042a41b124c4e3de4925e3be6d35b78f93f27c4535d5ff86980dc0f8bc407ed99b9b54528952dc62834d5a779392f7a12c2947dd19330eb05a6bcae15a
  languageName: node
  linkType: hard

"@types/connect-history-api-fallback@npm:^1.3.5":
  version: 1.5.1
  resolution: "@types/connect-history-api-fallback@npm:1.5.1"
  dependencies:
    "@types/express-serve-static-core": "*"
    "@types/node": "*"
  checksum: bc5e46663300eba56eaf8ef242156256e2bdadc52bb7d6543f4b37f2945b6a810901c245711f8321fce7d94c7b588b308a86519f3e1f87a80eb87841d808dbdc
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.36
  resolution: "@types/connect@npm:3.4.36"
  dependencies:
    "@types/node": "*"
  checksum: 4dee3d966fb527b98f0cbbdcf6977c9193fc3204ed539b7522fe5e64dfa45f9017bdda4ffb1f760062262fce7701a0ee1c2f6ce2e50af36c74d4e37052303172
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.3":
  version: 3.7.4
  resolution: "@types/eslint-scope@npm:3.7.4"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: ea6a9363e92f301cd3888194469f9ec9d0021fe0a397a97a6dd689e7545c75de0bd2153dfb13d3ab532853a278b6572c6f678ce846980669e41029d205653460
  languageName: node
  linkType: hard

"@types/eslint@npm:*, @types/eslint@npm:^7.29.0 || ^8.4.1":
  version: 8.44.2
  resolution: "@types/eslint@npm:8.44.2"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 25b3ef61bae96350026593c9914c8a61ee02fde48ab8d568a73ee45032f13c0028c62e47a5ff78715af488dfe8e8bba913f7d30f859f60c7f9e639d328e80482
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0":
  version: 1.0.1
  resolution: "@types/estree@npm:1.0.1"
  checksum: e9aa175eacb797216fafce4d41e8202c7a75555bc55232dee0f9903d7171f8f19f0ae7d5191bb1a88cb90e65468be508c0df850a9fb81b4433b293a5a749899d
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:*, @types/express-serve-static-core@npm:^4.17.33":
  version: 4.17.36
  resolution: "@types/express-serve-static-core@npm:4.17.36"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: 410b13cbd663f18c0f8729e7f2ff54d960d96de76ebbae7cadb612972f85cc66c54051e00d32f11aa230c0a683d81a6d6fc7f7e4e383a95c0801494c517f36e1
  languageName: node
  linkType: hard

"@types/express@npm:*, @types/express@npm:^4.17.13":
  version: 4.17.17
  resolution: "@types/express@npm:4.17.17"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^4.17.33
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: 0196dacc275ac3ce89d7364885cb08e7fb61f53ca101f65886dbf1daf9b7eb05c0943e2e4bbd01b0cc5e50f37e0eea7e4cbe97d0304094411ac73e1b7998f4da
  languageName: node
  linkType: hard

"@types/html-minifier-terser@npm:^6.0.0":
  version: 6.1.0
  resolution: "@types/html-minifier-terser@npm:6.1.0"
  checksum: eb843f6a8d662d44fb18ec61041117734c6aae77aa38df1be3b4712e8e50ffaa35f1e1c92fdd0fde14a5675fecf457abcd0d15a01fae7506c91926176967f452
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.1
  resolution: "@types/http-errors@npm:2.0.1"
  checksum: 3bb0c50b0a652e679a84c30cd0340d696c32ef6558518268c238840346c077f899315daaf1c26c09c57ddd5dc80510f2a7f46acd52bf949e339e35ed3ee9654f
  languageName: node
  linkType: hard

"@types/http-proxy@npm:^1.17.8":
  version: 1.17.11
  resolution: "@types/http-proxy@npm:1.17.11"
  dependencies:
    "@types/node": "*"
  checksum: 38ef4f8c91c7a5b664cf6dd4d90de7863f88549a9f8ef997f2f1184e4f8cf2e7b9b63c04f0b7b962f34a09983073a31a9856de5aae5159b2ddbb905a4c44dc9f
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.12
  resolution: "@types/json-schema@npm:7.0.12"
  checksum: 00239e97234eeb5ceefb0c1875d98ade6e922bfec39dd365ec6bd360b5c2f825e612ac4f6e5f1d13601b8b30f378f15e6faa805a3a732f4a1bbe61915163d293
  languageName: node
  linkType: hard

"@types/katex@npm:^0.14.0":
  version: 0.14.0
  resolution: "@types/katex@npm:0.14.0"
  checksum: 330e0d0337ba48c87f5b793965fbad673653789bf6e50dfe8d726a7b0cbefd37195055e31503aae629814aa79447e4f23a4b87ad1ac565c0d9a9d9978836f39b
  languageName: node
  linkType: hard

"@types/lodash-es@npm:^4.17.6":
  version: 4.17.9
  resolution: "@types/lodash-es@npm:4.17.9"
  dependencies:
    "@types/lodash": "*"
  checksum: a2c7ab8da13892f89ab86b61dd9a0125c767b9db7b9bdaac7fd8508c9fa884bbb19a9760dbdc1e7e698e3322b7a8ba03daa80832371da32a8bd42da0920be838
  languageName: node
  linkType: hard

"@types/lodash@npm:*, @types/lodash@npm:^4.14.181":
  version: 4.14.198
  resolution: "@types/lodash@npm:4.14.198"
  checksum: b290e4480707151bcec738bca40527915defe52a0d8e26c83685c674163a265e1a88cb2ee56b0fb587a89819d0cd5df86ada836aec3e9c2e4bf516e7d348d524
  languageName: node
  linkType: hard

"@types/mime@npm:*":
  version: 3.0.1
  resolution: "@types/mime@npm:3.0.1"
  checksum: 4040fac73fd0cea2460e29b348c1a6173da747f3a87da0dbce80dd7a9355a3d0e51d6d9a401654f3e5550620e3718b5a899b2ec1debf18424e298a2c605346e7
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.2
  resolution: "@types/mime@npm:1.3.2"
  checksum: 0493368244cced1a69cb791b485a260a422e6fcc857782e1178d1e6f219f1b161793e9f87f5fae1b219af0f50bee24fcbe733a18b4be8fdd07a38a8fb91146fd
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.0":
  version: 1.2.2
  resolution: "@types/minimist@npm:1.2.2"
  checksum: b8da83c66eb4aac0440e64674b19564d9d86c80ae273144db9681e5eeff66f238ade9515f5006ffbfa955ceff8b89ad2bd8ec577d7caee74ba101431fb07045d
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.6.0
  resolution: "@types/node@npm:20.6.0"
  checksum: 52611801af5cf151c6fac1963aa4a8a8ca2e388a9e9ed82b01b70bca762088ded5b32cc789c5564220d5d7dccba2b8dd34446a3d4fc74736805e1f2cf262e29d
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.1
  resolution: "@types/normalize-package-data@npm:2.4.1"
  checksum: e87bccbf11f95035c89a132b52b79ce69a1e3652fe55962363063c9c0dae0fe2477ebc585e03a9652adc6f381d24ba5589cc5e51849df4ced3d3e004a7d40ed5
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "@types/parse-json@npm:4.0.0"
  checksum: fd6bce2b674b6efc3db4c7c3d336bd70c90838e8439de639b909ce22f3720d21344f52427f1d9e57b265fcb7f6c018699b99e5e0c208a1a4823014269a6bf35b
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.8
  resolution: "@types/qs@npm:6.9.8"
  checksum: c28e07d00d07970e5134c6eed184a0189b8a4649e28fdf36d9117fe671c067a44820890de6bdecef18217647a95e9c6aebdaaae69f5fe4b0bec9345db885f77e
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.4
  resolution: "@types/range-parser@npm:1.2.4"
  checksum: b7c0dfd5080a989d6c8bb0b6750fc0933d9acabeb476da6fe71d8bdf1ab65e37c136169d84148034802f48378ab94e3c37bb4ef7656b2bec2cb9c0f8d4146a95
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.0":
  version: 0.12.0
  resolution: "@types/retry@npm:0.12.0"
  checksum: 61a072c7639f6e8126588bf1eb1ce8835f2cb9c2aba795c4491cf6310e013267b0c8488039857c261c387e9728c1b43205099223f160bb6a76b4374f741b5603
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.1
  resolution: "@types/send@npm:0.17.1"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: 10b620a5960058ef009afbc17686f680d6486277c62f640845381ec4baa0ea683fdd77c3afea4803daf5fcddd3fb2972c8aa32e078939f1d4e96f83195c89793
  languageName: node
  linkType: hard

"@types/serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "@types/serve-index@npm:1.9.1"
  dependencies:
    "@types/express": "*"
  checksum: 026f3995fb500f6df7c3fe5009e53bad6d739e20b84089f58ebfafb2f404bbbb6162bbe33f72d2f2af32d5b8d3799c8e179793f90d9ed5871fb8591190bb6056
  languageName: node
  linkType: hard

"@types/serve-static@npm:*, @types/serve-static@npm:^1.13.10":
  version: 1.15.2
  resolution: "@types/serve-static@npm:1.15.2"
  dependencies:
    "@types/http-errors": "*"
    "@types/mime": "*"
    "@types/node": "*"
  checksum: 15c261dbfc57890f7cc17c04d5b22b418dfa0330c912b46c5d8ae2064da5d6f844ef7f41b63c7f4bbf07675e97ebe6ac804b032635ec742ae45d6f1274259b3e
  languageName: node
  linkType: hard

"@types/sockjs@npm:^0.3.33":
  version: 0.3.33
  resolution: "@types/sockjs@npm:0.3.33"
  dependencies:
    "@types/node": "*"
  checksum: b9bbb2b5c5ead2fb884bb019f61a014e37410bddd295de28184e1b2e71ee6b04120c5ba7b9954617f0bdf962c13d06249ce65004490889c747c80d3f628ea842
  languageName: node
  linkType: hard

"@types/ws@npm:^8.5.5":
  version: 8.5.5
  resolution: "@types/ws@npm:8.5.5"
  dependencies:
    "@types/node": "*"
  checksum: d00bf8070e6938e3ccf933010921c6ce78ac3606696ce37a393b27a9a603f7bd93ea64f3c5fa295a2f743575ba9c9a9fdb904af0f5fe2229bf2adf0630386e4a
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-jsx-merge-props@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-helper-vue-jsx-merge-props@npm:1.4.0"
  checksum: 2383ff7719b324da2330f7444472e6a2f0c8710d1ad0a1b5cccdda1c5a4160129d0e9db97e6404cc4a4ecb64a7e8510fde0bec381b3eb03103f814534f02b18d
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-transform-on@npm:^1.1.5":
  version: 1.1.5
  resolution: "@vue/babel-helper-vue-transform-on@npm:1.1.5"
  checksum: 19818def69879b8e2c9182a14dbb493f9c8bdb5f37ead41319a1105cb4a9c4490bb07af1eeaa5009024ddd6dc2d3738e60c0c35d2a43e8c80d803cde38c966bd
  languageName: node
  linkType: hard

"@vue/babel-plugin-jsx@npm:^1.0.3":
  version: 1.1.5
  resolution: "@vue/babel-plugin-jsx@npm:1.1.5"
  dependencies:
    "@babel/helper-module-imports": ^7.22.5
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/template": ^7.22.5
    "@babel/traverse": ^7.22.5
    "@babel/types": ^7.22.5
    "@vue/babel-helper-vue-transform-on": ^1.1.5
    camelcase: ^6.3.0
    html-tags: ^3.3.1
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0bd998d02c8bf6c646525065c8bd1b6e4c4c8ce29e331712643ff0f9db65e8abfe84ff425b7941ae062daa28c92ef7e93f3713d158ff5ed07f00d140d67f84b9
  languageName: node
  linkType: hard

"@vue/babel-plugin-transform-vue-jsx@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-plugin-transform-vue-jsx@npm:1.4.0"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-helper-vue-jsx-merge-props": ^1.4.0
    html-tags: ^2.0.0
    lodash.kebabcase: ^4.1.1
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c737ad3d277efeacc091686a0159d3c41d330feb4a74203daa488d4a56a51178d14839fb28e4ce6f0ba5e69b628dc4f8dd556e95ad7cb18b6d91d1be8e5c31d0
  languageName: node
  linkType: hard

"@vue/babel-preset-app@npm:^5.0.8":
  version: 5.0.8
  resolution: "@vue/babel-preset-app@npm:5.0.8"
  dependencies:
    "@babel/core": ^7.12.16
    "@babel/helper-compilation-targets": ^7.12.16
    "@babel/helper-module-imports": ^7.12.13
    "@babel/plugin-proposal-class-properties": ^7.12.13
    "@babel/plugin-proposal-decorators": ^7.12.13
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-jsx": ^7.12.13
    "@babel/plugin-transform-runtime": ^7.12.15
    "@babel/preset-env": ^7.12.16
    "@babel/runtime": ^7.12.13
    "@vue/babel-plugin-jsx": ^1.0.3
    "@vue/babel-preset-jsx": ^1.1.2
    babel-plugin-dynamic-import-node: ^2.3.3
    core-js: ^3.8.3
    core-js-compat: ^3.8.3
    semver: ^7.3.4
  peerDependencies:
    "@babel/core": "*"
    core-js: ^3
    vue: ^2 || ^3.2.13
  peerDependenciesMeta:
    core-js:
      optional: true
    vue:
      optional: true
  checksum: 313ce62a7ff66870a9a1e18d37bb2cc1b560e04d74023cdfd0d3f54b8e2df610b5250dd5d8f79e8da43395bdc1acc2902ab447858d457aea3dd67571e84103a0
  languageName: node
  linkType: hard

"@vue/babel-preset-jsx@npm:^1.1.2":
  version: 1.4.0
  resolution: "@vue/babel-preset-jsx@npm:1.4.0"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props": ^1.4.0
    "@vue/babel-plugin-transform-vue-jsx": ^1.4.0
    "@vue/babel-sugar-composition-api-inject-h": ^1.4.0
    "@vue/babel-sugar-composition-api-render-instance": ^1.4.0
    "@vue/babel-sugar-functional-vue": ^1.4.0
    "@vue/babel-sugar-inject-h": ^1.4.0
    "@vue/babel-sugar-v-model": ^1.4.0
    "@vue/babel-sugar-v-on": ^1.4.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
    vue: "*"
  peerDependenciesMeta:
    vue:
      optional: true
  checksum: 19fc3d8606f115c9e74013c3e17df42e51538056aad732b66b33d63febfba0a5ccc12cca9dc352c56ebf0605f3dd58abdd05c2055363fdd2424ad10818acfbb1
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-inject-h@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-composition-api-inject-h@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 52a106dd7bd6614cb9bda90a39d6ac175ce849d797096bde5bacc4053bdc25b2ed6483d5b18b4a46fa7d7b51a4ac5987dda8ec88a8e712c4ee51709e9cfb8e1e
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-render-instance@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-composition-api-render-instance@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b7281b11353df729f66da37df5a32936060cf3dc219234add164c524ab6164e4905610b4080ef9ee3dfc94b046d4eba456eb7b5ea21caf011b7ad00835c962a2
  languageName: node
  linkType: hard

"@vue/babel-sugar-functional-vue@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-functional-vue@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 93a0338bc390fd6a869dab009c1d6739fbfc305f49c6135cea1499437a7574a4868cf07d83a939e3fba38fc1f0243af53d899dbd696d1882f2e486abb3d49d4b
  languageName: node
  linkType: hard

"@vue/babel-sugar-inject-h@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-inject-h@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d8dadf55d7135838316372867d390a4d2208f44604bdbe578349a49ca52e27b10c685cc48ddd662678413670cb6a8a8307426838758a62d1d3d88cb90cfd2b63
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-model@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-v-model@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-helper-vue-jsx-merge-props": ^1.4.0
    "@vue/babel-plugin-transform-vue-jsx": ^1.4.0
    camelcase: ^5.0.0
    html-tags: ^2.0.0
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fa2ad5a05f25a4a42de0e5f477c1897dff916185aea993a5a3af3a3aa38f46bb44d7d4ee083dcf6baa63e8a7930e3f5f67dcd01ec37a498257a38d2c35723664
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-on@npm:^1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-sugar-v-on@npm:1.4.0"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-plugin-transform-vue-jsx": ^1.4.0
    camelcase: ^5.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 77c9e7a6bc93e66d0949f51b06d51bff17a08b5eb3072674413f90dcf6736ea13f38bc931566efe63444fffa137098d0397e20e66f8648949f6b860c9bbd8cd1
  languageName: node
  linkType: hard

"@vue/cli-overlay@npm:^5.0.8":
  version: 5.0.8
  resolution: "@vue/cli-overlay@npm:5.0.8"
  checksum: b76412ff811d0c1d60371c522ee689d310f10d1311199580ba484fef0189ba0748a07c1de933752975218858e569b6f0586056a0644df359047a1335cd7d1f73
  languageName: node
  linkType: hard

"@vue/cli-plugin-babel@npm:~5.0.0":
  version: 5.0.8
  resolution: "@vue/cli-plugin-babel@npm:5.0.8"
  dependencies:
    "@babel/core": ^7.12.16
    "@vue/babel-preset-app": ^5.0.8
    "@vue/cli-shared-utils": ^5.0.8
    babel-loader: ^8.2.2
    thread-loader: ^3.0.0
    webpack: ^5.54.0
  peerDependencies:
    "@vue/cli-service": ^3.0.0 || ^4.0.0 || ^5.0.0-0
  checksum: eb26555631e938791d2389f57f1f3de8aaf3b6d296879052354f4da6d54e80fdae71a1dab701de6a7b7de3c28f0550a14a2cc92638619b23a0fc6788d3ef18db
  languageName: node
  linkType: hard

"@vue/cli-plugin-eslint@npm:~5.0.0":
  version: 5.0.8
  resolution: "@vue/cli-plugin-eslint@npm:5.0.8"
  dependencies:
    "@vue/cli-shared-utils": ^5.0.8
    eslint-webpack-plugin: ^3.1.0
    globby: ^11.0.2
    webpack: ^5.54.0
    yorkie: ^2.0.0
  peerDependencies:
    "@vue/cli-service": ^3.0.0 || ^4.0.0 || ^5.0.0-0
    eslint: ">=7.5.0"
  checksum: 1b9d62e526c40c93279231ffa7f3546545926026bf884dc2f015ff24c7f753f9d3e8c6c00977df5302eaf9edcd6d17f58a3df092fb09521aa51d1e9cffe2aebc
  languageName: node
  linkType: hard

"@vue/cli-plugin-router@npm:^5.0.8":
  version: 5.0.8
  resolution: "@vue/cli-plugin-router@npm:5.0.8"
  dependencies:
    "@vue/cli-shared-utils": ^5.0.8
  peerDependencies:
    "@vue/cli-service": ^3.0.0 || ^4.0.0 || ^5.0.0-0
  checksum: 7bd2a6b9c45955a11e4495a591f8fa05ac5f824ef5c320642c3b39b8ffc1009e1415a39612038f664a7af896f80f43522f2fb5f6dbc22996b435c25b1bf3300f
  languageName: node
  linkType: hard

"@vue/cli-plugin-vuex@npm:^5.0.8":
  version: 5.0.8
  resolution: "@vue/cli-plugin-vuex@npm:5.0.8"
  peerDependencies:
    "@vue/cli-service": ^3.0.0 || ^4.0.0 || ^5.0.0-0
  checksum: 0a01dd490f9558e0beb2af132c05fd76288cd9b728f9b118bd0815b3bc6457510a824d9fd3469c2a99498a07d1a785d01d08a8ff8ecfc4e809efc2bf2ccf4ce4
  languageName: node
  linkType: hard

"@vue/cli-service@npm:~5.0.0":
  version: 5.0.8
  resolution: "@vue/cli-service@npm:5.0.8"
  dependencies:
    "@babel/helper-compilation-targets": ^7.12.16
    "@soda/friendly-errors-webpack-plugin": ^1.8.0
    "@soda/get-current-script": ^1.0.2
    "@types/minimist": ^1.2.0
    "@vue/cli-overlay": ^5.0.8
    "@vue/cli-plugin-router": ^5.0.8
    "@vue/cli-plugin-vuex": ^5.0.8
    "@vue/cli-shared-utils": ^5.0.8
    "@vue/component-compiler-utils": ^3.3.0
    "@vue/vue-loader-v15": "npm:vue-loader@^15.9.7"
    "@vue/web-component-wrapper": ^1.3.0
    acorn: ^8.0.5
    acorn-walk: ^8.0.2
    address: ^1.1.2
    autoprefixer: ^10.2.4
    browserslist: ^4.16.3
    case-sensitive-paths-webpack-plugin: ^2.3.0
    cli-highlight: ^2.1.10
    clipboardy: ^2.3.0
    cliui: ^7.0.4
    copy-webpack-plugin: ^9.0.1
    css-loader: ^6.5.0
    css-minimizer-webpack-plugin: ^3.0.2
    cssnano: ^5.0.0
    debug: ^4.1.1
    default-gateway: ^6.0.3
    dotenv: ^10.0.0
    dotenv-expand: ^5.1.0
    fs-extra: ^9.1.0
    globby: ^11.0.2
    hash-sum: ^2.0.0
    html-webpack-plugin: ^5.1.0
    is-file-esm: ^1.0.0
    launch-editor-middleware: ^2.2.1
    lodash.defaultsdeep: ^4.6.1
    lodash.mapvalues: ^4.6.0
    mini-css-extract-plugin: ^2.5.3
    minimist: ^1.2.5
    module-alias: ^2.2.2
    portfinder: ^1.0.26
    postcss: ^8.2.6
    postcss-loader: ^6.1.1
    progress-webpack-plugin: ^1.0.12
    ssri: ^8.0.1
    terser-webpack-plugin: ^5.1.1
    thread-loader: ^3.0.0
    vue-loader: ^17.0.0
    vue-style-loader: ^4.1.3
    webpack: ^5.54.0
    webpack-bundle-analyzer: ^4.4.0
    webpack-chain: ^6.5.1
    webpack-dev-server: ^4.7.3
    webpack-merge: ^5.7.3
    webpack-virtual-modules: ^0.4.2
    whatwg-fetch: ^3.6.2
  peerDependencies:
    vue-template-compiler: ^2.0.0
    webpack-sources: "*"
  peerDependenciesMeta:
    cache-loader:
      optional: true
    less-loader:
      optional: true
    pug-plain-loader:
      optional: true
    raw-loader:
      optional: true
    sass-loader:
      optional: true
    stylus-loader:
      optional: true
    vue-template-compiler:
      optional: true
    webpack-sources:
      optional: true
  bin:
    vue-cli-service: bin/vue-cli-service.js
  checksum: 94269479a7d9095a6e5ddaad82b357503fe605ff3aa3ffa0bed0efcc3cb7896455920a1d15244abc88b362722a7fbe5d246014faeb214fdc13f9afe9a5aa7f3f
  languageName: node
  linkType: hard

"@vue/cli-shared-utils@npm:^5.0.8":
  version: 5.0.8
  resolution: "@vue/cli-shared-utils@npm:5.0.8"
  dependencies:
    "@achrinza/node-ipc": ^9.2.5
    chalk: ^4.1.2
    execa: ^1.0.0
    joi: ^17.4.0
    launch-editor: ^2.2.1
    lru-cache: ^6.0.0
    node-fetch: ^2.6.7
    open: ^8.0.2
    ora: ^5.3.0
    read-pkg: ^5.1.1
    semver: ^7.3.4
    strip-ansi: ^6.0.0
  checksum: 20c0c9e6411b861ac7c25427cf84081c09c113ab09c602480850af6974fcf5c5ef18197ed4fa62093cea3f6631975bfe69fb42e99d19c8ed1884de0c4e6d555d
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/compiler-core@npm:3.3.4"
  dependencies:
    "@babel/parser": ^7.21.3
    "@vue/shared": 3.3.4
    estree-walker: ^2.0.2
    source-map-js: ^1.0.2
  checksum: 5437942ea6575b316c9cd84f4f128a44939713da8b6958060e152c599e6d771d5db056c398d7574ee706ff8092e0d99ac4f14e7eef8712a8dd923d2323201b9e
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/compiler-dom@npm:3.3.4"
  dependencies:
    "@vue/compiler-core": 3.3.4
    "@vue/shared": 3.3.4
  checksum: 1c2ac0c89de8eef7be1c568d57504e6245adaaec40c2c4d9717bc231ca10bf682d918a3b358d24c786eeaf8e0d7eb8a65f57d9044775a304783fde1d069a1896
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/compiler-sfc@npm:3.3.4"
  dependencies:
    "@babel/parser": ^7.20.15
    "@vue/compiler-core": 3.3.4
    "@vue/compiler-dom": 3.3.4
    "@vue/compiler-ssr": 3.3.4
    "@vue/reactivity-transform": 3.3.4
    "@vue/shared": 3.3.4
    estree-walker: ^2.0.2
    magic-string: ^0.30.0
    postcss: ^8.1.10
    source-map-js: ^1.0.2
  checksum: 0a0adfdd3e812f528e25e4b3bbf14b2296b719a8aac609eca42035295527cc253b918a552dc15218e917efef26b7ca94054dc8784a1a18c06c3d4bb4d18ab8b9
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/compiler-ssr@npm:3.3.4"
  dependencies:
    "@vue/compiler-dom": 3.3.4
    "@vue/shared": 3.3.4
  checksum: 5d1875d55ea864080dd90e5d81a29f93308e312faf00163db5b391b38c2fe799fd3eb58955823dc632f2f8bdd271a4534cc0020646b7f82717be1a8d30dc16e7
  languageName: node
  linkType: hard

"@vue/component-compiler-utils@npm:^3.1.0, @vue/component-compiler-utils@npm:^3.3.0":
  version: 3.3.0
  resolution: "@vue/component-compiler-utils@npm:3.3.0"
  dependencies:
    consolidate: ^0.15.1
    hash-sum: ^1.0.2
    lru-cache: ^4.1.2
    merge-source-map: ^1.1.0
    postcss: ^7.0.36
    postcss-selector-parser: ^6.0.2
    prettier: ^1.18.2 || ^2.0.0
    source-map: ~0.6.1
    vue-template-es2015-compiler: ^1.9.0
  dependenciesMeta:
    prettier:
      optional: true
  checksum: 70fee2289a4f54ec1be4d46136ee9b9893e31bf5622cead5be06c3dfb83449c3dbe6f8c03404625ccf302d0628ff9e2ea1debfae609d1bfe1d065d8f57c5dba8
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.0.0":
  version: 6.5.0
  resolution: "@vue/devtools-api@npm:6.5.0"
  checksum: ec819ef3a426e91d09e9cfefd2827e9ed8ec9d62bb3b3e0674f3da8c7e92a4b879c3b777dc7329172ca6fe2670b62dd5580d23160339208f0f5ae038f2e504ad
  languageName: node
  linkType: hard

"@vue/reactivity-transform@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/reactivity-transform@npm:3.3.4"
  dependencies:
    "@babel/parser": ^7.20.15
    "@vue/compiler-core": 3.3.4
    "@vue/shared": 3.3.4
    estree-walker: ^2.0.2
    magic-string: ^0.30.0
  checksum: b425e78b2084ac7037887fbe012dcad5e5963ac9714ae15a04fda1c6766ec8c53ef231de1cfdc4d3cf46bd5d84bfec8ebdccf48da4ff5ee2f4b5084e54f0a1b1
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/reactivity@npm:3.3.4"
  dependencies:
    "@vue/shared": 3.3.4
  checksum: 81c3d0c587d23656a57a7a31afb51357274f6512b51baffc67cda183b2361a7e65e646029c26a8bc28587f26b65bba808dcd93cdd3bacab48d2b99d11ad0ec97
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/runtime-core@npm:3.3.4"
  dependencies:
    "@vue/reactivity": 3.3.4
    "@vue/shared": 3.3.4
  checksum: d402da51269658cba5d857d65fbe322121160bcb1a6fcf03601d5183705e92505c6e90418f491a331ca3e27628f457a6ca7158b9add25f5b0cf5cf53664b8011
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/runtime-dom@npm:3.3.4"
  dependencies:
    "@vue/runtime-core": 3.3.4
    "@vue/shared": 3.3.4
    csstype: ^3.1.1
  checksum: dac9ada7f6128bcccc031fe5c25d00db94ffb7c011fcb70bada22fa4d889ff842eeb139ab9304bcc52cb5ae9030911a52cb3510b691bb190bbe5fab680b4411a
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/server-renderer@npm:3.3.4"
  dependencies:
    "@vue/compiler-ssr": 3.3.4
    "@vue/shared": 3.3.4
  peerDependencies:
    vue: 3.3.4
  checksum: e8598ed1a44df70edaea0ad6786aea6443b9b3d9266249eec5690401859d72d45a1e29ba3eef20e37a95f020abd5e763088b79070ee848af436a4390a253a37a
  languageName: node
  linkType: hard

"@vue/shared@npm:3.3.4":
  version: 3.3.4
  resolution: "@vue/shared@npm:3.3.4"
  checksum: 12fe53ff816bfa29ea53f89212067a86512c626b8d30149ff28b36705820f6150e1fb4e4e46897ad9eddb1d1cfc02d8941053939910eed69a905f7a5509baabe
  languageName: node
  linkType: hard

"@vue/vue-loader-v15@npm:vue-loader@^15.9.7":
  version: 15.10.2
  resolution: "vue-loader@npm:15.10.2"
  dependencies:
    "@vue/component-compiler-utils": ^3.1.0
    hash-sum: ^1.0.2
    loader-utils: ^1.1.0
    vue-hot-reload-api: ^2.3.0
    vue-style-loader: ^4.1.0
  peerDependencies:
    css-loader: "*"
    webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
  peerDependenciesMeta:
    cache-loader:
      optional: true
    prettier:
      optional: true
    vue-template-compiler:
      optional: true
  checksum: 3f4bfd100967af90ad5bd37de9a1dc2ca2f04b1bd7e695772a960186d74c51e161c89c08dd8623062b9151bd2f0bf493d2bbcd381214fa189977e0d379519b57
  languageName: node
  linkType: hard

"@vue/web-component-wrapper@npm:^1.3.0":
  version: 1.3.0
  resolution: "@vue/web-component-wrapper@npm:1.3.0"
  checksum: 8cc4d1135990e61ab9d38a7b6460b018703b38b4dd3477390083018bffb93b283fabb7d57d83b3cfb78dd44da4f863167b964fe88dfa9886a54996f308036a94
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.11.6, @webassemblyjs/ast@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/ast@npm:1.11.6"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
  checksum: 38ef1b526ca47c210f30975b06df2faf1a8170b1636ce239fc5738fc231ce28389dd61ecedd1bacfc03cbe95b16d1af848c805652080cb60982836eb4ed2c6cf
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.6"
  checksum: 29b08758841fd8b299c7152eda36b9eb4921e9c584eb4594437b5cd90ed6b920523606eae7316175f89c20628da14326801090167cc7fbffc77af448ac84b7e2
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.6"
  checksum: e8563df85161096343008f9161adb138a6e8f3c2cc338d6a36011aa55eabb32f2fd138ffe63bc278d009ada001cc41d263dadd1c0be01be6c2ed99076103689f
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-buffer@npm:1.11.6"
  checksum: b14d0573bf680d22b2522e8a341ec451fddd645d1f9c6bd9012ccb7e587a2973b86ab7b89fe91e1c79939ba96095f503af04369a3b356c8023c13a5893221644
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.6"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.6
    "@webassemblyjs/helper-api-error": 1.11.6
    "@xtuc/long": 4.2.2
  checksum: f4b562fa219f84368528339e0f8d273ad44e047a07641ffcaaec6f93e5b76fd86490a009aa91a294584e1436d74b0a01fa9fde45e333a4c657b58168b04da424
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.6"
  checksum: 3535ef4f1fba38de3475e383b3980f4bbf3de72bbb631c2b6584c7df45be4eccd62c6ff48b5edd3f1bcff275cfd605a37679ec199fc91fd0a7705d7f1e3972dc
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
  checksum: b2cf751bf4552b5b9999d27bbb7692d0aca75260140195cb58ea6374d7b9c2dc69b61e10b211a0e773f66209c3ddd612137ed66097e3684d7816f854997682e9
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/ieee754@npm:1.11.6"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 13574b8e41f6ca39b700e292d7edf102577db5650fe8add7066a320aa4b7a7c09a5056feccac7a74eb68c10dea9546d4461412af351f13f6b24b5f32379b49de
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/leb128@npm:1.11.6"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 7ea942dc9777d4b18a5ebfa3a937b30ae9e1d2ce1fee637583ed7f376334dd1d4274f813d2e250056cca803e0952def4b954913f1a3c9068bcd4ab4ee5143bf0
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/utf8@npm:1.11.6"
  checksum: 807fe5b5ce10c390cfdd93e0fb92abda8aebabb5199980681e7c3743ee3306a75729bcd1e56a3903980e96c885ee53ef901fcbaac8efdfa480f9c0dae1d08713
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-edit@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/helper-wasm-section": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
    "@webassemblyjs/wasm-opt": 1.11.6
    "@webassemblyjs/wasm-parser": 1.11.6
    "@webassemblyjs/wast-printer": 1.11.6
  checksum: 29ce75870496d6fad864d815ebb072395a8a3a04dc9c3f4e1ffdc63fc5fa58b1f34304a1117296d8240054cfdbc38aca88e71fb51483cf29ffab0a61ef27b481
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-gen@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: a645a2eecbea24833c3260a249704a7f554ef4a94c6000984728e94bb2bc9140a68dfd6fd21d5e0bbb09f6dfc98e083a45760a83ae0417b41a0196ff6d45a23a
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-opt@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
    "@webassemblyjs/wasm-parser": 1.11.6
  checksum: b4557f195487f8e97336ddf79f7bef40d788239169aac707f6eaa2fa5fe243557c2d74e550a8e57f2788e70c7ae4e7d32f7be16101afe183d597b747a3bdd528
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.11.6, @webassemblyjs/wasm-parser@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-parser@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-api-error": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: 8200a8d77c15621724a23fdabe58d5571415cda98a7058f542e670ea965dd75499f5e34a48675184947c66f3df23adf55df060312e6d72d57908e3f049620d8a
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wast-printer@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@xtuc/long": 4.2.2
  checksum: d2fa6a4c427325ec81463e9c809aa6572af6d47f619f3091bf4c4a6fc34f1da3df7caddaac50b8e7a457f8784c62cd58c6311b6cb69b0162ccd8d4c072f79cf8
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.5, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.9.0":
  version: 1.9.0
  resolution: "acorn-import-assertions@npm:1.9.0"
  peerDependencies:
    acorn: ^8
  checksum: 944fb2659d0845c467066bdcda2e20c05abe3aaf11972116df457ce2627628a81764d800dd55031ba19de513ee0d43bb771bc679cc0eda66dc8b4fade143bc0c
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.1, acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0, acorn-walk@npm:^8.0.2":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^7.4.0":
  version: 7.4.1
  resolution: "acorn@npm:7.4.1"
  bin:
    acorn: bin/acorn
  checksum: 1860f23c2107c910c6177b7b7be71be350db9e1080d814493fae143ae37605189504152d1ba8743ba3178d0b37269ce1ffc42b101547fdc1827078f82671e407
  languageName: node
  linkType: hard

"acorn@npm:^8.0.4, acorn@npm:^8.0.5, acorn@npm:^8.7.1, acorn@npm:^8.8.2, acorn@npm:^8.9.0":
  version: 8.10.0
  resolution: "acorn@npm:8.10.0"
  bin:
    acorn: bin/acorn
  checksum: 538ba38af0cc9e5ef983aee196c4b8b4d87c0c94532334fa7e065b2c8a1f85863467bb774231aae91613fcda5e68740c15d97b1967ae3394d20faddddd8af61d
  languageName: node
  linkType: hard

"address@npm:^1.1.2":
  version: 1.2.2
  resolution: "address@npm:1.2.2"
  checksum: ace439960c1e3564d8f523aff23a841904bf33a2a7c2e064f7f60a064194075758b9690e65bd9785692a4ef698a998c57eb74d145881a1cecab8ba658ddb1607
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.5.0
  resolution: "agentkeepalive@npm:4.5.0"
  dependencies:
    humanize-ms: ^1.2.1
  checksum: 13278cd5b125e51eddd5079f04d6fe0914ac1b8b91c1f3db2c1822f99ac1a7457869068997784342fe455d59daaff22e14fb7b8c3da4e741896e7e31faf92481
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.0.1, ajv@npm:^8.9.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: 4dc13714e316e67537c8b31bc063f99a1d9d9a497eb4bbd55191ac0dcd5e4985bbb71570352ad6f1e76684fb6d790928f96ba3b2d4fd6e10024be9612fe3f001
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: a9c2ec842038a1fabc7db9ece7d3177e2fe1c5dc6f0c51ecfbf5f39911427b89c00b5dc6b8bd95f82a26e9b16aaae2e83d45f060e98070ce4d1333038edceb0e
  languageName: node
  linkType: hard

"ansi-escapes@npm:^3.0.0":
  version: 3.2.0
  resolution: "ansi-escapes@npm:3.2.0"
  checksum: 0f94695b677ea742f7f1eed961f7fd8d05670f744c6ad1f8f635362f6681dcfbc1575cb05b43abc7bb6d67e25a75fb8c7ea8f2a57330eb2c76b33f18cb2cef0a
  languageName: node
  linkType: hard

"ansi-html-community@npm:^0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 04c568e8348a636963f915e48eaa3e01218322e1169acafdd79c384f22e5558c003f79bbc480c1563865497482817c7eed025f0653ebc17642fededa5cb42089
  languageName: node
  linkType: hard

"ansi-regex@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-regex@npm:3.0.1"
  checksum: 09daf180c5f59af9850c7ac1bd7fda85ba596cc8cbeb210826e90755f06c818af86d9fa1e6e8322fab2c3b9e9b03f56c537b42241139f824dd75066a1e7257cc
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"arch@npm:^2.1.1":
  version: 2.2.0
  resolution: "arch@npm:2.2.0"
  checksum: e21b7635029fe8e9cdd5a026f9a6c659103e63fff423834323cdf836a1bb240a72d0c39ca8c470f84643385cf581bd8eda2cad8bf493e27e54bd9783abe9101f
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.2":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async-validator@npm:^4.0.7":
  version: 4.2.5
  resolution: "async-validator@npm:4.2.5"
  checksum: 3e3d891a2e21497c8a646afeb7b1e6ed5f98de5f58ce3600732080f327cb581e65d8d8ff184273f1461dc84105d49f5cf31422a67ce50e787967c306838b6f40
  languageName: node
  linkType: hard

"async@npm:^2.6.4":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: ^4.17.14
  checksum: a52083fb32e1ebe1d63e5c5624038bb30be68ff07a6c8d7dfe35e47c93fc144bd8652cbec869e0ac07d57dde387aa5f1386be3559cdee799cb1f789678d88e19
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.2.4":
  version: 10.4.15
  resolution: "autoprefixer@npm:10.4.15"
  dependencies:
    browserslist: ^4.21.10
    caniuse-lite: ^1.0.30001520
    fraction.js: ^4.2.0
    normalize-range: ^0.1.2
    picocolors: ^1.0.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: d490b14fb098c043e109fc13cd23628f146af99a493d35b9df3a26f8ec0b4dd8937c5601cdbaeb465b98ea31d3ea05aa7184711d4d93dfb52358d073dcb67032
  languageName: node
  linkType: hard

"babel-loader@npm:^8.2.2":
  version: 8.3.0
  resolution: "babel-loader@npm:8.3.0"
  dependencies:
    find-cache-dir: ^3.3.1
    loader-utils: ^2.0.0
    make-dir: ^3.1.0
    schema-utils: ^2.6.5
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: d48bcf9e030e598656ad3ff5fb85967db2eaaf38af5b4a4b99d25618a2057f9f100e6b231af2a46c1913206db506115ca7a8cbdf52c9c73d767070dae4352ab5
  languageName: node
  linkType: hard

"babel-plugin-dynamic-import-node@npm:^2.3.3":
  version: 2.3.3
  resolution: "babel-plugin-dynamic-import-node@npm:2.3.3"
  dependencies:
    object.assign: ^4.1.0
  checksum: c9d24415bcc608d0db7d4c8540d8002ac2f94e2573d2eadced137a29d9eab7e25d2cbb4bc6b9db65cf6ee7430f7dd011d19c911a9a778f0533b4a05ce8292c9b
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.5":
  version: 0.4.5
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.5"
  dependencies:
    "@babel/compat-data": ^7.22.6
    "@babel/helper-define-polyfill-provider": ^0.4.2
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 33a8e06aa54e2858d211c743d179f0487b03222f9ca1bfd7c4865bca243fca942a3358cb75f6bb894ed476cbddede834811fbd6903ff589f055821146f053e1a
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.8.3":
  version: 0.8.3
  resolution: "babel-plugin-polyfill-corejs3@npm:0.8.3"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
    core-js-compat: ^3.31.0
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: dcbb30e551702a82cfd4d2c375da2c317658e55f95e9edcda93b9bbfdcc8fb6e5344efcb144e04d3406859e7682afce7974c60ededd9f12072a48a83dd22a0da
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.5.2":
  version: 0.5.2
  resolution: "babel-plugin-polyfill-regenerator@npm:0.5.2"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d962200f604016a9a09bc9b4aaf60a3db7af876bb65bcefaeac04d44ac9d9ec4037cf24ce117760cc141d7046b6394c7eb0320ba9665cb4a2ee64df2be187c93
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 61f9934c7378a51dce61b915586191078ef7f1c3eca707fdd58b96ff2ff56d9e0af2bdab66b1462301a73c73374239e6542d9821c0af787f3209a23365d07e7f
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"bluebird@npm:^3.1.1":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 869417503c722e7dc54ca46715f70e15f4d9c602a423a02c825570862d12935be59ed9c7ba34a9b31f186c017c23cac6b54e35446f8353059c101da73eac22ef
  languageName: node
  linkType: hard

"body-parser@npm:1.20.1":
  version: 1.20.1
  resolution: "body-parser@npm:1.20.1"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.4
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.11.0
    raw-body: 2.5.1
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: f1050dbac3bede6a78f0b87947a8d548ce43f91ccc718a50dd774f3c81f2d8b04693e52acf62659fad23101827dd318da1fb1363444ff9a8482b886a3e4a5266
  languageName: node
  linkType: hard

"bonjour-service@npm:^1.0.11":
  version: 1.1.1
  resolution: "bonjour-service@npm:1.1.1"
  dependencies:
    array-flatten: ^2.1.2
    dns-equal: ^1.0.0
    fast-deep-equal: ^3.1.3
    multicast-dns: ^7.2.5
  checksum: 832d0cf78b91368fac8bb11fd7a714e46f4c4fb1bb14d7283bce614a6fb3aae2f3fe209aba5b4fa051811c1cab6921d073a83db8432fb23292f27dd4161fb0f1
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.14.5, browserslist@npm:^4.16.3, browserslist@npm:^4.21.10, browserslist@npm:^4.21.4, browserslist@npm:^4.21.9":
  version: 4.21.10
  resolution: "browserslist@npm:4.21.10"
  dependencies:
    caniuse-lite: ^1.0.30001517
    electron-to-chromium: ^1.4.477
    node-releases: ^2.0.13
    update-browserslist-db: ^1.0.11
  bin:
    browserslist: cli.js
  checksum: 1e27c0f111a35d1dd0e8fc2c61781b0daefabc2c9471b0b10537ce54843014bceb2a1ce4571af1a82b2bf1e6e6e05d38865916689a158f03bc2c7a4ec2577db8
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"bytes@npm:3.0.0":
  version: 3.0.0
  resolution: "bytes@npm:3.0.0"
  checksum: a2b386dd8188849a5325f58eef69c3b73c51801c08ffc6963eddc9be244089ba32d19347caf6d145c86f315ae1b1fc7061a32b0c1aa6379e6a719090287ed101
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^17.0.0":
  version: 17.1.4
  resolution: "cacache@npm:17.1.4"
  dependencies:
    "@npmcli/fs": ^3.1.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^7.7.1
    minipass: ^7.0.3
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^4.0.0
    ssri: ^10.0.0
    tar: ^6.1.11
    unique-filename: ^3.0.0
  checksum: b7751df756656954a51201335addced8f63fc53266fa56392c9f5ae83c8d27debffb4458ac2d168a744a4517ec3f2163af05c20097f93d17bdc2dc8a385e14a6
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: ^3.1.2
    tslib: ^2.0.3
  checksum: bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: ^4.0.0
    caniuse-lite: ^1.0.0
    lodash.memoize: ^4.1.2
    lodash.uniq: ^4.5.0
  checksum: db2a229383b20d0529b6b589dde99d7b6cb56ba371366f58cbbfa2929c9f42c01f873e2b6ef641d4eda9f0b4118de77dbb2805814670bdad4234bf08e720b0b4
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001517, caniuse-lite@npm:^1.0.30001520":
  version: 1.0.30001534
  resolution: "caniuse-lite@npm:1.0.30001534"
  checksum: 8e8b63c1ce0d5b944ee2d8223955b33f3d4f60c33fed394ff6353b5c7106b2dc55219fd07d80c79e66ed1f82ed9367cee17bda96789cbd2ab57c8d30b1b5c510
  languageName: node
  linkType: hard

"case-sensitive-paths-webpack-plugin@npm:^2.3.0":
  version: 2.4.0
  resolution: "case-sensitive-paths-webpack-plugin@npm:2.4.0"
  checksum: bcf469446eeee9ac0046e30860074ebb9aa4803aab9140e6bb72b600b23b1d70635690754be4504ce35cd99cdf05226bee8d894ba362a3f5485d5f6310fc6d02
  languageName: node
  linkType: hard

"chalk@npm:^2.1.0, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 8e3ddf3981c4da405ddbd7d9c8d91944ddf6e33d6837756979f7840a29272a69a5189ecae0ff84006750d6d1e92368d413335eab4db5476db6e6703a1d1e0505
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0, chokidar@npm:^3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: b49fcde40176ba007ff361b198a2d35df60d9bb2a5aab228279eb810feae9294a6b4649ab15981304447afe1e6ffbf4788ad5db77235dc770ab777c6e771980c
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"ci-info@npm:^1.5.0":
  version: 1.6.0
  resolution: "ci-info@npm:1.6.0"
  checksum: dfc058f60c3889793befe77349c3cd1a5452d21bed5ff60cb34382bee7bbdccc5c4c2ff2b77eab8c411c54d84f93963dacf593b9d901b43b93b7ad2a422aa163
  languageName: node
  linkType: hard

"clean-css@npm:^5.2.2":
  version: 5.3.2
  resolution: "clean-css@npm:5.3.2"
  dependencies:
    source-map: ~0.6.0
  checksum: 8787b281acc9878f309b5f835d410085deedfd4e126472666773040a6a8a72f472a1d24185947d23b87b1c419bf2c5ed429395d5c5ff8279c98b05d8011e9758
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.0.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: ^2.0.0
  checksum: d88e97bfdac01046a3ffe7d49f06757b3126559d7e44aa2122637eb179284dc6cd49fca2fac4f67c19faaf7e6dab716b6fe1dfcd309977407d8c7578ec2d044d
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-highlight@npm:^2.1.10":
  version: 2.1.11
  resolution: "cli-highlight@npm:2.1.11"
  dependencies:
    chalk: ^4.0.0
    highlight.js: ^10.7.1
    mz: ^2.4.0
    parse5: ^5.1.1
    parse5-htmlparser2-tree-adapter: ^6.0.0
    yargs: ^16.0.0
  bin:
    highlight: bin/highlight
  checksum: 0a60e60545e39efea78c1732a25b91692017ec40fb6e9497208dc0eeeae69991d3923a8d6e4edd0543db3c395ed14529a33dd4d0353f1679c5b6dded792a8496
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.0
  resolution: "cli-spinners@npm:2.9.0"
  checksum: a9c56e1f44457d4a9f4f535364e729cb8726198efa9e98990cfd9eda9e220dfa4ba12f92808d1be5e29029cdfead781db82dc8549b97b31c907d55f96aa9b0e2
  languageName: node
  linkType: hard

"clipboardy@npm:^2.3.0":
  version: 2.3.0
  resolution: "clipboardy@npm:2.3.0"
  dependencies:
    arch: ^2.1.1
    execa: ^1.0.0
    is-wsl: ^2.1.1
  checksum: 2733790bc8bbb76a5be7706fa4632f655010774e579a9d3ebe31dc10cf44a2b82cf07b0b6f74162e63048ce32d912193c08c5b5311dce5c19fc641a3bda1292b
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2, cliui@npm:^7.0.4":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: ce2e8f578a4813806788ac399b9e866297740eecd4ad1823c27fd344d78b22c5f8597d548adbcc46f0573e43e21e751f39446c5a5e804a12aace402b7a315d7f
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"colord@npm:^2.9.1":
  version: 2.9.3
  resolution: "colord@npm:2.9.3"
  checksum: 95d909bfbcfd8d5605cbb5af56f2d1ce2b323990258fd7c0d2eb0e6d3bb177254d7fb8213758db56bb4ede708964f78c6b992b326615f81a18a6aaf11d64c650
  languageName: node
  linkType: hard

"colorette@npm:^2.0.10":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"compressible@npm:~2.0.16":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.7.4
  resolution: "compression@npm:1.7.4"
  dependencies:
    accepts: ~1.3.5
    bytes: 3.0.0
    compressible: ~2.0.16
    debug: 2.6.9
    on-headers: ~1.0.2
    safe-buffer: 5.1.2
    vary: ~1.1.2
  checksum: 35c0f2eb1f28418978615dc1bc02075b34b1568f7f56c62d60f4214d4b7cc00d0f6d282b5f8a954f59872396bd770b6b15ffd8aa94c67d4bce9b8887b906999b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^2.0.0":
  version: 2.0.0
  resolution: "connect-history-api-fallback@npm:2.0.0"
  checksum: dc5368690f4a5c413889792f8df70d5941ca9da44523cde3f87af0745faee5ee16afb8195434550f0504726642734f2683d6c07f8b460f828a12c45fbd4c9a68
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"consolidate@npm:^0.15.1":
  version: 0.15.1
  resolution: "consolidate@npm:0.15.1"
  dependencies:
    bluebird: ^3.1.1
  checksum: 5a44ee975f8403dd3ff8ff3472fda7db0484b19f153eaac38e784465505a0741939c72d703befda7c75649739fc7a68f9659a86e2a62469336a8d531bd7a10df
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: 1f4bd2ca5765f8c9689a7e8954183f5332139eb72b6ff783d8947032ec1fdf43109852c178e21a953a30c0dd42257828185be01b49d1eb1a67fd054ca588a180
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:^9.0.1":
  version: 9.1.0
  resolution: "copy-webpack-plugin@npm:9.1.0"
  dependencies:
    fast-glob: ^3.2.7
    glob-parent: ^6.0.1
    globby: ^11.0.3
    normalize-path: ^3.0.0
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.0
  peerDependencies:
    webpack: ^5.1.0
  checksum: 06cb4fb6fc99a95ccfd3169115ee57f64953e5b4075900fc8faab98b7e7d3fcd6915b125fdb98c919cfd55e581a8444f5c6b9dbb342cbd60b154d8fb3f79f2b9
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.31.0, core-js-compat@npm:^3.8.3":
  version: 3.32.2
  resolution: "core-js-compat@npm:3.32.2"
  dependencies:
    browserslist: ^4.21.10
  checksum: efca146ad71a542e6f196db5ba5aed617e48c615bdf1fbb065471b3267f833ac545bd5fc5ad0642c3d3974b955f0684ff0863d7471d7050ee0284e0a1313942e
  languageName: node
  linkType: hard

"core-js@npm:^3.8.3":
  version: 3.32.2
  resolution: "core-js@npm:3.32.2"
  checksum: d6fac7e8eb054eefc211c76cd0a0ff07447a917122757d085f469f046ec888d122409c7db1a9601c3eb5fa767608ed380bcd219eace02bdf973da155680edeec
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: c53bf7befc1591b2651a22414a5e786cd5f2eeaa87f3678a3d49d6069835a9d8d1aef223728e98aa8fec9a95bf831120d245096db12abe019fecb51f5696c96f
  languageName: node
  linkType: hard

"cross-spawn@npm:^5.0.1":
  version: 5.1.0
  resolution: "cross-spawn@npm:5.1.0"
  dependencies:
    lru-cache: ^4.0.1
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: 726939c9954fc70c20e538923feaaa33bebc253247d13021737c3c7f68cdc3e0a57f720c0fe75057c0387995349f3f12e20e9bfdbf12274db28019c7ea4ec166
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.0":
  version: 6.0.5
  resolution: "cross-spawn@npm:6.0.5"
  dependencies:
    nice-try: ^1.0.4
    path-key: ^2.0.1
    semver: ^5.5.0
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: f893bb0d96cd3d5751d04e67145bdddf25f99449531a72e82dcbbd42796bbc8268c1076c6b3ea51d4d455839902804b94bc45dfb37ecbb32ea8e54a6741c3ab9
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^6.3.1":
  version: 6.4.1
  resolution: "css-declaration-sorter@npm:6.4.1"
  peerDependencies:
    postcss: ^8.0.9
  checksum: cbdc9e0d481011b1a28fd5b60d4eb55fe204391d31a0b1b490b2cecf4baa85810f9b8c48adab4df644f4718104ed3ed72c64a9745e3216173767bf4aeca7f9b8
  languageName: node
  linkType: hard

"css-loader@npm:^6.5.0":
  version: 6.8.1
  resolution: "css-loader@npm:6.8.1"
  dependencies:
    icss-utils: ^5.1.0
    postcss: ^8.4.21
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.3
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.2.0
    semver: ^7.3.8
  peerDependencies:
    webpack: ^5.0.0
  checksum: 7c1784247bdbe76dc5c55fb1ac84f1d4177a74c47259942c9cfdb7a8e6baef11967a0bc85ac285f26bd26d5059decb848af8154a03fdb4f4894f41212f45eef3
  languageName: node
  linkType: hard

"css-minimizer-webpack-plugin@npm:^3.0.2":
  version: 3.4.1
  resolution: "css-minimizer-webpack-plugin@npm:3.4.1"
  dependencies:
    cssnano: ^5.0.6
    jest-worker: ^27.0.2
    postcss: ^8.3.5
    schema-utils: ^4.0.0
    serialize-javascript: ^6.0.0
    source-map: ^0.6.1
  peerDependencies:
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@parcel/css":
      optional: true
    clean-css:
      optional: true
    csso:
      optional: true
    esbuild:
      optional: true
  checksum: 065c6c1eadb7c99267db5d04d6f3909e9968b73c4cb79ab9e4502a5fbf1a3d564cfe6f8e0bff8e4ab00d4ed233e9c3c76a4ebe0ee89150b3d9ecb064ddf1e5e9
  languageName: node
  linkType: hard

"css-render@npm:^0.15.10":
  version: 0.15.12
  resolution: "css-render@npm:0.15.12"
  dependencies:
    "@emotion/hash": ~0.8.0
    csstype: ~3.0.5
  checksum: 80265c5055e3a77b7ee357c16e3090f84f19a2f4e0917425c430f987e0af62c2491121832c7e0c7b0037eaff4291dc0385b1e6ef068c6088a704973e971defd0
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.0.1
    domhandler: ^4.3.1
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: d6202736839194dd7f910320032e7cfc40372f025e4bf21ca5bf6eb0a33264f322f50ba9c0adc35dadd342d3d6fae5ca244779a4873afbfa76561e343f2058e0
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2, css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^5.2.14":
  version: 5.2.14
  resolution: "cssnano-preset-default@npm:5.2.14"
  dependencies:
    css-declaration-sorter: ^6.3.1
    cssnano-utils: ^3.1.0
    postcss-calc: ^8.2.3
    postcss-colormin: ^5.3.1
    postcss-convert-values: ^5.1.3
    postcss-discard-comments: ^5.1.2
    postcss-discard-duplicates: ^5.1.0
    postcss-discard-empty: ^5.1.1
    postcss-discard-overridden: ^5.1.0
    postcss-merge-longhand: ^5.1.7
    postcss-merge-rules: ^5.1.4
    postcss-minify-font-values: ^5.1.0
    postcss-minify-gradients: ^5.1.1
    postcss-minify-params: ^5.1.4
    postcss-minify-selectors: ^5.2.1
    postcss-normalize-charset: ^5.1.0
    postcss-normalize-display-values: ^5.1.0
    postcss-normalize-positions: ^5.1.1
    postcss-normalize-repeat-style: ^5.1.1
    postcss-normalize-string: ^5.1.0
    postcss-normalize-timing-functions: ^5.1.0
    postcss-normalize-unicode: ^5.1.1
    postcss-normalize-url: ^5.1.0
    postcss-normalize-whitespace: ^5.1.1
    postcss-ordered-values: ^5.1.3
    postcss-reduce-initial: ^5.1.2
    postcss-reduce-transforms: ^5.1.0
    postcss-svgo: ^5.1.0
    postcss-unique-selectors: ^5.1.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: d3bbbe3d50c6174afb28d0bdb65b511fdab33952ec84810aef58b87189f3891c34aaa8b6a6101acd5314f8acded839b43513e39a75f91a698ddc985a1b1d9e95
  languageName: node
  linkType: hard

"cssnano-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "cssnano-utils@npm:3.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 975c84ce9174cf23bb1da1e9faed8421954607e9ea76440cd3bb0c1bea7e17e490d800fca5ae2812d1d9e9d5524eef23ede0a3f52497d7ccc628e5d7321536f2
  languageName: node
  linkType: hard

"cssnano@npm:^5.0.0, cssnano@npm:^5.0.6":
  version: 5.1.15
  resolution: "cssnano@npm:5.1.15"
  dependencies:
    cssnano-preset-default: ^5.2.14
    lilconfig: ^2.0.3
    yaml: ^1.10.2
  peerDependencies:
    postcss: ^8.2.15
  checksum: ca9e1922178617c66c2f1548824b2c7af2ecf69cc3a187fc96bf8d29251c2e84d9e4966c69cf64a2a6a057a37dff7d6d057bc8a2a0957e6ea382e452ae9d0bbb
  languageName: node
  linkType: hard

"csso@npm:^4.2.0":
  version: 4.2.0
  resolution: "csso@npm:4.2.0"
  dependencies:
    css-tree: ^1.1.2
  checksum: 380ba9663da3bcea58dee358a0d8c4468bb6539be3c439dc266ac41c047217f52fd698fb7e4b6b6ccdfb8cf53ef4ceed8cc8ceccb8dfca2aa628319826b5b998
  languageName: node
  linkType: hard

"csstype@npm:^3.1.1":
  version: 3.1.2
  resolution: "csstype@npm:3.1.2"
  checksum: e1a52e6c25c1314d6beef5168da704ab29c5186b877c07d822bd0806717d9a265e8493a2e35ca7e68d0f5d472d43fac1cdce70fd79fd0853dff81f3028d857b5
  languageName: node
  linkType: hard

"csstype@npm:~3.0.5":
  version: 3.0.11
  resolution: "csstype@npm:3.0.11"
  checksum: 95e56abfe9ca219ae065acb4e43f61771a03170eed919127f558dfa168240867aba7629c8d98a201a0dd06d9a5ce82686f0570031c928516c61816adbc7c877f
  languageName: node
  linkType: hard

"date-fns-tz@npm:^1.3.3":
  version: 1.3.8
  resolution: "date-fns-tz@npm:1.3.8"
  peerDependencies:
    date-fns: ">=2.0.0"
  checksum: dbf17cb88de7c7b71a9f86b815ac62ebdbf725c030e78d2421309456b5001a85284a5b6763f4af0ba660183f12e876c165e725a0723f642edb7846407db03133
  languageName: node
  linkType: hard

"date-fns@npm:^2.28.0":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": ^7.21.0
  checksum: f7be01523282e9bb06c0cd2693d34f245247a29098527d4420628966a2d9aad154bd0e90a6b1cf66d37adcb769cd108cf8a7bd49d76db0fb119af5cdd13644f4
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.1, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.2, debug@npm:^4.3.3":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^1.5.2":
  version: 1.5.2
  resolution: "deepmerge@npm:1.5.2"
  checksum: 5ecfe328e0105f2c554b90af555cbba052ab4468f1893e3b26800cb8869d3c1a1c590a5bbe1fdf481a8cc89b1bc47b5ac73a7153d5a0e4b702ea6eca081038a8
  languageName: node
  linkType: hard

"default-gateway@npm:^6.0.3":
  version: 6.0.3
  resolution: "default-gateway@npm:6.0.3"
  dependencies:
    execa: ^5.0.0
  checksum: 126f8273ecac8ee9ff91ea778e8784f6cd732d77c3157e8c5bdd6ed03651b5291f71446d05bc02d04073b1e67583604db5394ea3cf992ede0088c70ea15b7378
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "define-data-property@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.1
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.0
  checksum: 6129cda80ea82198ff22b26ae4624744c7f61b9d664ab78b28156db87849dc893a5c2e76ab8628f1115901bd1c109bd5b943f5901e0f10456254d4aa8b7cd5ae
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.4":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: a8471ac849c7c13824f053babea1bc26e2f359394dd5a460f8340d8abd13434be01e3327a5c59d212f8c8997817450efd3f3ac77bec709b21979cf0235644524
  languageName: node
  linkType: hard

"dns-packet@npm:^5.2.2":
  version: 5.6.1
  resolution: "dns-packet@npm:5.6.1"
  dependencies:
    "@leichtgewicht/ip-codec": ^2.0.1
  checksum: 64c06457f0c6e143f7a0946e0aeb8de1c5f752217cfa143ef527467c00a6d78db1835cfdb6bb68333d9f9a4963cf23f410439b5262a8935cce1236f45e344b81
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: ~0.4
  checksum: ea52fe303f5392e48dea563abef0e6fb3a478b8dbe3c599e99bb5d53981c6c38fc4944e56bb92a8ead6bb989d10b7914722ae11febbd2fd0910e33b9fc4aaa77
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: fbb0b01f87a8a2d18e6e5a388ad0f7ec4a5c05c06d219377da1abc7bb0f674d804f4a8a94e3f71ff15f6cb7dcfc75704a54b261db672b9b3ab03da6b758b0b22
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: ^2.2.0
  checksum: 4c665ceed016e1911bf7d1dadc09dc888090b64dee7851cccd2fcf5442747ec39c647bb1cb8c8919f8bbdd0f0c625a6bafeeed4b2d656bbecdbae893f43ffaaa
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv-expand@npm:^5.1.0":
  version: 5.1.0
  resolution: "dotenv-expand@npm:5.1.0"
  checksum: 8017675b7f254384915d55f9eb6388e577cf0a1231a28d54b0ca03b782be9501b0ac90ac57338636d395fa59051e6209e9b44b8ddf169ce6076dffb5dea227d3
  languageName: node
  linkType: hard

"dotenv@npm:^10.0.0":
  version: 10.0.0
  resolution: "dotenv@npm:10.0.0"
  checksum: f412c5fe8c24fbe313d302d2500e247ba8a1946492db405a4de4d30dd0eb186a88a43f13c958c5a7de303938949c4231c56994f97d05c4bc1f22478d631b4005
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"easy-stack@npm:1.0.1":
  version: 1.0.1
  resolution: "easy-stack@npm:1.0.1"
  checksum: 161a99e497b3857b0be4ec9e1ebbe90b241ea9d84702f9881b8e5b3f6822065b8c4e33436996935103e191bffba3607de70712a792f4d406a050def48c6bc381
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.477":
  version: 1.4.519
  resolution: "electron-to-chromium@npm:1.4.519"
  checksum: 2599e5b62a045698015f5feafa0ca19d659d887a9b289cde19c24981d06ede843feb928c7ef1bb2aa63a1d25694d1b1e385c9c0959d56b4bfe98b05cb63eecf0
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.15.0":
  version: 5.15.0
  resolution: "enhanced-resolve@npm:5.15.0"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: fbd8cdc9263be71cc737aa8a7d6c57b43d6aa38f6cc75dde6fcd3598a130cc465f979d2f4d01bb3bf475acb43817749c79f8eef9be048683602ca91ab52e4f11
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.5":
  version: 2.4.1
  resolution: "enquirer@npm:2.4.1"
  dependencies:
    ansi-colors: ^4.1.1
    strip-ansi: ^6.0.1
  checksum: f080f11a74209647dbf347a7c6a83c8a47ae1ebf1e75073a808bc1088eb780aa54075bfecd1bcdb3e3c724520edb8e6ee05da031529436b421b71066fcc48cb5
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: ^1.3.4
  checksum: 3b916d2d14c6682f287c8bfa28e14672f47eafe832701080e420e7cdbaebb2c50293868256a95706ac2330fe078cf5664713158b49bc30d7a5f2ac229ded0e18
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.3.1
  resolution: "es-module-lexer@npm:1.3.1"
  checksum: 3beafa7e171eb1e8cc45695edf8d51638488dddf65294d7911f8d6a96249da6a9838c87529262cc6ea53988d8272cec0f4bff93f476ed031a54ba3afb51a0ed3
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:^8.0.3":
  version: 8.7.1
  resolution: "eslint-plugin-vue@npm:8.7.1"
  dependencies:
    eslint-utils: ^3.0.0
    natural-compare: ^1.4.0
    nth-check: ^2.0.1
    postcss-selector-parser: ^6.0.9
    semver: ^7.3.5
    vue-eslint-parser: ^8.0.1
  peerDependencies:
    eslint: ^6.2.0 || ^7.0.0 || ^8.0.0
  checksum: c3aefb226dea126db32cab4c570f17f4b4047d609f4f5748e8dc410e929fbbfacdc07af1421c0f7773398513e9363de4480694714bc85fa2188ca50d1d19cfbc
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.0.0":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-utils@npm:2.1.0"
  dependencies:
    eslint-visitor-keys: ^1.1.0
  checksum: 27500938f348da42100d9e6ad03ae29b3de19ba757ae1a7f4a087bdcf83ac60949bbb54286492ca61fac1f5f3ac8692dd21537ce6214240bf95ad0122f24d71d
  languageName: node
  linkType: hard

"eslint-utils@npm:^3.0.0":
  version: 3.0.0
  resolution: "eslint-utils@npm:3.0.0"
  dependencies:
    eslint-visitor-keys: ^2.0.0
  peerDependencies:
    eslint: ">=5"
  checksum: 0668fe02f5adab2e5a367eee5089f4c39033af20499df88fe4e6aba2015c20720404d8c3d6349b6f716b08fdf91b9da4e5d5481f265049278099c4c836ccb619
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^1.1.0, eslint-visitor-keys@npm:^1.3.0":
  version: 1.3.0
  resolution: "eslint-visitor-keys@npm:1.3.0"
  checksum: 37a19b712f42f4c9027e8ba98c2b06031c17e0c0a4c696cd429bd9ee04eb43889c446f2cd545e1ff51bef9593fcec94ecd2c2ef89129fcbbf3adadbef520376a
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.0.0, eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.1.0, eslint-visitor-keys@npm:^3.4.1":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-webpack-plugin@npm:^3.1.0":
  version: 3.2.0
  resolution: "eslint-webpack-plugin@npm:3.2.0"
  dependencies:
    "@types/eslint": ^7.29.0 || ^8.4.1
    jest-worker: ^28.0.2
    micromatch: ^4.0.5
    normalize-path: ^3.0.0
    schema-utils: ^4.0.0
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
    webpack: ^5.0.0
  checksum: 095034c35e773fdb21ec7e597ae1f8a6899679c290db29d8568ca94619e8c7f4971f0f9edccc8a965322ab8af9286c87205985a38f4fdcf17654aee7cd8bb7b5
  languageName: node
  linkType: hard

"eslint@npm:^7.32.0":
  version: 7.32.0
  resolution: "eslint@npm:7.32.0"
  dependencies:
    "@babel/code-frame": 7.12.11
    "@eslint/eslintrc": ^0.4.3
    "@humanwhocodes/config-array": ^0.5.0
    ajv: ^6.10.0
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.0.1
    doctrine: ^3.0.0
    enquirer: ^2.3.5
    escape-string-regexp: ^4.0.0
    eslint-scope: ^5.1.1
    eslint-utils: ^2.1.0
    eslint-visitor-keys: ^2.0.0
    espree: ^7.3.1
    esquery: ^1.4.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    functional-red-black-tree: ^1.0.1
    glob-parent: ^5.1.2
    globals: ^13.6.0
    ignore: ^4.0.6
    import-fresh: ^3.0.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    js-yaml: ^3.13.1
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.0.4
    natural-compare: ^1.4.0
    optionator: ^0.9.1
    progress: ^2.0.0
    regexpp: ^3.1.0
    semver: ^7.2.1
    strip-ansi: ^6.0.0
    strip-json-comments: ^3.1.0
    table: ^6.0.9
    text-table: ^0.2.0
    v8-compile-cache: ^2.0.3
  bin:
    eslint: bin/eslint.js
  checksum: cc85af9985a3a11085c011f3d27abe8111006d34cc274291b3c4d7bea51a4e2ff6135780249becd919ba7f6d6d1ecc38a6b73dacb6a7be08d38453b344dc8d37
  languageName: node
  linkType: hard

"espree@npm:^7.3.0, espree@npm:^7.3.1":
  version: 7.3.1
  resolution: "espree@npm:7.3.1"
  dependencies:
    acorn: ^7.4.0
    acorn-jsx: ^5.3.1
    eslint-visitor-keys: ^1.3.0
  checksum: aa9b50dcce883449af2e23bc2b8d9abb77118f96f4cb313935d6b220f77137eaef7724a83c3f6243b96bc0e4ab14766198e60818caad99f9519ae5a336a39b45
  languageName: node
  linkType: hard

"espree@npm:^9.0.0":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: aefb0d2596c230118656cd4ec7532d447333a410a48834d80ea648b1e7b5c9bc9ed8b5e33a89cb04e487b60d622f44cf5713bf4abed7c97343edefdc84a35900
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 6151e6f9828abe2259e57f5fd3761335bb0d2ebd76dc1a01048ccee22fabcfef3c0859300f6d83ff0d1927849368775ec5a6d265dde2f6de5a1be1721cd94efc
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-pubsub@npm:4.3.0":
  version: 4.3.0
  resolution: "event-pubsub@npm:4.3.0"
  checksum: 6940f57790c01a967b7c637f1c9fd000ee968a1d5894186ffb3356ffbe174c70e22e62adbbcfcee3f305482d99b6abe7613c1c27c909b07adc9127dc16c8cf73
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"evtd@npm:^0.2.2, evtd@npm:^0.2.4":
  version: 0.2.4
  resolution: "evtd@npm:0.2.4"
  checksum: 1f9151a077c83c0f63df40a1b4e6d844616b502e60950b0e08674f05fed3257ca64afd71d1e24e4d8f55e7d8c32fe8e1797d87b2d2b14e1237be159010330ec1
  languageName: node
  linkType: hard

"execa@npm:^0.8.0":
  version: 0.8.0
  resolution: "execa@npm:0.8.0"
  dependencies:
    cross-spawn: ^5.0.1
    get-stream: ^3.0.0
    is-stream: ^1.1.0
    npm-run-path: ^2.0.0
    p-finally: ^1.0.0
    signal-exit: ^3.0.0
    strip-eof: ^1.0.0
  checksum: c2a4bf6e051737e46bee61a93ec286cb71a05f16650a1918c8d6262ba9f0bac031472252411baa8c78b7f432f10cb4c601349403774d69be2ebd864e9b1eca60
  languageName: node
  linkType: hard

"execa@npm:^1.0.0":
  version: 1.0.0
  resolution: "execa@npm:1.0.0"
  dependencies:
    cross-spawn: ^6.0.0
    get-stream: ^4.0.0
    is-stream: ^1.1.0
    npm-run-path: ^2.0.0
    p-finally: ^1.0.0
    signal-exit: ^3.0.0
    strip-eof: ^1.0.0
  checksum: ddf1342c1c7d02dd93b41364cd847640f6163350d9439071abf70bf4ceb1b9b2b2e37f54babb1d8dc1df8e0d8def32d0e81e74a2e62c3e1d70c303eb4c306bc4
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"express@npm:^4.17.3":
  version: 4.18.2
  resolution: "express@npm:4.18.2"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.1
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.5.0
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.2.0
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.1
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.7
    proxy-addr: ~2.0.7
    qs: 6.11.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.18.0
    serve-static: 1.15.0
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 3c4b9b076879442f6b968fe53d85d9f1eeacbb4f4c41e5f16cc36d77ce39a2b0d81b3f250514982110d815b2f7173f5561367f9110fcc541f9371948e8c8b037
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.7, fast-glob@npm:^3.2.9":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: b6f3add6403e02cf3a798bfbb1183d0f6da2afd368f27456010c0bc1f9640aea308243d4cb2c0ab142f618276e65ecb8be1661d7c62a7b4e5ba774b9ce5432e5
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: ^1.0.4
  checksum: 0170e6bfcd5d57a70412440b8ef600da6de3b2a6c5966aeaf0a852d542daff506a0ee92d6de7679d1de82e644bce69d7a574a6c93f0b03964b5337eed75ada1a
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: ">=0.5.1"
  checksum: d49a62caf027f871149fc2b3f3c7104dc6d62744277eb6f9f36e2d5714e847d846b9f7f0d0b7169b25a012e24a594cde11a93034b30732e4c683f20b8a5019fa
  languageName: node
  linkType: hard

"figures@npm:^2.0.0":
  version: 2.0.0
  resolution: "figures@npm:2.0.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 081beb16ea57d1716f8447c694f637668322398b57017b20929376aaf5def9823b35245b734cdd87e4832dc96e9c6f46274833cada77bfe15e5f980fea1fd21f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"finalhandler@npm:1.2.0":
  version: 1.2.0
  resolution: "finalhandler@npm:1.2.0"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: 2.4.1
    parseurl: ~1.3.3
    statuses: 2.0.1
    unpipe: ~1.0.0
  checksum: 92effbfd32e22a7dff2994acedbd9bcc3aa646a3e919ea6a53238090e87097f8ef07cced90aa2cc421abdf993aefbdd5b00104d55c7c5479a8d00ed105b45716
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^3.0.2
    pkg-dir: ^4.1.0
  checksum: 1e61c2e64f5c0b1c535bd85939ae73b0e5773142713273818cc0b393ee3555fb0fd44e1a5b161b8b6c3e03e98c2fcc9c227d784850a13a90a8ab576869576817
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.1.0
  resolution: "flat-cache@npm:3.1.0"
  dependencies:
    flatted: ^3.2.7
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: 99312601d5b90f44aef403f17f056dc09be7e437703740b166cdc9386d99e681f74e6b6e8bd7d010bda66904ea643c9527276b1b80308a2119741d94108a4d8f
  languageName: node
  linkType: hard

"flatted@npm:^3.2.7":
  version: 3.2.7
  resolution: "flatted@npm:3.2.7"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0":
  version: 1.15.2
  resolution: "follow-redirects@npm:1.15.2"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: faa66059b66358ba65c234c2f2a37fcec029dc22775f35d9ad6abac56003268baf41e55f9ee645957b32c7d9f62baf1f0b906e68267276f54ec4b4c597c2b190
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 139d270bc82dc9e6f8bc045fe2aae4001dc2472157044fdfad376d0a3457f77857fa883c1c8b21b491c6caade9a926a4bed3d3d2e8d3c9202b151a4cbbd0bcd5
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fraction.js@npm:^4.2.0":
  version: 4.3.6
  resolution: "fraction.js@npm:4.3.6"
  checksum: e96ae77e64ebfd442d3a5a01a3f0637b0663fc2440bcf2841b3ad9341ba24c81fb2e3e7142e43ef7d088558c6b3f8609df135b201adc7a1c674aea6a71384162
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-extra@npm:^9.1.0":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: ba71ba32e0faa74ab931b7a0031d1523c66a73e225de7426e275e238e312d07313d2da2d33e34a52aa406c8763ade5712eb3ec9ba4d9edce652bcacdc29e6b20
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs-monkey@npm:^1.0.4":
  version: 1.0.4
  resolution: "fs-monkey@npm:1.0.4"
  checksum: 8b254c982905c0b7e028eab22b410dc35a5c0019c1c860456f5f54ae6a61666e1cb8c6b700d6c88cc873694c00953c935847b9959cc4dcf274aacb8673c1e8bf
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: ca6c170f37640e2d94297da8bb4bf27a1d12bea3e00e6a3e007fd7aa32e37e000f5772acf941b4e4f3cf1c95c3752033d0c509af157ad8f526e7f00723b9eb9f
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.1, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.1":
  version: 1.2.1
  resolution: "get-intrinsic@npm:1.2.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
  checksum: 5b61d88552c24b0cf6fa2d1b3bc5459d7306f699de060d76442cce49a4721f52b8c560a33ab392cf5575b7810277d54ded9d4d39a1ea61855619ebc005aa7e5f
  languageName: node
  linkType: hard

"get-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "get-stream@npm:3.0.0"
  checksum: 36142f46005ed74ce3a45c55545ec4e7da8e243554179e345a786baf144e5c4a35fb7bdc49fadfa9f18bd08000589b6fe364abdadfc4e1eb0e1b9914a6bb9c56
  languageName: node
  linkType: hard

"get-stream@npm:^4.0.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: ^3.0.0
  checksum: 443e1914170c15bd52ff8ea6eff6dfc6d712b031303e36302d2778e3de2506af9ee964d6124010f7818736dcfde05c04ba7ca6cc26883106e084357a17ae7d73
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.1":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.3.4
  resolution: "glob@npm:10.3.4"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^2.0.3
    minimatch: ^9.0.1
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
    path-scurry: ^1.10.1
  bin:
    glob: dist/cjs/src/bin.js
  checksum: 176b97c124414401cb51329a93d2ba112cef8814adbed10348481916b9521b677773eee2691cb6b24d66632d8c8bb8913533f5ac4bfb2d0ef5454a1856082361
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.6.0, globals@npm:^13.9.0":
  version: 13.21.0
  resolution: "globals@npm:13.21.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 86c92ca8a04efd864c10852cd9abb1ebe6d447dcc72936783e66eaba1087d7dba5c9c3421a48d6ca722c319378754dbcc3f3f732dbe47592d7de908edf58a773
  languageName: node
  linkType: hard

"globby@npm:^11.0.2, globby@npm:^11.0.3":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 68071f313062315cd9dce55710e9496873945f1dd425107007058fc1629f93002a7649fcc3e464281ce02c7e809a35f5925504ab8105d972cf649f1f47cb7d6c
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-property-descriptors@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.1.1
  checksum: a6d3f0a266d0294d972e354782e872e2fe1b6495b321e6ef678c9b7a06a40408a6891817350c62e752adced73a94ac903c54734fee05bf65b1905ee1368194bb
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: febc5b5b531de8022806ad7407935e2135f1cc9e64636c3916c6842bd7995994ca3b29871ecd7954bd35f9e2986c17b3b227880484d22259e2f8e6ce63fd383e
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hash-sum@npm:^1.0.2":
  version: 1.0.2
  resolution: "hash-sum@npm:1.0.2"
  checksum: 268553ba6c84333f502481d101a7d65cd39f61963544f12fc3ce60264718f471796dbc37348cee08c5529f04fafeba041886a4d35721e34d6440a48a42629283
  languageName: node
  linkType: hard

"hash-sum@npm:^2.0.0":
  version: 2.0.0
  resolution: "hash-sum@npm:2.0.0"
  checksum: efeeacf09ecbd467202865403c3a1991fa15d4f4903c1148ecbe13223fdbf9ec6d7dc661e17e5ce6e776cd70d67b6ee4c82e0171318962435be45c1155175f3f
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"highlight.js@npm:^10.7.1":
  version: 10.7.3
  resolution: "highlight.js@npm:10.7.3"
  checksum: defeafcd546b535d710d8efb8e650af9e3b369ef53e28c3dc7893eacfe263200bba4c5fcf43524ae66d5c0c296b1af0870523ceae3e3104d24b7abf6374a4fea
  languageName: node
  linkType: hard

"highlight.js@npm:^11.5.0":
  version: 11.8.0
  resolution: "highlight.js@npm:11.8.0"
  checksum: d2578a57aee7315946ff19379053fd0a28b127baabf7617ab1d28d62cdc4eaf3d75053569cb8479a5afdc7a68f1ba9a6c1d612d8ae399b4b9aa43093b4fb6831
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: ^2.0.1
    obuf: ^1.0.0
    readable-stream: ^2.0.1
    wbuf: ^1.1.0
  checksum: 2de144115197967ad6eeee33faf41096c6ba87078703c5cb011632dcfbffeb45784569e0cf02c317bd79c48375597c8ec88c30fff5bb0b023e8f654fb6e9c06e
  languageName: node
  linkType: hard

"html-entities@npm:^2.3.2":
  version: 2.4.0
  resolution: "html-entities@npm:2.4.0"
  checksum: 25bea32642ce9ebd0eedc4d24381883ecb0335ccb8ac26379a0958b9b16652fdbaa725d70207ce54a51db24103436a698a8e454397d3ba8ad81460224751f1dc
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^6.0.2":
  version: 6.1.0
  resolution: "html-minifier-terser@npm:6.1.0"
  dependencies:
    camel-case: ^4.1.2
    clean-css: ^5.2.2
    commander: ^8.3.0
    he: ^1.2.0
    param-case: ^3.0.4
    relateurl: ^0.2.7
    terser: ^5.10.0
  bin:
    html-minifier-terser: cli.js
  checksum: ac52c14006476f773204c198b64838477859dc2879490040efab8979c0207424da55d59df7348153f412efa45a0840a1ca3c757bf14767d23a15e3e389d37a93
  languageName: node
  linkType: hard

"html-tags@npm:^2.0.0":
  version: 2.0.0
  resolution: "html-tags@npm:2.0.0"
  checksum: a02b47dd71de5572f16c9a1d88e2876fcc4d60bb36b7effce48cd3cd0bdd8fdcbf2602d968d2268d134767620d876edc08d8a6fc0abd9dc59a05e89d56251fbb
  languageName: node
  linkType: hard

"html-tags@npm:^3.3.1":
  version: 3.3.1
  resolution: "html-tags@npm:3.3.1"
  checksum: b4ef1d5a76b678e43cce46e3783d563607b1d550cab30b4f511211564574770aa8c658a400b100e588bc60b8234e59b35ff72c7851cc28f3b5403b13a2c6cbce
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:^5.1.0":
  version: 5.5.3
  resolution: "html-webpack-plugin@npm:5.5.3"
  dependencies:
    "@types/html-minifier-terser": ^6.0.0
    html-minifier-terser: ^6.0.2
    lodash: ^4.17.21
    pretty-error: ^4.0.0
    tapable: ^2.0.0
  peerDependencies:
    webpack: ^5.20.0
  checksum: ccf685195739c372ad641bbd0c9100a847904f34eedc7aff3ece7856cd6c78fd3746d2d615af1bb71e5727993fe711b89e9b744f033ed3fde646540bf5d5e954
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 64d7d1ae3a6933eb0e9a94e6f27be4af45a53a96c3c34e84ff57113787105a89fff9d1c3df263ef63add823df019b0e8f52f7121e32393bb5ce9a713bf100b41
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.3
    setprototypeof: 1.1.0
    statuses: ">= 1.4.0 < 2"
  checksum: a9654ee027e3d5de305a56db1d1461f25709ac23267c6dc28cdab8323e3f96caa58a9a6a5e93ac15d7285cee0c2f019378c3ada9026e7fe19c872d695f27de7c
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.8
  resolution: "http-parser-js@npm:0.5.8"
  checksum: 6bbdf2429858e8cf13c62375b0bfb6dc3955ca0f32e58237488bc86cd2378f31d31785fd3ac4ce93f1c74e0189cf8823c91f5cb061696214fd368d2452dc871d
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:^2.0.3":
  version: 2.0.6
  resolution: "http-proxy-middleware@npm:2.0.6"
  dependencies:
    "@types/http-proxy": ^1.17.8
    http-proxy: ^1.18.1
    is-glob: ^4.0.1
    is-plain-obj: ^3.0.0
    micromatch: ^4.0.2
  peerDependencies:
    "@types/express": ^4.17.13
  peerDependenciesMeta:
    "@types/express":
      optional: true
  checksum: 2ee85bc878afa6cbf34491e972ece0f5be0a3e5c98a60850cf40d2a9a5356e1fc57aab6cff33c1fc37691b0121c3a42602d2b1956c52577e87a5b77b62ae1c3a
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: ^4.0.0
    follow-redirects: ^1.0.0
    requires-port: ^1.0.0
  checksum: f5bd96bf83e0b1e4226633dbb51f8b056c3e6321917df402deacec31dd7fe433914fc7a2c1831cf7ae21e69c90b3a669b8f434723e9e8b71fd68afe30737b6a5
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^4.0.6":
  version: 4.0.6
  resolution: "ignore@npm:4.0.6"
  checksum: 248f82e50a430906f9ee7f35e1158e3ec4c3971451dd9f99c9bc1548261b4db2b99709f60ac6c6cac9333494384176cc4cc9b07acbe42d52ac6a09cad734d800
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 3d4c309c6006e2621659311783eaea7ebcd41fe4ca1d78c91c473157ad6666a57a2df790fe0d07a12300d9aac2888204d7be8d59f9aaf665b1c7fcdb432517ef
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.4
  resolution: "immutable@npm:4.3.4"
  checksum: de3edd964c394bab83432429d3fb0b4816b42f56050f2ca913ba520bd3068ec3e504230d0800332d3abc478616e8f55d3787424a90d0952e6aba864524f1afc3
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0, import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 78cb8d7d850d20a5e9a7f3620db31483aa00ad5f722ce03a55b110e5a723539b3716a3b463e2b96ce3fe286f33afc7c131fa2f91407528ba80cea98a7545d4c0
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: cfcfac6b873b701996d71ec82a7dd27ba92450afdb421e356f44044ed688df04567344c36cbacea7d01b1c39a4c732dc012570ebe9bebfb06f27314bca625349
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"ipaddr.js@npm:^2.0.1":
  version: 2.1.0
  resolution: "ipaddr.js@npm:2.1.0"
  checksum: 807a054f2bd720c4d97ee479d6c9e865c233bea21f139fb8dabd5a35c4226d2621c42e07b4ad94ff3f82add926a607d8d9d37c625ad0319f0e08f9f2bd1968e2
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-ci@npm:^1.0.10":
  version: 1.2.1
  resolution: "is-ci@npm:1.2.1"
  dependencies:
    ci-info: ^1.5.0
  bin:
    is-ci: bin.js
  checksum: eca06c5626e54ec01be6f9114a8f19b3f571602cfe66458e42ccc42e401e2ebbe1bd3b2fcaa93b5896b9c759e964f3c7f4d9b2d0f4fc4ef5dba78a7c4825e0be
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.13.0
  resolution: "is-core-module@npm:2.13.0"
  dependencies:
    has: ^1.0.3
  checksum: 053ab101fb390bfeb2333360fd131387bed54e476b26860dc7f5a700bbf34a0ec4454f7c8c4d43e8a0030957e4b3db6e16d35e1890ea6fb654c833095e040355
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-file-esm@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-file-esm@npm:1.0.0"
  dependencies:
    read-pkg-up: ^7.0.1
  checksum: d404834bb694464dbff9a66ef770bdb1d1ef25b55df088aa03f600ebc13c11ee102eac64bc8f10e03c37b69e067fee74e149b82aee2fff5cc970f91cac94ab30
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: eef9c6e15f68085fec19ff6a978a6f1b8f48018fd1265035552078ee945573594933b09bbd6f562553e2a241561439f1ef5339276eba68d272001343084cfab8
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-plain-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-plain-obj@npm:3.0.0"
  checksum: a6ebdf8e12ab73f33530641972a72a4b8aed6df04f762070d823808303e4f76d87d5ea5bd76f96a7bbe83d93f04ac7764429c29413bd9049853a69cb630fb21c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: e32d27061eef62c0847d303125440a38660517e586f2f3db7c9d179ae5b6674ab0f469d519b2e25c147a1a3bc87156d0d5f4d8821e0ce4a9ee7fe1fcf11ce45c
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 063c6bec9d5647aa6d42108d4c59723d2bd4ae42135a2d4db6eadbd49b7ea05b750fd69d279e5c7c45cf9da753ad2c00d8978be354d65aa9f6bb434969c6a2ae
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"jackspeak@npm:^2.0.3":
  version: 2.3.3
  resolution: "jackspeak@npm:2.3.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 4313a7c0cc44c7753c4cb9869935f0b06f4cf96827515f63f58ff46b3d2f6e29aba6b3b5151778397c3f5ae67ef8bfc48871967bd10343c27e90cff198ec7808
  languageName: node
  linkType: hard

"javascript-stringify@npm:^2.0.1":
  version: 2.1.0
  resolution: "javascript-stringify@npm:2.1.0"
  checksum: 009981ec84299da88795fc764221ed213e3d52251cc93a396430a7a02ae09f1163a9be36a36808689681a8e6113cf00fe97ec2eea2552df48111f79be59e9358
  languageName: node
  linkType: hard

"jest-worker@npm:^27.0.2, jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"jest-worker@npm:^28.0.2":
  version: 28.1.3
  resolution: "jest-worker@npm:28.1.3"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: e921c9a1b8f0909da9ea07dbf3592f95b653aef3a8bb0cbcd20fc7f9a795a1304adecac31eecb308992c167e8d7e75c522061fec38a5928ace0f9571c90169ca
  languageName: node
  linkType: hard

"joi@npm:^17.4.0":
  version: 17.10.1
  resolution: "joi@npm:17.10.1"
  dependencies:
    "@hapi/hoek": ^9.0.0
    "@hapi/topo": ^5.0.0
    "@sideway/address": ^4.1.3
    "@sideway/formula": ^3.0.1
    "@sideway/pinpoint": ^2.0.0
  checksum: 7affbb27ae9283596d6471871352b85ebf73a1e782c0be1998bce1ecc86ba48bee834c8af9a44d7143bb470654d6ae1248a29009ee5463618675d655dbd69306
  languageName: node
  linkType: hard

"js-message@npm:1.0.7":
  version: 1.0.7
  resolution: "js-message@npm:1.0.7"
  checksum: 18dcc4d80356e8b5be978ca7838d96d4e350a1cb8adc5741c229dec6df09f53bfed7c75c1f360171d2d791a14e2f077d6c2b1013ba899ded7a27d7dfcd4f3784
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.2":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.1":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.3
  resolution: "keyv@npm:4.5.3"
  dependencies:
    json-buffer: 3.0.1
  checksum: 3ffb4d5b72b6b4b4af443bbb75ca2526b23c750fccb5ac4c267c6116888b4b65681015c2833cb20d26cf3e6e32dac6b988c77f7f022e1a571b7d90f1442257da
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"klona@npm:^2.0.5":
  version: 2.0.6
  resolution: "klona@npm:2.0.6"
  checksum: ac9ee3732e42b96feb67faae4d27cf49494e8a3bf3fa7115ce242fe04786788e0aff4741a07a45a2462e2079aa983d73d38519c85d65b70ef11447bbc3c58ce7
  languageName: node
  linkType: hard

"launch-editor-middleware@npm:^2.2.1":
  version: 2.6.0
  resolution: "launch-editor-middleware@npm:2.6.0"
  dependencies:
    launch-editor: ^2.6.0
  checksum: 6e39d216a64f4749cfe9c04d8a576daf0585dd7c1c17a82be5a45192b1ddcb2c565e252a8dbb10614ed9746063c5c69a487e2470e795f40567bd447d43be49d0
  languageName: node
  linkType: hard

"launch-editor@npm:^2.2.1, launch-editor@npm:^2.6.0":
  version: 2.6.0
  resolution: "launch-editor@npm:2.6.0"
  dependencies:
    picocolors: ^1.0.0
    shell-quote: ^1.7.3
  checksum: 48e4230643e8fdb5c14c11314706d58d9f3fbafe2606be3d6e37da1918ad8bfe39dd87875c726a1b59b9f4da99d87ec3e36d4c528464f0b820f9e91e5cb1c02d
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:^2.0.3":
  version: 2.1.0
  resolution: "lilconfig@npm:2.1.0"
  checksum: 8549bb352b8192375fed4a74694cd61ad293904eee33f9d4866c2192865c44c4eb35d10782966242634e0cbc1e91fe62b1247f148dc5514918e3a966da7ea117
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.1.0, loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"loader-utils@npm:^1.0.2, loader-utils@npm:^1.1.0":
  version: 1.4.2
  resolution: "loader-utils@npm:1.4.2"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^1.0.1
  checksum: eb6fb622efc0ffd1abdf68a2022f9eac62bef8ec599cf8adb75e94d1d338381780be6278534170e99edc03380a6d29bc7eb1563c89ce17c5fed3a0b17f1ad804
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^2.1.2
  checksum: a5281f5fff1eaa310ad5e1164095689443630f3411e927f95031ab4fb83b4a98f388185bb1fe949e8ab8d4247004336a625e9255c22122b815bb9a4c5d8fc3b7
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 05cbffad6e2adbb331a4e16fbd826e7faee403a1a04873b82b42c0f22090f280839f85b95393f487c1303c8a3d2a010048bf06151a6cbe03eee4d388fb0a12d2
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.defaultsdeep@npm:^4.6.1":
  version: 4.6.1
  resolution: "lodash.defaultsdeep@npm:4.6.1"
  checksum: 1f346f16158b760545ca99553cb13e907a28b281425751af6bfe681387b9e5685438a7ddbfd36a8d5cc8bda066867a134aa31416f17e318db8c461c377810a76
  languageName: node
  linkType: hard

"lodash.escape@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.escape@npm:4.0.1"
  checksum: fcb54f457497256964d619d5cccbd80a961916fca60df3fe0fa3e7f052715c2944c0ed5aefb4f9e047d127d44aa2d55555f3350cb42c6549e9e293fb30b41e7f
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.invokemap@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.invokemap@npm:4.6.0"
  checksum: 646ceebbefbcb6da301f8c2868254680fd0bcdc6ada470495d9ae49c9c32938829c1b38a38c95d0258409a9655f85db404b16e648381c7450b7ed3d9c52d8808
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: 5a6c59161914e1bae23438a298c7433e83d935e0f59853fa862e691164696bc07f6dfa4c313d499fbf41ba8d53314e9850416502376705a357d24ee6ca33af78
  languageName: node
  linkType: hard

"lodash.mapvalues@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.mapvalues@npm:4.6.0"
  checksum: 0ff1b252fda318fc36e47c296984925e98fbb0fc5a2ecc4ef458f3c739a9476d47e40c95ac653e8314d132aa59c746d4276527b99d6e271940555c6e12d2babd
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 9ff3942feeccffa4f1fafa88d32f0d24fdc62fd15ded5a74a5f950ff5f0c6f61916157246744c620173dddf38d37095a92327d5fd3861e2063e736a5c207d089
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.pullall@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.pullall@npm:4.2.0"
  checksum: 7a5fbaedf186ec197ce1e0b9ba1d88a89773ebaf6a8291c7d273838cac59cb3b339cf36ef00e94172862ee84d2304c38face161846f08f5581d0553dcbdcd090
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: b463d8a382cfb5f0e71c504dcb6f807a7bd379ff1ea216669aa42c52fc28c54e404bfbd96791aa09e6df0de2c1d7b8f1b7f4b1a61f324d38fe98bc535aeee4f5
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: a4779b57a8d0f3c441af13d9afe7ecff22dd1b8ce1129849f71d9bbc8e8ee4e46dfb4b7c28f7ad3d67481edd6e51126e4e2a6ee276e25906d10f7140187c392d
  languageName: node
  linkType: hard

"lodash.uniqby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.uniqby@npm:4.7.0"
  checksum: 659264545a95726d1493123345aad8cbf56e17810fa9a0b029852c6d42bc80517696af09d99b23bef1845d10d95e01b8b4a1da578f22aeba7a30d3e0022a4938
  languageName: node
  linkType: hard

"lodash@npm:^4.17.14, lodash@npm:^4.17.20, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"log-update@npm:^2.3.0":
  version: 2.3.0
  resolution: "log-update@npm:2.3.0"
  dependencies:
    ansi-escapes: ^3.0.0
    cli-cursor: ^2.0.0
    wrap-ansi: ^3.0.1
  checksum: 84fd8e93bfc316eb6ca479a37743f2edcb7563fe5b9161205ce2980f0b3c822717b8f8f1871369697fcb0208521d7b8d00750c594edc3f8a8273dd8b48dd14a3
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^4.0.1, lru-cache@npm:^4.1.2":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: ^1.0.2
    yallist: ^2.1.2
  checksum: 4bb4b58a36cd7dc4dcec74cbe6a8f766a38b7426f1ff59d4cf7d82a2aa9b9565cd1cb98f6ff60ce5cd174524868d7bc9b7b1c294371851356066ca9ac4cf135a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.0.1
  resolution: "lru-cache@npm:10.0.1"
  checksum: 06f8d0e1ceabd76bb6f644a26dbb0b4c471b79c7b514c13c6856113879b3bf369eb7b497dad4ff2b7e2636db202412394865b33c332100876d838ad1372f0181
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.0":
  version: 0.30.3
  resolution: "magic-string@npm:0.30.3"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.4.15
  checksum: a5a9ddf9bd3bf49a2de1048bf358464f1bda7b3cc1311550f4a0ba8f81a4070e25445d53a5ee28850161336f1bff3cf28aa3320c6b4aeff45ce3e689f300b2f3
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^11.0.3":
  version: 11.1.1
  resolution: "make-fetch-happen@npm:11.1.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^17.0.0
    http-cache-semantics: ^4.1.1
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^5.0.0
    minipass-fetch: ^3.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^10.0.0
  checksum: 7268bf274a0f6dcf0343829489a4506603ff34bd0649c12058753900b0eb29191dce5dba12680719a5d0a983d3e57810f594a12f3c18494e93a1fbc6348a4540
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"memfs@npm:^3.4.3":
  version: 3.5.3
  resolution: "memfs@npm:3.5.3"
  dependencies:
    fs-monkey: ^1.0.4
  checksum: 18dfdeacad7c8047b976a6ccd58bc98ba76e122ad3ca0e50a21837fe2075fc0d9aafc58ab9cf2576c2b6889da1dd2503083f2364191b695273f40969db2ecc44
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 5abc259d2ae25bb06d19ce2b94a21632583c74e2a9109ee1ba7fd147aa7362b380d971e0251069f8b3eb7d48c21ac839e21fa177b335e82c76ec172e30c31a26
  languageName: node
  linkType: hard

"merge-source-map@npm:^1.1.0":
  version: 1.1.0
  resolution: "merge-source-map@npm:1.1.0"
  dependencies:
    source-map: ^0.6.1
  checksum: 945a83dcc59eff77dde709be1d3d6cb575c11cd7164a7ccdc1c6f0d463aad7c12750a510bdf84af2c05fac4615c4305d97ac90477975348bb901a905c8e92c4b
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4, micromatch@npm:^4.0.5":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27, mime-types@npm:^2.1.31, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 69c08205156a1f4906d9c46f9b4dc08d18a50176352e77fdeb645cedfe9f20c0b19865d465bd2dec27a5c432347f24dc07fc3695e11159d193f892834233e939
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^2.5.3":
  version: 2.7.6
  resolution: "mini-css-extract-plugin@npm:2.7.6"
  dependencies:
    schema-utils: ^4.0.0
  peerDependencies:
    webpack: ^5.0.0
  checksum: be6f7cefc6275168eb0a6b8fe977083a18c743c9612c9f00e6c1a62c3393ca7960e93fba1a7ebb09b75f36a0204ad087d772c1ef574bc29c90c0e8175a3c0b83
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 253487976bf485b612f16bf57463520a14f512662e592e95c571afdab1442a6a6864b6c88f248ce6fc4ff0b6de04ac7aa6c8bb51e868e99d1d65eb0658a708b5
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.4
  resolution: "minipass-fetch@npm:3.0.4"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: af7aad15d5c128ab1ebe52e043bdf7d62c3c6f0cecb9285b40d7b395e1375b45dcdfd40e63e93d26a0e8249c9efd5c325c65575aceee192883970ff8cb11364a
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.1":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.3":
  version: 7.0.3
  resolution: "minipass@npm:7.0.3"
  checksum: 6f1614f5b5b55568a46bca5fec0e7c46dac027691db27d0e1923a8192866903144cd962ac772c0e9f89b608ea818b702709c042bce98e190d258847d85461531
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.6":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"module-alias@npm:^2.2.2":
  version: 2.2.3
  resolution: "module-alias@npm:2.2.3"
  checksum: 6169187f69de8dcf8af8fab4d9e53ada6338a43f7670d38d0b27a089c28f9eb18d85a6fd46f11b54c63079a68449b85d071d7db0ac067f9f7faedbcd6231456d
  languageName: node
  linkType: hard

"mrmime@npm:^1.0.0":
  version: 1.0.1
  resolution: "mrmime@npm:1.0.1"
  checksum: cc979da44bbbffebaa8eaf7a45117e851f2d4cb46a3ada6ceb78130466a04c15a0de9a9ce1c8b8ba6f6e1b8618866b1352992bf1757d241c0ddca558b9f28a77
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multicast-dns@npm:^7.2.5":
  version: 7.2.5
  resolution: "multicast-dns@npm:7.2.5"
  dependencies:
    dns-packet: ^5.2.2
    thunky: ^1.0.2
  bin:
    multicast-dns: cli.js
  checksum: 00b8a57df152d4cd0297946320a94b7c3cdf75a46a2247f32f958a8927dea42958177f9b7fdae69fab2e4e033fb3416881af1f5e9055a3e1542888767139e2fb
  languageName: node
  linkType: hard

"mz@npm:^2.4.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"naive-ui@npm:^2.34.4":
  version: 2.34.4
  resolution: "naive-ui@npm:2.34.4"
  dependencies:
    "@css-render/plugin-bem": ^0.15.10
    "@css-render/vue3-ssr": ^0.15.10
    "@types/katex": ^0.14.0
    "@types/lodash": ^4.14.181
    "@types/lodash-es": ^4.17.6
    async-validator: ^4.0.7
    css-render: ^0.15.10
    date-fns: ^2.28.0
    date-fns-tz: ^1.3.3
    evtd: ^0.2.4
    highlight.js: ^11.5.0
    lodash: ^4.17.21
    lodash-es: ^4.17.21
    seemly: ^0.3.6
    treemate: ^0.3.11
    vdirs: ^0.1.8
    vooks: ^0.2.12
    vueuc: ^0.4.51
  peerDependencies:
    vue: ^3.0.0
  checksum: 0e91d9208ec362f53bd23571db6a1b4a213bbbd225330e788b0e8610d06628de46df19c068c098b8d57ac3fbe53dbbc8ce25c89e4790c2d852130a23be504d52
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.6
  resolution: "nanoid@npm:3.3.6"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 7d0eda657002738aa5206107bd0580aead6c95c460ef1bdd0b1a87a9c7ae6277ac2e9b945306aaa5b32c6dcb7feaf462d0f552e7f8b5718abfc6ead5c94a71b3
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 0b4af3b5bb5d86c289f7a026303d192a7eb4417231fe47245c460baeabae7277bcd8fd9c728fb6bd62c30b3e15cd6620373e2cf33353b095d8b403d3e8a15aff
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-forge@npm:^1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 08fb072d3d670599c89a1704b3e9c649ff1b998256737f0e06fbd1a5bf41cae4457ccaee32d95052d80bbafd9ffe01284e078c8071f0267dc9744e51c5ed42a9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 9.4.0
  resolution: "node-gyp@npm:9.4.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^11.0.3
    nopt: ^6.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 78b404e2e0639d64e145845f7f5a3cb20c0520cdaf6dda2f6e025e9b644077202ea7de1232396ba5bde3fee84cdc79604feebe6ba3ec84d464c85d407bb5da99
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.13":
  version: 2.0.13
  resolution: "node-releases@npm:2.0.13"
  checksum: 17ec8f315dba62710cae71a8dad3cd0288ba943d2ece43504b3b1aa8625bf138637798ab470b1d9035b0545996f63000a8a926e0f6d35d0996424f8b6d36dda3
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-path@npm:^1.0.0":
  version: 1.0.0
  resolution: "normalize-path@npm:1.0.0"
  checksum: b8b66ac272c7bbf63a3e47a050a5570b137c3e5085e410e2a0fffe1ff663e24e53c51364666586bd38f931e357d3cb5760a30e3c2f902a03f84bd7bf06e7f727
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 4a4944631173e7d521d6b80e4c85ccaeceb2870f315584fa30121f505a6dfd86439c5e3fdd8cd9e0e291290c41d0c3599f0cb12ab356722ed242584c30348e50
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: ^2.0.0
  checksum: acd5ad81648ba4588ba5a8effb1d98d2b339d31be16826a118d50f182a134ac523172101b82eab1d01cb4c2ba358e857d54cfafd8163a1ffe7bd52100b741125
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.9.0":
  version: 1.12.3
  resolution: "object-inspect@npm:1.12.3"
  checksum: dabfd824d97a5f407e6d5d24810d888859f6be394d8b733a77442b277e0808860555176719c5905e765e3743a7cada6b8b0a3b85e5331c530fd418cc8ae991db
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.0":
  version: 4.1.4
  resolution: "object.assign@npm:4.1.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: 76cab513a5999acbfe0ff355f15a6a125e71805fcf53de4e9d4e082e1989bdb81d1e329291e1e4e0ae7719f0e4ef80e88fb2d367ae60500d79d25a6224ac8864
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 41a2ba310e7b6f6c3b905af82c275bf8854896e2e4c5752966d64cbcd2f599cfffd5932006bcf3b8b419dfdacebb3a3912d5d94e10f1d0acab59876c8757f27f
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: ^1.0.0
  checksum: bb44015ac7a525d0fb43b029a583d4ad359834632b4424ca209b438aacf6d669dda81b5edfbdb42c22636e607b276ba5589f46694a729e3bc27948ce26f4cc1a
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"open@npm:^8.0.2, open@npm:^8.0.9":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: ^2.0.0
    is-docker: ^2.1.1
    is-wsl: ^2.2.0
  checksum: 6388bfff21b40cb9bd8f913f9130d107f2ed4724ea81a8fd29798ee322b361ca31fa2cdfb491a5c31e43a3996cfe9566741238c7a741ada8d7af1cb78d85cf26
  languageName: node
  linkType: hard

"opener@npm:^1.5.2":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"optionator@npm:^0.9.1":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": ^1.2.3
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
  checksum: 09281999441f2fe9c33a5eeab76700795365a061563d66b098923eb719251a42bdbe432790d35064d0816ead9296dbeb1ad51a733edf4167c96bd5d0882e428a
  languageName: node
  linkType: hard

"ora@npm:^5.3.0":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 93a654c53dc805dd5b5891bab16eb0ea46db8f66c4bfd99336ae929323b1af2b70a8b0654f8f1eae924b2b73d037031366d645f1fd18b3d30cbd15950cc4b1d4
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-retry@npm:^4.5.0":
  version: 4.6.2
  resolution: "p-retry@npm:4.6.2"
  dependencies:
    "@types/retry": 0.12.0
    retry: ^0.13.1
  checksum: 45c270bfddaffb4a895cea16cb760dcc72bdecb6cb45fef1971fa6ea2e91ddeafddefe01e444ac73e33b1b3d5d29fb0dd18a7effb294262437221ddc03ce0f2e
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^6.0.0":
  version: 6.0.1
  resolution: "parse5-htmlparser2-tree-adapter@npm:6.0.1"
  dependencies:
    parse5: ^6.0.1
  checksum: 1848378b355d027915645c13f13f982e60502d201f53bc2067a508bf2dba4aac08219fc781dcd160167f5f50f0c73f58d20fa4fb3d90ee46762c20234fa90a6d
  languageName: node
  linkType: hard

"parse5@npm:^5.1.1":
  version: 5.1.1
  resolution: "parse5@npm:5.1.1"
  checksum: 613a714af4c1101d1cb9f7cece2558e35b9ae8a0c03518223a4a1e35494624d9a9ad5fad4c13eab66a0e0adccd9aa3d522fc8f5f9cc19789e0579f3fa0bdfc65
  languageName: node
  linkType: hard

"parse5@npm:^6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 7d569a176c5460897f7c8f3377eff640d54132b9be51ae8a8fa4979af940830b2b0c296ce75e5bd8f4041520aadde13170dbdec44889975f906098ea0002f4bd
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0, path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: f7ab0ad42fe3fb8c7f11d0c4f849871e28fbd8e1add65c370e422512fc5887097b9cf34d09c1747d45c942a8c1e26468d6356e2df3f740bf177ab8ca7301ebfd
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: ^9.1.1 || ^10.0.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: e2557cff3a8fb8bc07afdd6ab163a92587884f9969b05bbbaf6fe7379348bfb09af9ed292af12ed32398b15fb443e81692047b786d1eeb6d898a51eb17ed7d90
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 69a14ea24db543e8b0f4353305c5eac6907917031340e5a8b37df688e52accd09e3cebfe1660b70d76b6bd89152f52183f28c74813dbf454ba1a01c82a38abce
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^0.2.1":
  version: 0.2.1
  resolution: "picocolors@npm:0.2.1"
  checksum: 3b0f441f0062def0c0f39e87b898ae7461c3a16ffc9f974f320b44c799418cabff17780ee647fda42b856a1dc45897e2c62047e1b546d94d6d5c6962f45427b2
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.26":
  version: 1.0.32
  resolution: "portfinder@npm:1.0.32"
  dependencies:
    async: ^2.6.4
    debug: ^3.2.7
    mkdirp: ^0.5.6
  checksum: 116b4aed1b9e16f6d5503823d966d9ffd41b1c2339e27f54c06cd2f3015a9d8ef53e2a53b57bc0a25af0885977b692007353aa28f9a0a98a44335cb50487240d
  languageName: node
  linkType: hard

"postcss-calc@npm:^8.2.3":
  version: 8.2.4
  resolution: "postcss-calc@npm:8.2.4"
  dependencies:
    postcss-selector-parser: ^6.0.9
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.2
  checksum: 314b4cebb0c4ed0cf8356b4bce71eca78f5a7842e6a3942a3bba49db168d5296b2bd93c3f735ae1c616f2651d94719ade33becc03c73d2d79c7394fb7f73eabb
  languageName: node
  linkType: hard

"postcss-colormin@npm:^5.3.1":
  version: 5.3.1
  resolution: "postcss-colormin@npm:5.3.1"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
    colord: ^2.9.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: e5778baab30877cd1f51e7dc9d2242a162aeca6360a52956acd7f668c5bc235c2ccb7e4df0370a804d65ebe00c5642366f061db53aa823f9ed99972cebd16024
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^5.1.3":
  version: 5.1.3
  resolution: "postcss-convert-values@npm:5.1.3"
  dependencies:
    browserslist: ^4.21.4
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: df48cdaffabf9737f9cfdc58a3dc2841cf282506a7a944f6c70236cff295d3a69f63de6e0935eeb8a9d3f504324e5b4e240abc29e21df9e35a02585d3060aeb5
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^5.1.2":
  version: 5.1.2
  resolution: "postcss-discard-comments@npm:5.1.2"
  peerDependencies:
    postcss: ^8.2.15
  checksum: abfd064ebc27aeaf5037643dd51ffaff74d1fa4db56b0523d073ace4248cbb64ffd9787bd6924b0983a9d0bd0e9bf9f10d73b120e50391dc236e0d26c812fa2a
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-discard-duplicates@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 88d6964201b1f4ed6bf7a32cefe68e86258bb6e42316ca01d9b32bdb18e7887d02594f89f4a2711d01b51ea6e3fcca8c54be18a59770fe5f4521c61d3eb6ca35
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-discard-empty@npm:5.1.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 970adb12fae5c214c0768236ad9a821552626e77dedbf24a8213d19cc2c4a531a757cd3b8cdd3fc22fb1742471b8692a1db5efe436a71236dec12b1318ee8ff4
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-discard-overridden@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: d64d4a545aa2c81b22542895cfcddc787d24119f294d35d29b0599a1c818b3cc51f4ee80b80f5a0a09db282453dd5ac49f104c2117cc09112d0ac9b40b499a41
  languageName: node
  linkType: hard

"postcss-loader@npm:^6.1.1":
  version: 6.2.1
  resolution: "postcss-loader@npm:6.2.1"
  dependencies:
    cosmiconfig: ^7.0.0
    klona: ^2.0.5
    semver: ^7.3.5
  peerDependencies:
    postcss: ^7.0.0 || ^8.0.1
    webpack: ^5.0.0
  checksum: e40ae79c3e39df37014677a817b001bd115d8b10dedf53a07b97513d93b1533cd702d7a48831bdd77b9a9484b1ec84a5d4a723f80e83fb28682c75b5e65e8a90
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^5.1.7":
  version: 5.1.7
  resolution: "postcss-merge-longhand@npm:5.1.7"
  dependencies:
    postcss-value-parser: ^4.2.0
    stylehacks: ^5.1.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: 81c3fc809f001b9b71a940148e242bdd6e2d77713d1bfffa15eb25c1f06f6648d5e57cb21645746d020a2a55ff31e1740d2b27900442913a9d53d8a01fb37e1b
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^5.1.4":
  version: 5.1.4
  resolution: "postcss-merge-rules@npm:5.1.4"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
    cssnano-utils: ^3.1.0
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 8ab6a569babe6cb412d6612adee74f053cea7edb91fa013398515ab36754b1fec830d68782ed8cdfb44cffdc6b78c79eab157bff650f428aa4460d3f3857447e
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-minify-font-values@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 35e858fa41efa05acdeb28f1c76579c409fdc7eabb1744c3bd76e895bb9fea341a016746362a67609688ab2471f587202b9a3e14ea28ad677754d663a2777ece
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-minify-gradients@npm:5.1.1"
  dependencies:
    colord: ^2.9.1
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 27354072a07c5e6dab36731103b94ca2354d4ed3c5bc6aacfdf2ede5a55fa324679d8fee5450800bc50888dbb5e9ed67569c0012040c2be128143d0cebb36d67
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^5.1.4":
  version: 5.1.4
  resolution: "postcss-minify-params@npm:5.1.4"
  dependencies:
    browserslist: ^4.21.4
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: bd63e2cc89edcf357bb5c2a16035f6d02ef676b8cede4213b2bddd42626b3d428403849188f95576fc9f03e43ebd73a29bf61d33a581be9a510b13b7f7f100d5
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^5.2.1":
  version: 5.2.1
  resolution: "postcss-minify-selectors@npm:5.2.1"
  dependencies:
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6fdbc84f99a60d56b43df8930707da397775e4c36062a106aea2fd2ac81b5e24e584a1892f4baa4469fa495cb87d1422560eaa8f6c9d500f9f0b691a5f95bab5
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-extract-imports@npm:3.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 4b65f2f1382d89c4bc3c0a1bdc5942f52f3cb19c110c57bd591ffab3a5fee03fcf831604168205b0c1b631a3dce2255c70b61aaae3ef39d69cd7eb450c2552d2
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-modules-local-by-default@npm:4.0.3"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: 2f8083687f3d6067885f8863dd32dbbb4f779cfcc7e52c17abede9311d84faf6d3ed8760e7c54c6380281732ae1f78e5e56a28baf3c271b33f450a11c9e30485
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-scope@npm:3.0.0"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 330b9398dbd44c992c92b0dc612c0626135e2cc840fee41841eb61247a6cfed95af2bd6f67ead9dd9d0bb41f5b0367129d93c6e434fa3e9c58ade391d9a5a138
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-charset@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: e79d92971fc05b8b3c9b72f3535a574e077d13c69bef68156a0965f397fdf157de670da72b797f57b0e3bac8f38155b5dd1735ecab143b9cc4032d72138193b4
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-display-values@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: b6eb7b9b02c3bdd62bbc54e01e2b59733d73a1c156905d238e178762962efe0c6f5104544da39f32cade8a4fb40f10ff54b63a8ebfbdff51e8780afb9fbdcf86
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-positions@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d9afc233729c496463c7b1cdd06732469f401deb387484c3a2422125b46ec10b4af794c101f8c023af56f01970b72b535e88373b9058ecccbbf88db81662b3c4
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-repeat-style@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 2c6ad2b0ae10a1fda156b948c34f78c8f1e185513593de4d7e2480973586675520edfec427645fa168c337b0a6b3ceca26f92b96149741ca98a9806dad30d534
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-string@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6e549c6e5b2831e34c7bdd46d8419e2278f6af1d5eef6d26884a37c162844e60339340c57e5e06058cdbe32f27fc6258eef233e811ed2f71168ef2229c236ada
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-timing-functions@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: da550f50e90b0b23e17b67449a7d1efd1aa68288e66d4aa7614ca6f5cc012896be1972b7168eee673d27da36504faccf7b9f835c0f7e81243f966a42c8c030aa
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-unicode@npm:5.1.1"
  dependencies:
    browserslist: ^4.21.4
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 4c24d26cc9f4b19a9397db4e71dd600dab690f1de8e14a3809e2aa1452dbc3791c208c38a6316bbc142f29e934fdf02858e68c94038c06174d78a4937e0f273c
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-url@npm:5.1.0"
  dependencies:
    normalize-url: ^6.0.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 3bd4b3246d6600230bc827d1760b24cb3101827ec97570e3016cbe04dc0dd28f4dbe763245d1b9d476e182c843008fbea80823061f1d2219b96f0d5c724a24c0
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-whitespace@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 12d8fb6d1c1cba208cc08c1830959b7d7ad447c3f5581873f7e185f99a9a4230c43d3af21ca12c818e4690a5085a95b01635b762ad4a7bef69d642609b4c0e19
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^5.1.3":
  version: 5.1.3
  resolution: "postcss-ordered-values@npm:5.1.3"
  dependencies:
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6f3ca85b6ceffc68aadaf319d9ee4c5ac16d93195bf8cba2d1559b631555ad61941461cda6d3909faab86e52389846b2b36345cff8f0c3f4eb345b1b8efadcf9
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^5.1.2":
  version: 5.1.2
  resolution: "postcss-reduce-initial@npm:5.1.2"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 55db697f85231a81f1969d54c894e4773912d9ddb914f9b03d2e73abc4030f2e3bef4d7465756d0c1acfcc2c2d69974bfb50a972ab27546a7d68b5a4fc90282b
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-reduce-transforms@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 0c6af2cba20e3ff63eb9ad045e634ddfb9c3e5c0e614c020db2a02f3aa20632318c4ede9e0c995f9225d9a101e673de91c0a6e10bb2fa5da6d6c75d15a55882f
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4, postcss-selector-parser@npm:^6.0.5, postcss-selector-parser@npm:^6.0.9":
  version: 6.0.13
  resolution: "postcss-selector-parser@npm:6.0.13"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: f89163338a1ce3b8ece8e9055cd5a3165e79a15e1c408e18de5ad8f87796b61ec2d48a2902d179ae0c4b5de10fccd3a325a4e660596549b040bc5ad1b465f096
  languageName: node
  linkType: hard

"postcss-svgo@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-svgo@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
    svgo: ^2.7.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d86eb5213d9f700cf5efe3073799b485fb7cacae0c731db3d7749c9c2b1c9bc85e95e0baeca439d699ff32ea24815fc916c4071b08f67ed8219df229ce1129bd
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-unique-selectors@npm:5.1.1"
  dependencies:
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 637e7b786e8558265775c30400c54b6b3b24d4748923f4a39f16a65fd0e394f564ccc9f0a1d3c0e770618a7637a7502ea1d0d79f731d429cb202255253c23278
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^7.0.36":
  version: 7.0.39
  resolution: "postcss@npm:7.0.39"
  dependencies:
    picocolors: ^0.2.1
    source-map: ^0.6.1
  checksum: 4ac793f506c23259189064bdc921260d869a115a82b5e713973c5af8e94fbb5721a5cc3e1e26840500d7e1f1fa42a209747c5b1a151918a9bc11f0d7ed9048e3
  languageName: node
  linkType: hard

"postcss@npm:^8.1.10, postcss@npm:^8.2.6, postcss@npm:^8.3.5, postcss@npm:^8.4.21":
  version: 8.4.29
  resolution: "postcss@npm:8.4.29"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: dd6daa25e781db9ae5b651d9b7bfde0ec6e60e86a37da69a18eb4773d5ddd51e28fc4ff054fbdc04636a31462e6bf09a1e50986f69ac52b10d46b7457cd36d12
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier@npm:^1.18.2 || ^2.0.0":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: b49e409431bf129dd89238d64299ba80717b57ff5a6d1c1a8b1a28b590d998a34e083fa13573bc732bb8d2305becb4c9a4407f8486c81fa7d55100eb08263cf8
  languageName: node
  linkType: hard

"pretty-error@npm:^4.0.0":
  version: 4.0.0
  resolution: "pretty-error@npm:4.0.0"
  dependencies:
    lodash: ^4.17.20
    renderkid: ^3.0.0
  checksum: a5b9137365690104ded6947dca2e33360bf55e62a4acd91b1b0d7baa3970e43754c628cc9e16eafbdd4e8f8bcb260a5865475d4fc17c3106ff2d61db4e72cdf3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"progress-webpack-plugin@npm:^1.0.12":
  version: 1.0.16
  resolution: "progress-webpack-plugin@npm:1.0.16"
  dependencies:
    chalk: ^2.1.0
    figures: ^2.0.0
    log-update: ^2.3.0
  peerDependencies:
    webpack: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
  checksum: a651996e74d8f56508464983638256a8a74ad17deab630aa631922413dac14a1056de43341a75036ffdc9ae84549e14ccab15a6d3fb4b6ed549393973adfc8bb
  languageName: node
  linkType: hard

"progress@npm:^2.0.0":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: f67403fe7b34912148d9252cb7481266a354bd99ce82c835f79070643bb3c6583d10dbcfda4d41e04bbc1d8437e9af0fb1e1f2135727878f5308682a579429b7
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 856c0aae0ff2ad60881168334448e898ad7a0e45fe7386d114b150084254c01e200c957cf378378025df4e052c7890c5bd933939b0e0d2ecfcc1dc2f0b2991f5
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 39f760e09a2a3bbfe8f5287cf733ecdad69d6af2fe6f97ca95f24b8921858b91e9ea3c9eeec6e08cede96181b3bb33f95c6ffd8c77e63986508aa2e8159fa200
  languageName: node
  linkType: hard

"qs@npm:6.11.0":
  version: 6.11.0
  resolution: "qs@npm:6.11.0"
  dependencies:
    side-channel: ^1.0.4
  checksum: 6e1f29dd5385f7488ec74ac7b6c92f4d09a90408882d0c208414a34dd33badc1a621019d4c799a3df15ab9b1d0292f97c1dd71dc7c045e69f81a8064e5af7297
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.5.1":
  version: 2.5.1
  resolution: "raw-body@npm:2.5.1"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: 5362adff1575d691bb3f75998803a0ffed8c64eabeaa06e54b4ada25a0cd1b2ae7f4f5ec46565d1bec337e08b5ac90c76eaa0758de6f72a633f025d754dec29e
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: ^4.1.0
    read-pkg: ^5.2.0
    type-fest: ^0.8.1
  checksum: e4e93ce70e5905b490ca8f883eb9e48b5d3cebc6cd4527c25a0d8f3ae2903bd4121c5ab9c5a3e217ada0141098eeb661313c86fa008524b089b8ed0b7f165e44
  languageName: node
  linkType: hard

"read-pkg@npm:^5.1.1, read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: eb696e60528b29aebe10e499ba93f44991908c57d70f2d26f369e46b8b9afc208ef11b4ba64f67630f31df8b6872129e0a8933c8c53b7b4daf0eace536901222
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.0
  resolution: "regenerate-unicode-properties@npm:10.1.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: b1a8929588433ab8b9dc1a34cf3665b3b472f79f2af6ceae00d905fc496b332b9af09c6718fb28c730918f19a00dc1d7310adbaa9b72a2ec7ad2f435da8ace17
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.0
  resolution: "regenerator-runtime@npm:0.14.0"
  checksum: 1c977ad82a82a4412e4f639d65d22be376d3ebdd30da2c003eeafdaaacd03fc00c2320f18120007ee700900979284fc78a9f00da7fb593f6e6eeebc673fba9a3
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 20b6f9377d65954980fe044cfdd160de98df415b4bff38fbade67b3337efaf078308c4fed943067cd759827cc8cfeca9cb28ccda1f08333b85d6a2acbd022c27
  languageName: node
  linkType: hard

"regexpp@npm:^3.1.0":
  version: 3.2.0
  resolution: "regexpp@npm:3.2.0"
  checksum: a78dc5c7158ad9ddcfe01aa9144f46e192ddbfa7b263895a70a5c6c73edd9ce85faf7c0430e59ac38839e1734e275b9c3de5c57ee3ab6edc0e0b1bdebefccef8
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.3.1":
  version: 5.3.2
  resolution: "regexpu-core@npm:5.3.2"
  dependencies:
    "@babel/regjsgen": ^0.8.0
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.1.0
    regjsparser: ^0.9.1
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 95bb97088419f5396e07769b7de96f995f58137ad75fac5811fb5fe53737766dfff35d66a0ee66babb1eb55386ef981feaef392f9df6d671f3c124812ba24da2
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 5e1b76afe8f1d03c3beaf9e0d935dd467589c3625f6d65fb8ffa14f224d783a0fed4bf49c2c1b8211043ef92b6117313419edf055a098ed8342e340586741afc
  languageName: node
  linkType: hard

"relateurl@npm:^0.2.7":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 5891e792eae1dfc3da91c6fda76d6c3de0333a60aa5ad848982ebb6dccaa06e86385fb1235a1582c680a3d445d31be01c6bfc0804ebbcab5aaf53fa856fde6b6
  languageName: node
  linkType: hard

"renderkid@npm:^3.0.0":
  version: 3.0.0
  resolution: "renderkid@npm:3.0.0"
  dependencies:
    css-select: ^4.1.3
    dom-converter: ^0.2.0
    htmlparser2: ^6.1.0
    lodash: ^4.17.21
    strip-ansi: ^6.0.1
  checksum: 77162b62d6f33ab81f337c39efce0439ff0d1f6d441e29c35183151f83041c7850774fb904da163d6c844264d440d10557714e6daa0b19e4561a5cd4ef305d41
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.14.2":
  version: 1.22.4
  resolution: "resolve@npm:1.22.4"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 23f25174c2736ce24c6d918910e0d1f89b6b38fefa07a995dff864acd7863d59a7f049e691f93b4b2ee29696303390d921552b6d1b841ed4a8101f517e1d0124
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>":
  version: 1.22.4
  resolution: "resolve@patch:resolve@npm%3A1.22.4#~builtin<compat/resolve>::version=1.22.4&hash=07638b"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: c45f2545fdc4d21883861b032789e20aa67a2f2692f68da320cc84d5724cd02f2923766c5354b3210897e88f1a7b3d6d2c7c22faeead8eed7078e4c783a444bc
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: ^2.0.0
    signal-exit: ^3.0.2
  checksum: 482e13d02d834b6e5e3aa90304a8b5e840775d6f06916cc92a50038adf9f098dcc72405b567da8a37e137ae40ad3e31896fa3136ae62f7a426c2fbf53d036536
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 47c4d5be674f7c13eee4cfe927345023972197dbbdfba5d3af7e461d13b44de1bfd663bfc80d2f601f8ef3fc8164c16dd99655a221921954a65d044a2fc1233b
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2, safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sass-loader@npm:^13.3.2":
  version: 13.3.2
  resolution: "sass-loader@npm:13.3.2"
  dependencies:
    neo-async: ^2.6.2
  peerDependencies:
    fibers: ">= 3.1.0"
    node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
    sass: ^1.3.0
    sass-embedded: "*"
    webpack: ^5.0.0
  peerDependenciesMeta:
    fibers:
      optional: true
    node-sass:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
  checksum: 7394a8d1b818a289b9caabd979543c907b83e28ae08bc80ccb836e0ccabc4ae574c077ab2fa520ba5fb8abb2ec3e7c9822a1cbd8c58a28ff30018be9d1dc6c27
  languageName: node
  linkType: hard

"sass@npm:^1.64.0":
  version: 1.66.1
  resolution: "sass@npm:1.66.1"
  dependencies:
    chokidar: ">=3.0.0 <4.0.0"
    immutable: ^4.0.0
    source-map-js: ">=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 74fc11d0fcd5e16c5331b57dd59865705a299c64e89f2b99646869caeb011dc8d0b6144a6c74a90c264e9ef70654207dbf44fc9b7e3393f8bd14809b904c8a52
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0, schema-utils@npm:^3.1.1, schema-utils@npm:^3.2.0":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: ea56971926fac2487f0757da939a871388891bc87c6a82220d125d587b388f1704788f3706e7f63a7b70e49fc2db974c41343528caea60444afd5ce0fe4b85c0
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0":
  version: 4.2.0
  resolution: "schema-utils@npm:4.2.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: 26a0463d47683258106e6652e9aeb0823bf0b85843039e068b57da1892f7ae6b6b1094d48e9ed5ba5cbe9f7166469d880858b9d91abe8bd249421eb813850cde
  languageName: node
  linkType: hard

"seemly@npm:^0.3.6":
  version: 0.3.6
  resolution: "seemly@npm:0.3.6"
  checksum: 56d0472d992ff0e679d191941bffd80c1782ebba53cb4435f942431de6e083c50db4784b4d98771c45b704b79ebe17ea34f8cf6b20eda767f363513e1e5c3f08
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: d7e5fcc695a4804209d232a1b18624a5134be334d4e1114b0721f7a5e72bd73da483dcf41528c1af4f4f4892ad7cfd6a1e55c8ffb83f9c9fe723b738db609dbb
  languageName: node
  linkType: hard

"selfsigned@npm:^2.1.1":
  version: 2.1.1
  resolution: "selfsigned@npm:2.1.1"
  dependencies:
    node-forge: ^1
  checksum: aa9ce2150a54838978d5c0aee54d7ebe77649a32e4e690eb91775f71fdff773874a4fbafd0ac73d8ec3b702ff8a395c604df4f8e8868528f36fd6c15076fb43a
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.5.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.2.1, semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.8":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 12d8ad952fa353b0995bf180cdac205a4068b759a140e5d3c608317098b3575ac2f1e09182206bf2eb26120e1c0ed8fb92c48c592f6099680de56bb071423ca3
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 74fc07ebb58566b87b078ec63e5a3e41ecd987e4272ba67b7467e86c6ad51bc6b0b0154133b6d8b08a2ddda360464f71382f7ef864700f34844a76c8027817a8
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.0, serialize-javascript@npm:^6.0.1":
  version: 6.0.1
  resolution: "serialize-javascript@npm:6.0.1"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3c4f4cb61d0893b988415bdb67243637333f3f574e9e9cc9a006a2ced0b390b0b3b44aef8d51c951272a9002ec50885eefdc0298891bc27eb2fe7510ea87dc4f
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: ~1.3.4
    batch: 0.6.1
    debug: 2.6.9
    escape-html: ~1.0.3
    http-errors: ~1.6.2
    mime-types: ~2.1.17
    parseurl: ~1.3.2
  checksum: e2647ce13379485b98a53ba2ea3fbad4d44b57540d00663b02b976e426e6194d62ac465c0d862cb7057f65e0de8ab8a684aa095427a4b8612412eca0d300d22f
  languageName: node
  linkType: hard

"serve-static@npm:1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.18.0
  checksum: af57fc13be40d90a12562e98c0b7855cf6e8bd4c107fe9a45c212bf023058d54a1871b1c89511c3958f70626fff47faeb795f5d83f8cf88514dbaeb2b724464d
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 27cb44304d6c9e1a23bc6c706af4acaae1a7aa1054d4ec13c05f01a99fd4887109a83a8042b67ad90dbfcd100d43efc171ee036eb080667172079213242ca36e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: ^1.0.0
  checksum: 9eed1750301e622961ba5d588af2212505e96770ec376a37ab678f965795e995ade7ed44910f5d3d3cb5e10165a1847f52d3348c64e146b8be922f7707958908
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 404c5a752cd40f94591dfd9346da40a735a05139dac890ffc229afba610854d8799aaa52f87f7e0c94c5007f2c6af55bdcaeb584b56691926c5eaf41dc8f1372
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.7.3":
  version: 1.8.1
  resolution: "shell-quote@npm:1.8.1"
  checksum: 5f01201f4ef504d4c6a9d0d283fa17075f6770bfbe4c5850b074974c68062f37929ca61700d95ad2ac8822e14e8c4b990ca0e6e9272e64befd74ce5e19f0736b
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"single-spa-vue@npm:^2.1.0":
  version: 2.5.1
  resolution: "single-spa-vue@npm:2.5.1"
  checksum: 8d9f940f60d69d33cd09c3c0efc7c6a412dbada826dc1286908a1416c29332f5a4dc03ecead0d0abdcf3bd2d252039b179e4f764a4a5fe26289262d7ee0bc329
  languageName: node
  linkType: hard

"sirv@npm:^2.0.3":
  version: 2.0.3
  resolution: "sirv@npm:2.0.3"
  dependencies:
    "@polka/url": ^1.0.0-next.20
    mrmime: ^1.0.0
    totalist: ^3.0.0
  checksum: e2dfd4c97735a6ad6d842d0eec2cd9e3919ff0e46f0d228248c5753ad4b70b832711e77e1259c031c439cdb08303cc54d923685c92b0e890145cc733af7c5568
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.24":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: ^0.11.3
    uuid: ^8.3.2
    websocket-driver: ^0.7.4
  checksum: 355309b48d2c4e9755349daa29cea1c0d9ee23e49b983841c6bf7a20276b00d3c02343f9f33f26d2ee8b261a5a02961b52a25c8da88b2538c5b68d3071b4934c
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: ^2.0.0
    smart-buffer: ^4.2.0
  checksum: 259d9e3e8e1c9809a7f5c32238c3d4d2a36b39b83851d0f573bfde5f21c4b1288417ce1af06af1452569cd1eb0841169afd4998f0e04ba04656f6b7f0e46d748
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: e9ae98d22f69c88e7aff5b8778dc01c361ef635580e82d29e5c60a6533cc8f4d820803e67d7432581af0cc4fb49973125076ee3b90df191d153e223c004193b2
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.3.0
  resolution: "spdx-exceptions@npm:2.3.0"
  checksum: cb69a26fa3b46305637123cd37c85f75610e8c477b6476fa7354eb67c08128d159f1d36715f19be6f9daf4b680337deb8c65acdcae7f2608ba51931540687ac0
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.13
  resolution: "spdx-license-ids@npm:3.0.13"
  checksum: 3469d85c65f3245a279fa11afc250c3dca96e9e847f2f79d57f466940c5bb8495da08a542646086d499b7f24a74b8d0b42f3fc0f95d50ff99af1f599f6360ad7
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: ^4.1.0
    detect-node: ^2.0.4
    hpack.js: ^2.1.6
    obuf: ^1.1.2
    readable-stream: ^3.0.6
    wbuf: ^1.7.3
  checksum: 0fcaad3b836fb1ec0bdd39fa7008b9a7a84a553f12be6b736a2512613b323207ffc924b9551cef0378f7233c85916cff1118652e03a730bdb97c0e042243d56c
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: ^4.1.0
    handle-thing: ^2.0.0
    http-deceiver: ^1.2.7
    select-hose: ^2.0.0
    spdy-transport: ^3.0.0
  checksum: 2c739d0ff6f56ad36d2d754d0261d5ec358457bea7cbf77b1b05b0c6464f2ce65b85f196305f50b7bd9120723eb94bae9933466f28e67e5cd8cde4e27f1d75f8
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.5
  resolution: "ssri@npm:10.0.5"
  dependencies:
    minipass: ^7.0.3
  checksum: 0a31b65f21872dea1ed3f7c200d7bc1c1b91c15e419deca14f282508ba917cbb342c08a6814c7f68ca4ca4116dd1a85da2bbf39227480e50125a1ceffeecb750
  languageName: node
  linkType: hard

"ssri@npm:^8.0.1":
  version: 8.0.1
  resolution: "ssri@npm:8.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: bc447f5af814fa9713aa201ec2522208ae0f4d8f3bda7a1f445a797c7b929a02720436ff7c478fb5edc4045adb02b1b88d2341b436a80798734e2494f1067b36
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: 2ff482bb100285d16dd75cd8f7c60ab652570e8952c0bfa91828a2b5f646a0ff533f14596ea4eabd48bb7f4aeea408dce8f8515812b975d958a4cc4fa6b9dfeb
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: bae1596873595c4610993fa84f86a3387d67586401c1816ea048c0196800c0646c4d2da98c2ee80557fd9eff05877efe33b91ba6cd052658ed96ddc85d19067d
  languageName: node
  linkType: hard

"standalone-single-spa-webpack-plugin@npm:^3.0.0":
  version: 3.0.0
  resolution: "standalone-single-spa-webpack-plugin@npm:3.0.0"
  peerDependencies:
    html-webpack-plugin: "*"
    webpack: "*"
  checksum: 8b7c29c6b19dcad585c93909440a22e74ccffe2274ddfff30ad187189806f2b98f9403392473e9618d9de4c26d4084d5c3cd96f1e0aaae98bc8c920cd69759aa
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^2.1.1":
  version: 2.1.1
  resolution: "string-width@npm:2.1.1"
  dependencies:
    is-fullwidth-code-point: ^2.0.0
    strip-ansi: ^4.0.0
  checksum: d6173abe088c615c8dffaf3861dc5d5906ed3dc2d6fd67ff2bd2e2b5dce7fd683c5240699cf0b1b8aa679a3b3bd6b28b5053c824cb89b813d7f6541d8f89064a
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-ansi@npm:4.0.0"
  dependencies:
    ansi-regex: ^3.0.0
  checksum: d9186e6c0cf78f25274f6750ee5e4a5725fb91b70fdd79aa5fe648eab092a0ec5b9621b22d69d4534a56319f75d8944efbd84e3afa8d4ad1b9a9491f12c84eca
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 40bc8ddd7e072f8ba0c2d6d05267b4e0a4800898c3435b5fb5f5a21e6e47dfaff18467e7aa0d1844bb5d6274c3097246595841fbfeb317e541974ee992cac506
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-indent@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-indent@npm:2.0.0"
  checksum: 7d9080d02ddace616ebbc17846e41d3880cb147e2a81e51142281322ded6b05b230a4fb12c2e5266f62735cf8f5fb9839e55d74799d11f26bcc8c71ca049a0ba
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.0, strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"stylehacks@npm:^5.1.1":
  version: 5.1.1
  resolution: "stylehacks@npm:5.1.1"
  dependencies:
    browserslist: ^4.21.4
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.2.15
  checksum: 11175366ef52de65bf06cefba0ddc9db286dc3a1451fd2989e74c6ea47091a02329a4bf6ce10b1a36950056927b6bbbe47c5ab3a1f4c7032df932d010fbde5a2
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-tags@npm:^1.0.0":
  version: 1.0.0
  resolution: "svg-tags@npm:1.0.0"
  checksum: 407e5ef87cfa2fb81c61d738081c2decd022ce13b922d035b214b49810630bf5d1409255a4beb3a940b77b32f6957806deff16f1bf0ce1ab11c7a184115a0b7f
  languageName: node
  linkType: hard

"svgo@npm:^2.7.0":
  version: 2.8.0
  resolution: "svgo@npm:2.8.0"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^4.1.3
    css-tree: ^1.1.3
    csso: ^4.2.0
    picocolors: ^1.0.0
    stable: ^0.1.8
  bin:
    svgo: bin/svgo
  checksum: b92f71a8541468ffd0b81b8cdb36b1e242eea320bf3c1a9b2c8809945853e9d8c80c19744267eb91cabf06ae9d5fff3592d677df85a31be4ed59ff78534fa420
  languageName: node
  linkType: hard

"systemjs-webpack-interop@npm:^2.3.7":
  version: 2.3.7
  resolution: "systemjs-webpack-interop@npm:2.3.7"
  peerDependencies:
    webpack: "*"
  checksum: 6a294aa45281b208a43a2e8e358a58e1f37605c322c40180d5cc3bd7a283d4b5c310110274f8b2e96e6d8bf081a101bda6301f962669a1b0722bbd9043ce6933
  languageName: node
  linkType: hard

"table@npm:^6.0.9":
  version: 6.8.1
  resolution: "table@npm:6.8.1"
  dependencies:
    ajv: ^8.0.1
    lodash.truncate: ^4.4.2
    slice-ansi: ^4.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  checksum: 08249c7046125d9d0a944a6e96cfe9ec66908d6b8a9db125531be6eb05fa0de047fd5542e9d43b4f987057f00a093b276b8d3e19af162a9c40db2681058fd306
  languageName: node
  linkType: hard

"tapable@npm:^2.0.0, tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.2.0
  resolution: "tar@npm:6.2.0"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: db4d9fe74a2082c3a5016630092c54c8375ff3b280186938cfd104f2e089c4fd9bad58688ef6be9cf186a889671bf355c7cda38f09bbf60604b281715ca57f5c
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.1.1, terser-webpack-plugin@npm:^5.3.7":
  version: 5.3.9
  resolution: "terser-webpack-plugin@npm:5.3.9"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.17
    jest-worker: ^27.4.5
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.1
    terser: ^5.16.8
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 41705713d6f9cb83287936b21e27c658891c78c4392159f5148b5623f0e8c48559869779619b058382a4c9758e7820ea034695e57dc7c474b4962b79f553bc5f
  languageName: node
  linkType: hard

"terser@npm:^5.10.0, terser@npm:^5.16.8":
  version: 5.19.4
  resolution: "terser@npm:5.19.4"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 09273ce7d3fbe8fea0ec2603ad1c06cc304838bdac42bbfe77835b0b0b6c4a894054575ca518fe16c95d5c401574a8c703f4fde97da45f1c972ea568e6ecafda
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"thread-loader@npm:^3.0.0":
  version: 3.0.4
  resolution: "thread-loader@npm:3.0.4"
  dependencies:
    json-parse-better-errors: ^1.0.2
    loader-runner: ^4.1.0
    loader-utils: ^2.0.0
    neo-async: ^2.6.2
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.27.0 || ^5.0.0
  checksum: 832edc6eac46df148465feb8d3e3e67a30ea82d1d29401ca1c6461d1a0386c6d1fed05739887fc9c69a7d189a68ca1686eaad214f283825e355de9b42663bcf0
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 993096c472b6b8f30e29dc777a8d17720e4cab448375041f20c0cb802a09a7fb2217f2a3e8cdc11851faa71c957e2db309357367fc9d7af3cb7a4d00f4b66034
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"totalist@npm:^3.0.0":
  version: 3.0.1
  resolution: "totalist@npm:3.0.1"
  checksum: 5132d562cf88ff93fd710770a92f31dbe67cc19b5c6ccae2efc0da327f0954d211bbfd9456389655d726c624f284b4a23112f56d1da931ca7cfabbe1f45e778a
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"treemate@npm:^0.3.11":
  version: 0.3.11
  resolution: "treemate@npm:0.3.11"
  checksum: 0c6ccbc6c5ce7faf27f5f48669c4734aa93ba4064a77f1324af02779aab6333986f6b32748aafe8de1ba99da8f8d9a027fe9c7c1d5389b4768edb5fc6a77fca2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 329ea56123005922f39642318e3d1f0f8265d1e7fcb92c633e0809521da75eeaca28d2cf96d7248229deb40e5c19adf408259f4b9640afd20d13aecc1430f3ad
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: b2188e6e4b21557f6e92960ec496d28a51d68658018cba8b597bd3ef757721d1db309f120ae987abeeda874511d14b776157ff809f23c6d1ce8f83b9b2b7d60f
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: d61c4b2eba24009033ae4500d7d818a94fd6d1b481a8111612ee141400d5f1db46f199c014766b9fa9b31a6a7374d96fc748c6d688a78a3ce5a33123839becb7
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 8d6f5f586b9ce1ed0e84a37df6b42fdba1317a05b5df0c249962bd5da89528771e2d149837cad11aa26bcb84c35355cb9f58a10c3d41fa3b899181ece6c85220
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: ^4.0.0
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 0884b58365af59f89739e6f71e3feacb5b1b41f2df2d842d0757933620e6de08eff347d27e9d499b43c40476cbaf7988638d3acb2ffbcb9d35fd035591adfd15
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.0
  resolution: "universalify@npm:2.0.0"
  checksum: 2406a4edf4a8830aa6813278bab1f953a8e40f2f63a37873ffa9a3bc8f9745d06cc8e88f3572cb899b7e509013f7f6fcc3e37e8a6d914167a5381d8440518c44
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.11":
  version: 1.0.11
  resolution: "update-browserslist-db@npm:1.0.11"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: b98327518f9a345c7cad5437afae4d2ae7d865f9779554baf2a200fdf4bac4969076b679b1115434bd6557376bdd37ca7583d0f9b8f8e302d7d4cc1e91b5f231
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 97ffd3bd2bb80c773429d3fb8396469115cd190dded1e733f190d8b602bd0a1bcd6216b7ce3c4395ee3c79e3c879c19d268dbaae3093564cb169ad1212d436f4
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"v8-compile-cache@npm:^2.0.3":
  version: 2.4.0
  resolution: "v8-compile-cache@npm:2.4.0"
  checksum: 8eb6ddb59d86f24566503f1e6ca98f3e6f43599f05359bd3ab737eaaf1585b338091478a4d3d5c2646632cf8030288d7888684ea62238cdce15a65ae2416718f
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vdirs@npm:^0.1.4, vdirs@npm:^0.1.8":
  version: 0.1.8
  resolution: "vdirs@npm:0.1.8"
  dependencies:
    evtd: ^0.2.2
  peerDependencies:
    vue: ^3.0.11
  checksum: a7be8ccad3e72f2891150d53085b8f924cc9d9a9e474cd58827e81417e8feef7f5a8ecbb00efa7631592bf5b3be0a0fa40da41789fcf18dab2cec2bddd01ea47
  languageName: node
  linkType: hard

"vooks@npm:^0.2.12, vooks@npm:^0.2.4":
  version: 0.2.12
  resolution: "vooks@npm:0.2.12"
  dependencies:
    evtd: ^0.2.2
  peerDependencies:
    vue: ^3.0.0
  checksum: e6841ec5b6cb3938ce3ba0822a209fc4fd9cbb18af7e5034a979f3a80a6f6cfcdadc402bf8992ddffe09a5796c746731b7f6c3366c4a2e8309278f657ae8ea18
  languageName: node
  linkType: hard

"vue-cli-plugin-single-spa@npm:~3.3.0":
  version: 3.3.0
  resolution: "vue-cli-plugin-single-spa@npm:3.3.0"
  dependencies:
    semver: ^7.3.5
    standalone-single-spa-webpack-plugin: ^3.0.0
    systemjs-webpack-interop: ^2.3.7
  peerDependencies:
    webpack: "*"
  checksum: 140113a4a09e64b434bdac39415bc653fa4910ab25d3d6363db6aa385ff2b5af2e85d90c679131e67adee5f7d2df63c8664b4ea02750753ead2e4c5d7fbfe15e
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^8.0.1":
  version: 8.3.0
  resolution: "vue-eslint-parser@npm:8.3.0"
  dependencies:
    debug: ^4.3.2
    eslint-scope: ^7.0.0
    eslint-visitor-keys: ^3.1.0
    espree: ^9.0.0
    esquery: ^1.4.0
    lodash: ^4.17.21
    semver: ^7.3.5
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 8cc751e9fc2bfba93664ad8945732ab1c97791f9123e703de8669b65670d1e01906d80436bf4932d7ee6fa6174ed4545e8abb059206c88f4bd71957ca6cf7ba8
  languageName: node
  linkType: hard

"vue-hot-reload-api@npm:^2.3.0":
  version: 2.3.4
  resolution: "vue-hot-reload-api@npm:2.3.4"
  checksum: 9befc0b3d6c1cc69430813fb7cfd2125c6a228730a36fad0653e4ddb60c8d4cf3ddc9649d2c9105c3d6044b42e8c8dce62b3c245bc65a6f187c1e2ca82a79252
  languageName: node
  linkType: hard

"vue-loader@npm:^17.0.0":
  version: 17.2.2
  resolution: "vue-loader@npm:17.2.2"
  dependencies:
    chalk: ^4.1.0
    hash-sum: ^2.0.0
    watchpack: ^2.4.0
  peerDependencies:
    webpack: ^4.1.0 || ^5.0.0-0
  peerDependenciesMeta:
    "@vue/compiler-sfc":
      optional: true
    vue:
      optional: true
  checksum: 54ea380e63bf9bf3e0a6229023f26aa7be934dc3ef424c455906f3047a9fd890c1ac60a61e72a7db9a2274712e6958bdaad123b9235fec003681990e71610db7
  languageName: node
  linkType: hard

"vue-router@npm:next":
  version: 4.0.13
  resolution: "vue-router@npm:4.0.13"
  dependencies:
    "@vue/devtools-api": ^6.0.0
  peerDependencies:
    vue: ^3.2.0
  checksum: c1a4345e05af7e45d4808b89a92c6083891383759bba5f27a0e63840b19dbb4743fdaada48191445c652d6194792563dc76edf85557ae612a4908befa9148a57
  languageName: node
  linkType: hard

"vue-style-loader@npm:^4.1.0, vue-style-loader@npm:^4.1.3":
  version: 4.1.3
  resolution: "vue-style-loader@npm:4.1.3"
  dependencies:
    hash-sum: ^1.0.2
    loader-utils: ^1.0.2
  checksum: ef79d0c6329303d69c87f128f67e486bd37e9a8d416aa662edafae62fab727117b7452f50be8b11fe0c4cb43992344d5ef6a46b206f375fca4d37ae5a5b99185
  languageName: node
  linkType: hard

"vue-template-es2015-compiler@npm:^1.9.0":
  version: 1.9.1
  resolution: "vue-template-es2015-compiler@npm:1.9.1"
  checksum: ad1e85662783be3ee262c323b05d12e6a5036fca24f16dc0f7ab92736b675919cb4fa4b79b28753eac73119b709d1b36789bf60e8ae423f50c4db35de9370e8b
  languageName: node
  linkType: hard

"vue@npm:^3.2.13":
  version: 3.3.4
  resolution: "vue@npm:3.3.4"
  dependencies:
    "@vue/compiler-dom": 3.3.4
    "@vue/compiler-sfc": 3.3.4
    "@vue/runtime-dom": 3.3.4
    "@vue/server-renderer": 3.3.4
    "@vue/shared": 3.3.4
  checksum: 58b6c62a66a375ce5df460fcb7ba41b37c8637c635faf06ef472ae4197f412cf9ad83586cd8e3f66c486404fbe8550e694f90ff724a571d1ba78830791099c59
  languageName: node
  linkType: hard

"vueuc@npm:^0.4.51":
  version: 0.4.51
  resolution: "vueuc@npm:0.4.51"
  dependencies:
    "@css-render/vue3-ssr": ^0.15.10
    "@juggle/resize-observer": ^3.3.1
    css-render: ^0.15.10
    evtd: ^0.2.4
    seemly: ^0.3.6
    vdirs: ^0.1.4
    vooks: ^0.2.4
  peerDependencies:
    vue: ^3.0.11
  checksum: 7969659facd8a1f7b7676bf5629d00a4830a648c279f3632c711ceedf8f7d7f6c8f61546a2dec74e2f2ff91e9185dcd76136bef7ae6eeac38bb1c6129840cfac
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.0":
  version: 2.4.0
  resolution: "watchpack@npm:2.4.0"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 23d4bc58634dbe13b86093e01c6a68d8096028b664ab7139d58f0c37d962d549a940e98f2f201cecdabd6f9c340338dc73ef8bf094a2249ef582f35183d1a131
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: ^1.0.0
  checksum: 2abc306c96930b757972a1c4650eb6b25b5d99f24088714957f88629e137db569368c5de0e57986c89ea70db2f1df9bba11a87cb6d0c8694b6f53a0159fab3bf
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^4.4.0":
  version: 4.9.1
  resolution: "webpack-bundle-analyzer@npm:4.9.1"
  dependencies:
    "@discoveryjs/json-ext": 0.5.7
    acorn: ^8.0.4
    acorn-walk: ^8.0.0
    commander: ^7.2.0
    escape-string-regexp: ^4.0.0
    gzip-size: ^6.0.0
    is-plain-object: ^5.0.0
    lodash.debounce: ^4.0.8
    lodash.escape: ^4.0.1
    lodash.flatten: ^4.4.0
    lodash.invokemap: ^4.6.0
    lodash.pullall: ^4.2.0
    lodash.uniqby: ^4.7.0
    opener: ^1.5.2
    picocolors: ^1.0.0
    sirv: ^2.0.3
    ws: ^7.3.1
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: 7e891c28d5a903242893e55ecc714fa01d7ad6bedade143235c07091b235915349812fa048968462781d59187507962f38b6c61ed7d25fb836ba0ac0ee919a39
  languageName: node
  linkType: hard

"webpack-chain@npm:^6.5.1":
  version: 6.5.1
  resolution: "webpack-chain@npm:6.5.1"
  dependencies:
    deepmerge: ^1.5.2
    javascript-stringify: ^2.0.1
  checksum: 51ea287b13cd29fa61ef3942539e6f179a6e677b51bca42ecc9d5eba7ab318166fbb859be5701b0ac4e907d1db29a0b4d2b53b60eddac6f6c33783392c742e5f
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^5.3.1":
  version: 5.3.3
  resolution: "webpack-dev-middleware@npm:5.3.3"
  dependencies:
    colorette: ^2.0.10
    memfs: ^3.4.3
    mime-types: ^2.1.31
    range-parser: ^1.2.1
    schema-utils: ^4.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: dd332cc6da61222c43d25e5a2155e23147b777ff32fdf1f1a0a8777020c072fbcef7756360ce2a13939c3f534c06b4992a4d659318c4a7fe2c0530b52a8a6621
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^4.7.3":
  version: 4.15.1
  resolution: "webpack-dev-server@npm:4.15.1"
  dependencies:
    "@types/bonjour": ^3.5.9
    "@types/connect-history-api-fallback": ^1.3.5
    "@types/express": ^4.17.13
    "@types/serve-index": ^1.9.1
    "@types/serve-static": ^1.13.10
    "@types/sockjs": ^0.3.33
    "@types/ws": ^8.5.5
    ansi-html-community: ^0.0.8
    bonjour-service: ^1.0.11
    chokidar: ^3.5.3
    colorette: ^2.0.10
    compression: ^1.7.4
    connect-history-api-fallback: ^2.0.0
    default-gateway: ^6.0.3
    express: ^4.17.3
    graceful-fs: ^4.2.6
    html-entities: ^2.3.2
    http-proxy-middleware: ^2.0.3
    ipaddr.js: ^2.0.1
    launch-editor: ^2.6.0
    open: ^8.0.9
    p-retry: ^4.5.0
    rimraf: ^3.0.2
    schema-utils: ^4.0.0
    selfsigned: ^2.1.1
    serve-index: ^1.9.1
    sockjs: ^0.3.24
    spdy: ^4.0.2
    webpack-dev-middleware: ^5.3.1
    ws: ^8.13.0
  peerDependencies:
    webpack: ^4.37.0 || ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: cd0063b068d2b938fd76c412d555374186ac2fa84bbae098265212ed50a5c15d6f03aa12a5a310c544a242943eb58c0bfde4c296d5c36765c182f53799e1bc71
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3":
  version: 5.9.0
  resolution: "webpack-merge@npm:5.9.0"
  dependencies:
    clone-deep: ^4.0.1
    wildcard: ^2.0.0
  checksum: 64fe2c23aacc5f19684452a0e84ec02c46b990423aee6fcc5c18d7d471155bd14e9a6adb02bd3656eb3e0ac2532c8e97d69412ad14c97eeafe32fa6d10050872
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.4.2":
  version: 0.4.6
  resolution: "webpack-virtual-modules@npm:0.4.6"
  checksum: cb056ba8c50b35436ae43149554b051b80065b0cf79f2d528ca692ddf344a422ac71c415adb9da83dc3acc6e7e58f518388cc1cd11cb4fa29dc04f2c4494afe3
  languageName: node
  linkType: hard

"webpack@npm:^5.54.0":
  version: 5.88.2
  resolution: "webpack@npm:5.88.2"
  dependencies:
    "@types/eslint-scope": ^3.7.3
    "@types/estree": ^1.0.0
    "@webassemblyjs/ast": ^1.11.5
    "@webassemblyjs/wasm-edit": ^1.11.5
    "@webassemblyjs/wasm-parser": ^1.11.5
    acorn: ^8.7.1
    acorn-import-assertions: ^1.9.0
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.15.0
    es-module-lexer: ^1.2.1
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.2.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.3.7
    watchpack: ^2.4.0
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 79476a782da31a21f6dd38fbbd06b68da93baf6a62f0d08ca99222367f3b8668f5a1f2086b7bb78e23172e31fa6df6fa7ab09b25e827866c4fc4dc2b30443ce2
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: ">=0.5.1"
    safe-buffer: ">=5.1.0"
    websocket-extensions: ">=0.1.1"
  checksum: fffe5a33fe8eceafd21d2a065661d09e38b93877eae1de6ab5d7d2734c6ed243973beae10ae48c6613cfd675f200e5a058d1e3531bc9e6c5d4f1396ff1f0bfb9
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 5976835e68a86afcd64c7a9762ed85f2f27d48c488c707e67ba85e717b90fa066b98ab33c744d64255c9622d349eedecf728e65a5f921da71b58d0e9591b9038
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.6.2":
  version: 3.6.19
  resolution: "whatwg-fetch@npm:3.6.19"
  checksum: 2896bc9ca867ea514392c73e2a272f65d5c4916248fe0837a9df5b1b92f247047bc76cf7c29c28a01ac6c5fb4314021d2718958c8a08292a96d56f72b2f56806
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.1
  resolution: "wildcard@npm:2.0.1"
  checksum: e0c60a12a219e4b12065d1199802d81c27b841ed6ad6d9d28240980c73ceec6f856771d575af367cbec2982d9ae7838759168b551776577f155044f5a5ba843c
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^3.0.1":
  version: 3.0.1
  resolution: "wrap-ansi@npm:3.0.1"
  dependencies:
    string-width: ^2.1.1
    strip-ansi: ^4.0.0
  checksum: 1ceed09986d58cf6e0b88ea29084e70ef3463b3b891a04a8dbf245abb1fb678358986bdc43e12bcc92a696ced17327d079bc796f4d709d15aad7b8c1a7e7c83a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^7.3.1":
  version: 7.5.9
  resolution: "ws@npm:7.5.9"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: c3c100a181b731f40b7f2fddf004aa023f79d64f489706a28bc23ff88e87f6a64b3c6651fbec3a84a53960b75159574d7a7385709847a62ddb7ad6af76f49138
  languageName: node
  linkType: hard

"ws@npm:^8.13.0":
  version: 8.14.1
  resolution: "ws@npm:8.14.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 9e310be2b0ff69e1f87d8c6d093ecd17a1ed4c37f281d17c35e8c30e2bd116401775b3d503249651374e6e0e1e9905db62fff096b694371c77561aee06bc3466
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 9ba99409209f485b6fcb970330908a6d41fa1c933f75e08250316cce19383179a6b70a7e0721b89672ebb6199cc377bf3e432f55100da6a7d6e11902b0a642cb
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0, yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs@npm:^16.0.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b14afbb51e3251a204d81937c86a7e9d4bdbf9a2bcee38226c900d00f522969ab675703bee2a6f99f8e20103f608382936034e64d921b74df82b63c07c5e8f59
  languageName: node
  linkType: hard

"yorkie@npm:^2.0.0":
  version: 2.0.0
  resolution: "yorkie@npm:2.0.0"
  dependencies:
    execa: ^0.8.0
    is-ci: ^1.0.10
    normalize-path: ^1.0.0
    strip-indent: ^2.0.0
  checksum: 6810adaf6be7b6cc115ed9454e5a4e8e0949deecb107d65dc0c196051e862f04d6394ad12fbef0d51e6c730329992ae98625ba82cba5e34775023ddb6dc921b8
  languageName: node
  linkType: hard
