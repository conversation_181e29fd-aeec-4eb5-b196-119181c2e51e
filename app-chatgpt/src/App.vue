<template>
    <Layout />
    <Toast position="top-center"/>
    <ConfirmPopup />
    <Message
        class="p-message-noLoginRequired"
        v-if="noLoginRequired"
        severity="warn"
        >注意 : 当前是免登环境~</Message
    >
    <Message
        class="p-message-noLoginRequired"
        v-if="env === 'test'"
        severity="error"
        >当前为测试环境, 将在2024年1月1号前关闭, 请尽快切换到线上环境
        "https://aidev.corpautohome.com/"</Message
    >
    <Teleport to="body">
        <div class="browsers-note-hidden">
            暂不支持当前浏览器，为了获得更好的使用体验，建议使用Chrome浏览器使用AIDEV系统
            <div class="browsers-note__btn" @click="toLoad">
                点击下载Chrome浏览器
            </div>
            <div class="browsers-note__close" @click="hideNote">关闭提示</div>
        </div>
    </Teleport>
</template>

<script>
import { mapState, mapActions } from 'pinia';
import useAppStore from '@/app/stores/app';
import Layout from '@/pages/layout/Index.vue';
import _result from '@/app/utils/supportedBrower.js';

export default {
    name: 'App',
    components: {
        Layout,
    },
    computed: {
        ...mapState(useAppStore, ['noLoginRequired', 'env']),
    },
    methods: {
        ...mapActions(useAppStore, ['initApp']),

        toLoad() {
            window.open('https://www.google.cn/intl/zh-CN/chrome/', '_blank');
        },

        hideNote() {
            document
                .querySelector('.browsers-note-hidden')
                .classList.remove('browsers-note-display');
        },
    },
    async created() {
        await this.initApp();

        if (_result) {
            const _dom = document.querySelector('.browsers-note-hidden');
            if (_dom) {
                _dom.classList.add('browsers-note-display');
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.p-message-noLoginRequired {
    position: fixed;
    left: 0;
    top: 0px;
    width: 100%;
    margin: 0;
    z-index: 999;

    ::v-deep {
        .p-message-text {
            font-size: 1.4rem;
        }
    }
}

.browsers-note-hidden {
    display: none;
    width: 100%;
    min-height: 60px;
    height: auto;
    line-height: 60px;
    text-align: center;
    color: #333333;
    font-size: 14px;
    background-color: #ffdd00;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
}

.browsers-note-display {
    display: block;
}

.browsers-note__btn {
    display: inline-block;
    background: #fff;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
    line-height: 38px;
    height: 38px;
    margin-left: 8px;
    line-height: 38px;
    border-radius: 19px;
    color: rgb(0, 153, 255);
    cursor: pointer;
}

.browsers-note__close {
    color: red;
    position: absolute;
    right: 15px;
    top: 0;
    font-weight: 600;
    cursor: pointer;
}
</style>
