import { ChatPromptTemplate } from '@langchain/core/prompts';
import { BaseLangChainService } from './BaseLangChainService.js';

/**
 * 翻译服务类
 * 基于BaseLangChainService，提供专门的翻译功能
 */
export class TranslatorService extends BaseLangChainService {
    constructor() {
        super();
        this.chain = null;
    }

    /**
     * 初始化翻译服务
     * @param {Object} config - 配置参数
     * @param {string} config.model - 模型名称
     * @param {string} config.apiKey - API密钥
     * @param {string} config.baseURL - API基础URL
     */
    async initialize(config) {
        try {
            // 初始化模型
            this.initializeModel(config);

            // 创建翻译提示模板
            const promptTemplate = this.createTranslationPromptTemplate();

            // 创建翻译链
            this.chain = this.createChain(promptTemplate);

            return true;
        } catch (error) {
            throw this.handleError(error);
        }
    }

    /**
     * 创建翻译提示模板
     * @returns {ChatPromptTemplate} 提示模板
     */
    createTranslationPromptTemplate() {
        return ChatPromptTemplate.fromMessages([
            [
                'system',
                "You are a professional translator. Translate the following text from {sourceLanguage} to {targetLanguage}. If the source language is 'auto', automatically detect the language. Use a {style} style and {tone} tone. Only return the translated text without any explanation.",
            ],
            ['user', '{text}'],
        ]);
    }

    /**
     * 执行翻译
     * @param {Object} params - 翻译参数
     * @param {string} params.text - 要翻译的文本
     * @param {string} params.sourceLanguage - 源语言
     * @param {string} params.targetLanguage - 目标语言
     * @param {string} params.style - 翻译风格
     * @param {string} params.tone - 翻译语调
     * @param {Function} onProgress - 进度回调函数
     * @param {Function} shouldCancel - 取消检查函数
     * @returns {Promise<string>} 翻译结果
     */
    async translate(params, onProgress, shouldCancel) {
        if (!this.chain) {
            throw new Error('翻译服务未初始化');
        }

        const { text, sourceLanguage, targetLanguage, style, tone } = params;

        if (!text?.trim()) {
            throw new Error('请输入要翻译的文本');
        }

        // 准备翻译参数
        const translationInput = {
            sourceLanguage: this.getLanguageName(sourceLanguage),
            targetLanguage: this.getLanguageName(targetLanguage),
            style: style || 'formal',
            tone: tone || 'neutral',
            text: text.trim(),
        };

        // 执行流式翻译
        return await this.executeStream(this.chain, translationInput, onProgress, shouldCancel);
    }

    /**
     * 将语言代码转换为语言名称
     * @param {string} code - 语言代码
     * @returns {string} 语言名称
     */
    getLanguageName(code) {
        const languageMap = {
            zh: 'Chinese',
            en: 'English',
            ja: 'Japanese',
            ko: 'Korean',
            fr: 'French',
            de: 'German',
            es: 'Spanish',
            ru: 'Russian',
            auto: 'auto-detect',
        };

        return languageMap[code] || code;
    }

    /**
     * 检查翻译服务是否就绪
     * @returns {boolean}
     */
    isReady() {
        return this.isModelReady() && this.chain !== null;
    }

    /**
     * 重置翻译服务
     */
    reset() {
        super.reset();
        this.chain = null;
    }

    /**
     * 验证翻译参数
     * @param {Object} params - 翻译参数
     * @returns {Object} 验证结果
     */
    validateTranslationParams(params) {
        const errors = [];

        if (!params.text?.trim()) {
            errors.push('文本内容不能为空');
        }

        if (!params.sourceLanguage) {
            errors.push('请选择源语言');
        }

        if (!params.targetLanguage) {
            errors.push('请选择目标语言');
        }

        if (params.sourceLanguage === params.targetLanguage && params.sourceLanguage !== 'auto') {
            errors.push('源语言和目标语言不能相同');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }
}
