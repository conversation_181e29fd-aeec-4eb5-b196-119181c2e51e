# LangChain 服务层

本目录包含了对 LangChain 功能的抽象和封装，将技术实现细节与业务逻辑分离。

## 架构设计

### 分层结构

```
src/services/langchain/
├── BaseLangChainService.js   # 通用 LangChain 基础服务
├── TranslatorService.js      # 翻译专用服务
├── index.js                  # 服务入口文件
└── README.md                 # 本文档
```

### 设计原则

1. **关注点分离**: 技术实现细节与业务逻辑分离
2. **可复用性**: 基础服务可以被多个业务服务继承和复用
3. **可扩展性**: 便于添加新的AI功能服务
4. **易测试性**: 每个服务都有明确的职责和接口

## 服务说明

### BaseLangChainService

**职责**: 提供 LangChain 的通用功能抽象

**主要功能**:

-   模型初始化和管理
-   流式处理通用逻辑
-   错误处理和统一异常管理
-   链创建和执行

**使用场景**: 作为其他AI服务的基类

### TranslatorService

**职责**: 提供翻译相关的业务功能

**主要功能**:

-   翻译参数验证
-   翻译提示词模板管理
-   语言代码转换
-   翻译业务逻辑封装

**使用场景**: 在Vue组件中直接使用进行翻译

## 使用示例

### 基本使用

```javascript
import { TranslatorService } from '@/services/langchain/index.js';

// 创建翻译服务实例
const translatorService = new TranslatorService();

// 初始化服务
await translatorService.initialize({
    model: 'deepseek-v3',
    apiKey: 'your-api-key',
    baseURL: 'https://api.siliconflow.cn/v1',
    temperature: 0.1,
});

// 执行翻译
const result = await translatorService.translate(
    {
        text: 'Hello, world!',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        style: 'formal',
        tone: 'neutral',
    },
    // 流式进度回调
    (chunk) => {
        console.log('收到新的翻译片段:', chunk);
    },
    // 取消检查函数
    () => false // 不取消
);

console.log('翻译结果:', result);
```

### 参数验证

```javascript
const validation = translatorService.validateTranslationParams({
    text: 'Hello',
    sourceLanguage: 'en',
    targetLanguage: 'zh',
    style: 'formal',
    tone: 'neutral',
});

if (!validation.isValid) {
    console.error('参数验证失败:', validation.errors);
}
```

## 扩展指南

### 添加新的AI服务

1. 继承 `BaseLangChainService`
2. 实现特定的业务逻辑
3. 定义特定的提示词模板
4. 添加业务相关的验证逻辑

示例:

```javascript
import { BaseLangChainService } from './BaseLangChainService.js';
import { ChatPromptTemplate } from '@langchain/core/prompts';

export class SummaryService extends BaseLangChainService {
    constructor() {
        super();
        this.chain = null;
    }

    async initialize(config) {
        this.initializeModel(config);

        const promptTemplate = ChatPromptTemplate.fromMessages([
            ['system', 'You are a professional summarizer...'],
            ['user', '{text}'],
        ]);

        this.chain = this.createChain(promptTemplate);
    }

    async summarize(text, onProgress, shouldCancel) {
        return await this.executeStream(this.chain, { text }, onProgress, shouldCancel);
    }
}
```

## 优势

### 重构前的问题

-   LangChain 技术细节与业务逻辑耦合
-   代码难以复用和扩展
-   测试困难
-   维护成本高

### 重构后的优势

-   **清晰的分层**: 技术层和业务层分离
-   **高复用性**: 基础服务可被多个业务复用
-   **易扩展**: 添加新AI功能只需实现对应的服务类
-   **易测试**: 每个服务职责单一，便于单元测试
-   **易维护**: 技术升级只需修改基础服务层

## 最佳实践

1. **服务初始化**: 确保在使用前调用 `initialize()` 方法
2. **错误处理**: 使用 try-catch 包裹服务调用
3. **参数验证**: 调用业务方法前进行参数验证
4. **资源管理**: 必要时调用 `reset()` 方法清理资源
5. **流式处理**: 提供适当的进度回调和取消机制
