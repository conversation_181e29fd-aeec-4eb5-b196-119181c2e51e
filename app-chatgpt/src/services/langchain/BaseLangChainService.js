import { ChatOpenAI } from '@langchain/openai';
import { StringOutputParser } from '@langchain/core/output_parsers';

/**
 * LangChain 基础服务类
 * 提供通用的模型管理、流式处理等功能
 */
export class BaseLangChainService {
    constructor() {
        this.model = null;
        this.parser = new StringOutputParser();
    }

    /**
     * 初始化模型
     * @param {Object} config - 模型配置
     * @param {string} config.model - 模型名称
     * @param {string} config.apiKey - API密钥
     * @param {string} config.baseURL - API基础URL
     * @param {number} config.temperature - 温度参数
     * @param {boolean} config.streaming - 是否启用流式返回
     */
    initializeModel(config) {
        if (!config.apiKey) {
            throw new Error('API密钥不能为空');
        }

        try {
            this.model = new ChatOpenAI({
                model: config.model,
                temperature: config.temperature || 0.1,
                openAIApiKey: config.apiKey,
                configuration: {
                    baseURL: config.baseURL,
                },
                streaming: config.streaming !== false,
            });
        } catch (error) {
            throw new Error(`模型初始化失败: ${error.message}`);
        }
    }

    /**
     * 创建处理链
     * @param {Object} promptTemplate - 提示模板
     * @returns {Object} 处理链
     */
    createChain(promptTemplate) {
        if (!this.model) {
            throw new Error('模型未初始化');
        }

        return promptTemplate.pipe(this.model).pipe(this.parser);
    }

    /**
     * 执行流式处理
     * @param {Object} chain - 处理链
     * @param {Object} input - 输入参数
     * @param {Function} onChunk - 流式回调函数
     * @param {Function} onCancel - 取消检查函数
     * @returns {Promise<string>} 完整结果
     */
    async executeStream(chain, input, onChunk, onCancel) {
        let result = '';

        try {
            const stream = await chain.stream(input);

            for await (const chunk of stream) {
                // 检查是否需要取消
                if (onCancel && onCancel()) {
                    break;
                }

                result += chunk;

                // 调用回调函数处理每个块
                if (onChunk) {
                    onChunk(chunk);
                }

                // 让出执行权，避免阻塞UI
                await new Promise((resolve) => setTimeout(resolve, 10));
            }
        } catch (error) {
            throw this.handleError(error);
        }

        return result;
    }

    /**
     * 错误处理
     * @param {Error} error - 原始错误
     * @returns {Error} 处理后的错误
     */
    handleError(error) {
        let message = error.message || '未知错误';

        // 特殊错误类型处理
        if (message.includes('401') || message.includes('API key')) {
            message += '，请检查API密钥配置';
        } else if (message.includes('400')) {
            message = '请求参数错误，请检查输入内容';
        } else if (message.includes('429')) {
            message = 'API调用频率超限，请稍后再试';
        } else if (message.includes('500')) {
            message = '服务器内部错误，请稍后再试';
        }

        return new Error(message);
    }

    /**
     * 检查模型是否已初始化
     * @returns {boolean}
     */
    isModelReady() {
        return this.model !== null;
    }

    /**
     * 重置服务状态
     */
    reset() {
        this.model = null;
    }
}
