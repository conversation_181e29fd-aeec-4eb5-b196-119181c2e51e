import { ChatPromptTemplate } from '@langchain/core/prompts';
import { BaseLangChainService } from './BaseLangChainService.js';

/**
 * Mermaid 流程图生成服务类
 * 基于BaseLangChainService，提供专门的流程图生成功能
 */
export class MermaidService extends BaseLangChainService {
    constructor() {
        super();
        this.chain = null;
    }

    /**
     * 初始化流程图生成服务
     * @param {Object} config - 配置参数
     * @param {string} config.model - 模型名称
     * @param {string} config.apiKey - API密钥
     * @param {string} config.baseURL - API基础URL
     */
    async initialize(config) {
        try {
            // 初始化模型
            this.initializeModel(config);

            // 创建流程图生成提示模板
            const promptTemplate = this.createMermaidPromptTemplate();

            // 创建处理链
            this.chain = this.createChain(promptTemplate);

            return true;
        } catch (error) {
            throw this.handleError(error);
        }
    }

    /**
     * 创建Mermaid流程图生成提示模板
     * @returns {ChatPromptTemplate} 提示模板
     */
    createMermaidPromptTemplate() {
        return ChatPromptTemplate.fromMessages([
            [
                'system',
                `You are a professional Mermaid diagram generator. Generate a valid Mermaid diagram based on the user's description.

Diagram types available:
- flowchart: Process flow diagrams  
- sequenceDiagram: Sequence diagrams for interactions
- gantt: Project timelines and schedules
- erDiagram: Entity relationship diagrams
- gitgraph: Git branch visualization
- journey: User journey maps
- pie: Pie charts for data visualization
- quadrantChart: 2x2 quadrant analysis
- sankey: Flow diagrams showing quantities
- timeline: Timeline visualizations

Instructions:
1. Use diagram type: {diagramType}
2. Consider the complexity level: {complexity} (simple/medium/complex)
3. Generate clean, valid Mermaid syntax
4. Include proper labels and connections
5. Use appropriate styling when beneficial
6. Ensure the diagram is readable and well-structured

IMPORTANT: 
- Only return the Mermaid code, no explanation
- Start with the diagram type declaration
- Use proper Mermaid syntax
- For Chinese text, ensure proper encoding

Examples:
Flowchart: Start with "flowchart TD" or "flowchart LR"
Sequence: Start with "sequenceDiagram"
Gantt: Start with "gantt"`,
            ],
            ['user', 'Description: {description}'],
        ]);
    }

    /**
     * 生成Mermaid流程图
     * @param {Object} params - 生成参数
     * @param {string} params.description - 流程描述
     * @param {string} params.diagramType - 图表类型
     * @param {string} params.complexity - 复杂度级别
     * @param {Function} onProgress - 进度回调函数
     * @param {Function} shouldCancel - 取消检查函数
     * @returns {Promise<string>} 生成的Mermaid代码
     */
    async generateMermaid(params, onProgress, shouldCancel) {
        if (!this.chain) {
            throw new Error('Mermaid服务未初始化');
        }

        const { description, diagramType, complexity } = params;

        if (!description?.trim()) {
            throw new Error('请输入流程描述');
        }

        // 准备生成参数
        const generateInput = {
            description: description.trim(),
            diagramType: diagramType || 'flowchart',
            complexity: complexity || 'medium',
        };

        // 执行流式生成
        return await this.executeStream(this.chain, generateInput, onProgress, shouldCancel);
    }

    /**
     * 获取支持的图表类型
     * @returns {Array} 图表类型列表
     */
    getSupportedDiagramTypes() {
        return [
            { value: 'flowchart', label: '流程图', description: '展示过程和决策流程' },
            { value: 'sequenceDiagram', label: '时序图', description: '展示对象间的交互序列' },
            { value: 'gantt', label: '甘特图', description: '展示项目时间线和任务' },
            { value: 'erDiagram', label: 'ER图', description: '展示实体关系模型' },
            { value: 'gitgraph', label: 'Git分支图', description: '展示Git分支和合并' },
            { value: 'journey', label: '用户旅程图', description: '展示用户体验流程' },
            { value: 'pie', label: '饼图', description: '展示数据比例分布' },
            { value: 'quadrantChart', label: '象限图', description: '展示2x2分析矩阵' },
            { value: 'sankey', label: '桑基图', description: '展示流量和转换' },
            { value: 'timeline', label: '时间线', description: '展示时间序列事件' },
        ];
    }

    /**
     * 获取复杂度选项
     * @returns {Array} 复杂度选项列表
     */
    getComplexityOptions() {
        return [
            { value: 'simple', label: '简单', description: '3-5个节点，适合简单流程' },
            { value: 'medium', label: '中等', description: '6-15个节点，适合一般业务流程' },
            { value: 'complex', label: '复杂', description: '15+个节点，适合复杂系统流程' },
        ];
    }

    /**
     * 验证流程图生成参数
     * @param {Object} params - 生成参数
     * @returns {Object} 验证结果
     */
    validateMermaidParams(params) {
        const errors = [];

        if (!params.description?.trim()) {
            errors.push('流程描述不能为空');
        }

        if (params.description && params.description.length > 2000) {
            errors.push('流程描述不能超过2000字符');
        }

        const supportedTypes = this.getSupportedDiagramTypes().map((t) => t.value);
        if (params.diagramType && !supportedTypes.includes(params.diagramType)) {
            errors.push('不支持的图表类型');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    /**
     * 检查Mermaid服务是否就绪
     * @returns {boolean}
     */
    isReady() {
        return this.isModelReady() && this.chain !== null;
    }

    /**
     * 重置Mermaid服务
     */
    reset() {
        super.reset();
        this.chain = null;
    }

    /**
     * 优化Mermaid代码
     * @param {string} mermaidCode - 原始Mermaid代码
     * @param {Object} optimizeParams - 优化参数
     * @param {Function} onProgress - 进度回调函数
     * @param {Function} shouldCancel - 取消检查函数
     * @returns {Promise<string>} 优化后的Mermaid代码
     */
    async optimizeMermaid(mermaidCode, optimizeParams, onProgress, shouldCancel) {
        if (!this.chain) {
            throw new Error('Mermaid服务未初始化');
        }

        // 创建优化提示模板
        const optimizeTemplate = ChatPromptTemplate.fromMessages([
            [
                'system',
                `You are a Mermaid diagram optimizer. Optimize the following Mermaid code based on the requirements:

Optimization goals:
1. Improve readability and layout
2. Add proper styling if requested
3. Optimize node names and labels
4. Improve visual hierarchy
5. Ensure valid Mermaid syntax

Requirements: {requirements}

IMPORTANT: 
- Only return the optimized Mermaid code
- Maintain the original diagram structure and meaning
- Use valid Mermaid syntax
- Ensure better visual presentation`,
            ],
            ['user', 'Original Mermaid code:\n{mermaidCode}'],
        ]);

        const optimizeChain = this.createChain(optimizeTemplate);

        const optimizeInput = {
            mermaidCode: mermaidCode,
            requirements: optimizeParams.requirements || '提高可读性和美观度',
        };

        return await this.executeStream(optimizeChain, optimizeInput, onProgress, shouldCancel);
    }
}
