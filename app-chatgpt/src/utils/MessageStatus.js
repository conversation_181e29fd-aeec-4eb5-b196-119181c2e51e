export class MessageStatus {
    static STATUS = {
        PENDING: 1, // 未提交模型回答
        SUBMITTED: 2, // 已提交模型
        FAILED: 3, // 请求模型接口失败
        COMPLETED: 5, // 模型回答完成
    };

    static SUPER_INFO = [
        {
            name: '范宝胤',
            dingTalkUrl: 'dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=fanbaoyin',
        },
        {
            name: '张俊',
            dingTalkUrl: 'dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=qi4jug4',
        },
        {
            name: '翟晓伟',
            dingTalkUrl: 'dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=9jc_k5eapbcmu',
        },
    ];

    static ADMIN_INFO = [
        {
            name: '张俊',
            dingTalkUrl: 'dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=qi4jug4',
        },
        {
            name: '翟晓伟',
            dingTalkUrl: 'dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=9jc_k5eapbcmu',
        },
    ];

    static getStatusInfo(status, answerContent = '') {
        const statusMap = {
            [this.STATUS.PENDING]: {
                message: '模型尚未完成回答生成，这可能需要一些时间。请耐心等待或刷新页面重试。',
                class: 'status-pending',
                showRefresh: true,
                showRetry: false,
                showResetContext: false,
                showAdminInfo: true,
                adminInfo: this.ADMIN_INFO,
                icon: 'pi pi-spin pi-spinner',
                title: '等待模型响应',
                subTitle: '模型正在处理您的请求',
            },
            [this.STATUS.SUBMITTED]: {
                message: '请求已提交，正在处理中，请耐心等待',
                class: 'status-submitted',
                showResetContext: false,
                showAdminInfo: false,
            },
            [this.STATUS.FAILED]: {
                ...this._getFailedMessage(answerContent),
                class: 'status-failed',
                showRefresh: true,
                showRetry: true,
                showAdminInfo: true,
                adminInfo: this.SUPER_INFO,
                icon: 'pi pi-exclamation-circle',
                title: '模型响应错误',
                subTitle: '很抱歉，模型响应出现了问题',
            },
            [this.STATUS.COMPLETED]: {
                message: '',
                class: 'status-completed',
                showResetContext: false,
                showAdminInfo: false,
            },
        };

        return statusMap[status] || statusMap[this.STATUS.FAILED];
    }

    static _getFailedMessage(answerContent = '') {
        // 检查是否包含上下文长度超限的关键词
        if (answerContent && (answerContent.includes('maximum context length') || answerContent.includes('tokens'))) {
            return {
                message:
                    '当前对话上下文长度已超出模型处理限制。建议您：1. 清除部分上下文后重试；2. 精简问题描述后重试；3. 开启新的会话。',
                showResetContext: true,
            };
        }

        // 默认错误信息
        return {
            message:
                '错误信息：模型在处理您的请求时遇到了问题，这可能是由于模型负载过高或网络连接不稳定导致的。建议您稍后重试。',
            showResetContext: false,
        };
    }

    static isCompleted(status) {
        return status === this.STATUS.COMPLETED;
    }
}
