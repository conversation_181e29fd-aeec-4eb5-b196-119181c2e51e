<template>
  <div class="share-detail-wrap">
    <ChatHistory :messages="message.messages" />
  </div>
</template>

<script>
import { mapActions, mapWritableState } from "pinia";
import useShareStore from "@/app/stores/share";
import ChatHistory from '@/pages/components/ChatHistory/index.vue'

export default {
  components: {
    ChatHistory
  },
  data() {
    return {}
  },
  computed: {
    ...mapWritableState(useShareStore, ['message'])
  },
  async mounted() {
    const { id } = this.$route.params;
    const params = {
      shareId: id
    }
    try {
      await this.getShareMessage(params);
    } catch (err) {
      this.$toast.add({ severity: 'error', summary: '错误', detail: err.message, life: 3000 });
    }
  },
  unmounted() {

  },
  methods: {
    ...mapActions(useShareStore, ['getShareMessage']),
  }
}
</script>

<style lang="scss" scoped>
.share-detail-wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%; 
  overflow-y: scroll;
}
</style>