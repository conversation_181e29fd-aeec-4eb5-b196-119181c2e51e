<template>
  <div class="home-wrap">
    <section class="intro">
      <h1>Vue3-Demo</h1>
      <p>当前应用为vue应用, 下面示例是路由, naive ui 组件, chat接口示例</p>
    </section>
    <n-card title="naive ui 组件">
      <a
        href="https://www.naiveui.com/zh-CN/os-theme/components/card"
        target="_blank"
        >naive-ui 官网</a
      >
    </n-card>

    <n-card title="http请求示例">
      <p>{{ beck }}</p>
      <p><button @click.prevent="fetchData">发送请求</button></p>
    </n-card>
  </div>
</template>

<script>
export default {
  name: "PageDocs",
  components: {},
  data() {
    return {
      beck: {},
    };
  },
  methods: {
    async fetchData() {
      try {
        const response = await fetch("/api/chat/chatSessions?account=biankai");

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const jsonData = await response.json();
        console.log(jsonData);
        this.beck = jsonData;
      } catch (err) {
        console.log(err.message);
      } finally {
        console.log("");
      }
    },
  },
};
</script>

<style lang="sass" scoped>
.intro
  text-align: center
  h1
    font-size: 20px
    margin-bottom: 20px
  nav
    margin: 50px 0 20px
    a
      margin:0 10px
.n-card
  max-width: 300px
  margin-top: 10px
</style>
