<template>
    <div class="codereview-my-wrap">
        <form>
            <div class="col-4">
                <label for="dd-city">应用名称</label>
                <AutoComplete
                    optionLabel="appName"
                    delay="400"
                    v-model="form.apps"
                    :suggestions="apps"
                    @complete="searchApps"
                />
            </div>
            <div class="col-4">
                <label for="dd-city">Git地址</label>
                <AutoComplete
                    delay="400"
                    v-model="form.gitUrls"
                    :suggestions="gitUrls"
                    @complete="searchGitUrls"
                />
            </div>
            <div class="col-4">
                <label for="dd-city">团队</label>
                <Dropdown
                    showClear
                    v-model="form.depts"
                    :options="depts"
                    optionLabel="deptName"
                    optionValue="deptId"
                    placeholder="请选择团队"
                />
            </div>
            <div class="col-4">
                <label for="dd-city">处理状态</label>
                <Dropdown
                    showClear
                    v-model="form.exposedOptions.exposed"
                    :options="form.exposedOptions.options"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择"
                />
            </div>
            <div class="col-4">
                <label for="dd-city">起止时间</label>
                <Calendar
                    showButtonBar
                    v-model="form.dates"
                    selectionMode="range"
                    dateFormat="yy/m/d"
                    @update:modelValue="updateDates"
                />
            </div>
            <div class="col-4">
                <label for="dd-city">改进建议</label>
                <Dropdown
                    showClear
                    v-model="form.suggestionTypes.data"
                    :options="form.suggestionTypes.options"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择"
                />
            </div>
            <div class="col-4">
                <Button
                    class="search"
                    label="查询"
                    @click.prevent="(event) => getCodeCommits('click', event)"
                />
            </div>
        </form>

        <DataTable
            lazy
            paginator
            scrollable
            showGridlines
            rowHover
            ref="dt"
            :value="codeCommitPage.list"
            :rows="10"
            :loading="table.loading"
            :first="table.first"
            :totalRecords="codeCommitPage.rowcount"
            :class="{
                'p-datatable-hide-paginator':
                    codeCommitPage.list && codeCommitPage.list.length == 0,
            }"
            @page="getCodeCommits('paginator', $event)"
        >
            <Column field="createdStime" header="时间" style="min-width: 13rem">
                <template #body="{ data }">
                    {{ formatDate(data.createdStime) }}
                </template>
            </Column>
            <Column
                field="deptName"
                header="团队"
                style="min-width: 9rem"
            ></Column>
            <Column
                field="appNames"
                header="应用"
                style="min-width: 15rem"
            ></Column>
            <Column field="gitUrl" header="Git地址" style="min-width: 3rem">
                <template #body="{ data }">
                    <Button label="Link" link
                        ><a :href="data.gitUrl" target="_blank">{{
                            data.projectName
                        }}</a></Button
                    >
                </template>
            </Column>
            <Column field="commitLink" header="类型" style="min-width: 8rem">
                <template #body="{ data }">
                    <div v-if="data.type == 0">
                        <Button label="Link" link
                            ><a :href="data.commitLink" target="_blank"
                                >普通提交</a
                            ></Button
                        >
                    </div>
                    <div v-else-if="data.type == 1">
                        <Button label="Link" link
                            ><a
                                :href="
                                    data.gitUrl.slice(0, -4) +
                                    '/merge_requests/' +
                                    data.mergeRequest
                                        .gitlabProjectMergeRequestId
                                "
                                target="_blank"
                                >合并请求</a
                            ></Button
                        >
                    </div>
                    <div v-else-if="data.type == 2">
                        <Button label="Link" link
                            ><a
                                :href="
                                    data.gitUrl.slice(0, -4) +
                                    '/compare/' +
                                    data.pushEvent.commitFrom +
                                    '...' +
                                    data.pushEvent.commitTo
                                "
                                target="_blank"
                                >推送事件</a
                            ></Button
                        >
                    </div>
                </template>
            </Column>
            <Column
                field="commitEmployeeName"
                header="提交人"
                style="min-width: 8rem"
            >
                <template #body="{ data }">
                    {{ data.commitEmployeeName
                    }}{{ data.exposed == 0 ? '(未读)' : '' }}
                </template>
            </Column>
            <Column
                field="suggestionCategorys"
                header="问题分类"
                style="min-width: 14rem; max-width: 220px"
            >
                <template #body="{ data }">
                    <div class="suggestion-category-tag">
                        <Chip
                            v-for="(item, index) in data.suggestionCategorys
                                ? data.suggestionCategorys
                                      .split(',')
                                      .slice(
                                          0,
                                          data.show
                                              ? data.suggestionCategorys.length
                                              : 3
                                      )
                                : []"
                            :key="index"
                            :label="item"
                        ></Chip>
                        <Button
                            v-tooltip.bottom="data.show ? '收起' : '展开更多'"
                            v-if="
                                data.suggestionCategorys &&
                                data.suggestionCategorys.split(',').length > 3
                            "
                            :icon="
                                data.show
                                    ? 'pi pi-angle-up'
                                    : 'pi pi-angle-down'
                            "
                            text
                            rounded
                            aria-label="Filter"
                            @click="data.show = data.show != true"
                        />
                    </div>
                </template>
            </Column>
            <Column
                field="evaluations"
                header="提交人反馈"
                style="min-width: 8rem"
            >
                <template #body="{ data }">
                    <div class="evaluation-tag">
                        <Tag
                            v-for="(item, index) in data.evaluations
                                ? data.evaluations.split(',')
                                : []"
                            :severity="evaluationSeveritys[item]"
                            :value="item"
                            :key="index"
                        ></Tag>
                    </div>
                </template>
            </Column>
            <Column alignFrozen="right" header="操作" style="min-width: 3rem">
                <template #body="slotProps">
                    <Button label="Link" link>
                        <router-link
                            @click="
                                reportUserEvent('codereview-list-hrefToDetail')
                            "
                            :to="{
                                name: 'codereview-detail',
                                params: { id: slotProps.data.id },
                            }"
                        >
                            <span>详情</span>
                        </router-link>
                    </Button>
                </template>
            </Column>
            <template #empty>
                <div class="p-table-empty-data">
                    <p>
                        <svg
                            width="64"
                            height="41"
                            viewBox="0 0 64 41"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <g
                                transform="translate(0 1)"
                                fill="none"
                                fill-rule="evenodd"
                            >
                                <ellipse
                                    fill="#f5f5f5"
                                    cx="32"
                                    cy="33"
                                    rx="32"
                                    ry="7"
                                ></ellipse>
                                <g fill-rule="nonzero" stroke="#d9d9d9">
                                    <path
                                        d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                    ></path>
                                    <path
                                        d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                        fill="#fafafa"
                                    ></path>
                                </g>
                            </g>
                        </svg>
                    </p>
                    <p>无数据</p>
                </div>
            </template>
        </DataTable>
    </div>
</template>

<script>
import { mapState, mapWritableState, mapActions } from 'pinia';
import useAppStore from '@/app/stores/app';
import useCodereviewStore from '@/app/stores/codereview';

import dayjs from 'dayjs';

export default {
    name: 'Codereview',
    components: {},
    data() {
        return {
            form: {
                apps: '',
                gitUrls: '',
                depts: null,
                employees: [],
                dates: null,
                suggestionTypes: {
                    options: [
                        {
                            label: '有建议',
                            value: 2,
                        },
                        {
                            label: '无建议',
                            value: 1,
                        },
                        // {
                        //   label: '未识别',
                        //   value: 3
                        // }
                    ],
                    data: 2,
                },
                exposedOptions: {
                    options: [
                        {
                            label: '未读',
                            value: 0,
                        },
                        {
                            label: '已读',
                            value: 1,
                        },
                    ],
                    exposed: null,
                },
            },
            table: {
                loading: false,
                first: 0,
                totalRecords: 0,
            },
        };
    },
    computed: {
        ...mapState(useAppStore, ['username']),
        ...mapWritableState(useCodereviewStore, [
            'apps',
            'gitUrls',
            'depts',
            'employees',
            'codeCommitPage',
            'evaluationSeveritys',
        ]),
    },
    methods: {
        ...mapActions(useAppStore, ['reportUserEvent']),
        ...mapActions(useCodereviewStore, [
            'getApps',
            'getGitUrls',
            'getDeptList',
            'getEmployees',
            'getCodeCommitPage',
        ]),
        async init() {
            await this.getDeptList();
            this.depts.forEach((item) => {
                if (item.selected) {
                    this.form.depts = item.deptId;
                }
            });
            await this.getCodeCommits('click');
        },
        async searchApps(event) {
            const params = {
                words: event.query,
            };
            await this.getApps(params);
        },
        async searchGitUrls(event) {
            const params = {
                words: event.query,
            };
            await this.getGitUrls(params);
        },
        async getCodeCommits(type, event) {
            const {
                apps,
                gitUrls,
                depts,
                dates,
                suggestionTypes,
                exposedOptions,
            } = this.form;
            const body = {
                pageSize: 10,
                pageIndex: 1,
                suggestionTypes: [],
                accountNameList: [this.username],
            };
            this.table.loading = true;

            if (type === 'click') {
                body.pageIndex = 1;
                this.table.first = 0;
            }

            if (type === 'paginator') {
                body.pageIndex = event.page + 1;
            }
            if (gitUrls) {
                body.gitUrl = gitUrls;
            }
            if (typeof apps == 'string' && apps.length > 0) {
                this.$toast.add({
                    severity: 'warn',
                    summary: '注意',
                    detail: '应用名称不合法��请从列表中选择。',
                    life: 3000,
                });
                this.table.loading = false;
                return;
            } else {
                body.appId = apps.appId;
            }
            if (depts) {
                body.deptId = depts;
            }

            if (exposedOptions.exposed == 0 || exposedOptions.exposed == 1) {
                body.exposed = exposedOptions.exposed;
            }

            if (dates) {
                body.startTime = dayjs(dates[0]);
                body.endTime = dayjs(dates[1]);
            }
            if (suggestionTypes.data) {
                body.suggestionTypes[0] = this.form.suggestionTypes.data;
            }
            try {
                await this.getCodeCommitPage(body);
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
            }
            this.table.loading = false;
            await this.reportUserEvent('codereview-list-search');
        },
        formatDate(value) {
            if (value) {
                return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            }
            return '';
        },
        updateDates(value) {
            if (value && value.length === 2 && value[1]) {
                value[1].setHours(23, 59, 59, 999);
            }
        },
    },
    mounted() {
        this.init();
    },
};
</script>

<style scoped lang="sass">
.codereview-my-wrap
  height: 100%
  overflow-y: scroll
  background-color: #f5f7fa
  padding: 20px

  form
    background: #fff
    padding: 24px
    border-radius: 8px
    box-shadow: 0 2px 12px rgba(0,0,0,0.05)
    display: flex
    justify-content: space-between
    align-items: top
    flex-direction: row
    flex-wrap: wrap
    margin-bottom: 24px

    .col-4
      display: flex
      flex: 0
      flex-basis: calc( 33.33% - 2.8rem )
      justify-content: space-between
      align-items: center
      margin: 0 1rem 1rem

      label
        min-width: 5rem
        white-space: nowrap
        margin-right: 1rem
        color: #606266
        font-weight: 500

      .search
        width: 8rem
        height: 2.4rem
        margin-left: 5rem
      .p-dropdown, .p-calendar
        width: 100%
      ::v-deep
        .p-autocomplete, .p-autocomplete-input, .p-autocomplete-multiple-container
          width: 100%
        .p-autocomplete-multiple-container
          padding-top: 0.5rem
          padding-bottom: 0.5rem
          .p-autocomplete-input-token input
            font-size: 1.4rem

  .p-datatable
    margin: 0
    width: 100%
    background: #fff
    border-radius: 8px
    box-shadow: 0 2px 12px rgba(0,0,0,0.05)

    a
      color: #1976D2
      text-decoration: none
      transition: color 0.2s
      &:hover
        color: #1565C0
        text-decoration: underline

    ::v-deep
      .p-paginator
        margin-top: 1rem

      .p-datatable-thead > tr > th
        padding: 16px 12px
        border: 1px solid #dee2e6 !important
        background-color: #e8eaf6 !important
        font-weight: bold
        color: #3f51b5

      .p-datatable-tbody > tr
        &:nth-child(even)
          background: #f8f9fa
        &:hover
          background: #ede7f6
          transition: background-color 0.2s

        > td
          padding: 16px 12px
          border: 1px solid #e0e0e0 !important

    .suggestion-category-tag
      display: flex
      flex-wrap: wrap
      gap:5px
      button
        margin-left: auto
    .evaluation-tag
      display: flex
      flex-wrap: wrap
      gap:5px

      ::v-deep .p-tag
        &[severity="excellent"]
          background-color: #e8f5e9
          color: #2e7d32
        &[severity="reasonable"]
          background-color: #e3f2fd
          color: #1976d2
        &[severity="invalid"]
          background-color: #fff3e0
          color: #e65100
        &[severity="wrong"]
          background-color: #ffebee
          color: #b71c1c

.p-datatable
  .p-table-empty-data
    color: #909399
    text-align: center
    padding: 24px

    p
      margin: 8px 0

    svg
      display: block
      margin: 0 auto
</style>
<style lang="scss">
.codereview-my-wrap {
    .p-button {
        padding: 0;
    }
    .p-chip {
        background: rgba(0, 0, 0, 0.12);
    }
}
</style>
