<template>
    <div class="codereview-report-wrap">
        <div class="codereview-report-content">
            <form>
                <div class="col-12 md:col-4 lg:col-3">
                    <label for="dd-year">年份</label>
                    <Calendar
                        id="year-input"
                        :manualInput="false"
                        v-model="form.year"
                        view="year"
                        :minDate="
                            new Date(
                                weeklyReportYear[weeklyReportYear.length - 1]
                            )
                        "
                        :maxDate="new Date(weeklyReportYear[0])"
                        dateFormat="yy"
                        @update:modelValue="getWeeks"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-4 lg:col-3">
                    <label for="dd-week">统计周期</label>
                    <Dropdown
                        v-model="form.week"
                        :options="this.weeklyReportWeek"
                        placeholder="请选择周"
                        class="w-full"
                    >
                        <template #value="slotProps">
                            <div class="flex align-items-center">
                                <div>
                                    第{{ slotProps.value.week }}周（{{
                                        formatDate(
                                            slotProps.value.weekStart,
                                            'YYYY-MM-DD'
                                        )
                                    }}至{{
                                        formatDate(
                                            slotProps.value.weekEnd,
                                            'YYYY-MM-DD'
                                        )
                                    }}）
                                </div>
                            </div>
                        </template>
                        <template #option="slotProps">
                            <div class="flex align-items-center">
                                <div>
                                    第{{ slotProps.option.week }}周（{{
                                        formatDate(
                                            slotProps.option.weekStart,
                                            'YYYY-MM-DD'
                                        )
                                    }}至{{
                                        formatDate(
                                            slotProps.option.weekEnd,
                                            'YYYY-MM-DD'
                                        )
                                    }}）
                                </div>
                            </div>
                        </template>
                    </Dropdown>
                </div>
                <div class="col-12 md:col-4 lg:col-3">
                    <label for="dd-city">团队</label>
                    <Dropdown
                        v-model="form.deptId"
                        :options="depts"
                        optionLabel="deptName"
                        optionValue="deptId"
                        placeholder="请选择团队"
                        class="w-full"
                    />
                </div>
                <div class="col-12 md:col-4 lg:col-3 flex align-items-end">
                    <Button
                        class="search w-full"
                        label="查询"
                        @click.prevent="getReport"
                    />
                </div>
            </form>

            <!-------------AI-Review统计周报------------->
            <section class="dept-report">
                <h3>团队统计周报</h3>

                <DataTable
                    :value="weeklyReport.weeklyDepartments"
                    :loading="table.loading"
                    showGridlines
                >
                    <ColumnGroup type="header">
                        <Row>
                            <Column
                                header="团队"
                                :rowspan="3"
                                class="dept-header"
                            />
                            <Column
                                header="统计周期"
                                :rowspan="3"
                                class="time-header"
                            />
                            <Column
                                header="master提交数"
                                :rowspan="3"
                                class="count-header"
                            />
                            <Column
                                header="产生优化建议的提交（粒度为提交）"
                                :rowspan="2"
                                :colspan="5"
                                class="colspan-header status-header"
                            />
                            <Column
                                header="已读后反馈情况（粒度为优化建议）"
                                :colspan="7"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column
                                header="总计"
                                :rowspan="2"
                                class="count-header"
                            />
                            <Column
                                header="未反馈数"
                                :rowspan="2"
                                class="count-header"
                            />
                            <Column
                                header="未反馈率"
                                :rowspan="2"
                                class="count-header"
                            />
                            <Column
                                header="已反馈"
                                :colspan="4"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column header="总计" class="count-header" />
                            <Column header="未读" class="status-header" />
                            <Column header="未读率" class="status-header" />
                            <Column header="已读" class="status-header" />
                            <Column header="已读率" class="status-header" />
                            <Column header="优秀" class="excellent-header" />
                            <Column header="合理" class="reasonable-header" />
                            <Column header="无营养" class="invalid-header" />
                            <Column header="错误" class="wrong-header" />
                        </Row>
                    </ColumnGroup>
                    <Column field="deptName">
                        <template #body="{ data }">
                            <div>{{ data.deptName }}</div>
                        </template>
                    </Column>
                    <Column field="统计周期">
                        <template #body="{ data }">
                            <div>
                                第{{ data.sweek }}周
                                <p />
                                {{
                                    formatDate(data.weekStart, 'YYYY-MM-DD')
                                }}至{{ formatDate(data.weekEnd, 'YYYY-MM-DD') }}
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCount">
                        <template #body="{ data }">
                            <div>{{ data.commitPushCount }}</div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggested">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&suggestionTypes=2'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggested }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedUnread">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&suggestionTypes=2&exposed=0'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggestedUnread }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedUnreadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.commitPushCountSuggestedUnreadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedRead">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&suggestionTypes=2&exposed=1'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggestedRead }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedReadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.commitPushCountSuggestedReadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountRead">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountRead }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluated">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountReadUnevaluated }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluatedRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.suggestionCountReadUnevaluatedRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadExcellent">
                        <template #body="{ data }">
                            <div class="excellent-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadExcellent
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadReasonable">
                        <template #body="{ data }">
                            <div class="reasonable-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadReasonable
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadInvalid">
                        <template #body="{ data }">
                            <div class="invalid-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadInvalid
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadWrong">
                        <template #body="{ data }">
                            <div class="wrong-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadWrong
                                }}</span>
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </section>

            <!-------------个人已读率排名------------->
            <section class="employee-report">
                <h3>个人已读率排名Top5</h3>
                <DataTable
                    :value="weeklyReport.weeklyEmployees"
                    sortable
                    :loading="table.loading"
                    showGridlines
                >
                    <ColumnGroup type="header">
                        <Row>
                            <Column
                                header="开发人员"
                                :rowspan="3"
                                class="dept-header"
                            />
                            <Column
                                header="master提交数"
                                :rowspan="3"
                                sortable
                                sortField="commitPushCount"
                                class="count-header"
                            />
                            <Column
                                header="产生优化建议的提交（粒度为提交）"
                                :rowspan="2"
                                :colspan="5"
                                class="colspan-header status-header"
                            />
                            <Column
                                header="已读后反馈情况（粒度为优化建议）"
                                :colspan="7"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column
                                header="总计"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountRead"
                                class="count-header"
                            />
                            <Column
                                header="未反馈数"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountReadUnevaluated"
                                class="count-header"
                            />
                            <Column
                                header="未反馈率"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountReadUnevaluatedRate"
                                class="count-header"
                            />
                            <Column
                                header="已反馈"
                                :colspan="4"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column
                                header="总计"
                                sortable
                                sortField="commitPushCountSuggested"
                                class="count-header"
                            />
                            <Column
                                header="未读"
                                sortable
                                sortField="commitPushCountSuggestedUnread"
                                class="status-header"
                            />
                            <Column
                                header="未读率"
                                sortable
                                sortField="commitPushCountSuggestedUnreadRate"
                                class="status-header"
                            />
                            <Column
                                header="已读"
                                sortable
                                sortField="commitPushCountSuggestedRead"
                                class="status-header"
                            />
                            <Column
                                header="已读率"
                                sortable
                                sortField="commitPushCountSuggestedReadRate"
                                class="status-header"
                            />

                            <Column
                                header="优秀"
                                sortable
                                sortField="suggestionCountReadExcellent"
                                class="excellent-header"
                            />
                            <Column
                                header="合理"
                                sortable
                                sortField="suggestionCountReadReasonable"
                                class="reasonable-header"
                            />
                            <Column
                                header="无营养"
                                sortable
                                sortField="suggestionCountReadInvalid"
                                class="invalid-header"
                            />
                            <Column
                                header="错误"
                                sortable
                                sortField="suggestionCountReadWrong"
                                class="wrong-header"
                            />
                        </Row>
                    </ColumnGroup>

                    <Column field="employeeName">
                        <template #body="{ data }">
                            <div>{{ data.employeeName }}</div>
                        </template>
                    </Column>
                    <Column field="commitPushCount">
                        <template #body="{ data }">
                            <div>{{ data.commitPushCount }}</div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggested">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&employees=[' +
                                        JSON.stringify({
                                            employeeName: data.employeeName,
                                            accountName: data.accountName,
                                        }) +
                                        ']&suggestionTypes=2'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggested }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedUnread">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&employees=[' +
                                        JSON.stringify({
                                            employeeName: data.employeeName,
                                            accountName: data.accountName,
                                        }) +
                                        ']&suggestionTypes=2&exposed=0'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggestedUnread }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedUnreadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.commitPushCountSuggestedUnreadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedRead">
                        <template #body="{ data }">
                            <div>
                                <a
                                    :href="
                                        '/app-chatgpt/codereview?deptId=' +
                                        data.deptId +
                                        '&dates=[' +
                                        new Date(data.weekStart).getTime() +
                                        ',' +
                                        (new Date(data.weekEnd).getTime() +
                                            24 * 60 * 60 * 1000 -
                                            1) +
                                        ']&employees=[' +
                                        JSON.stringify({
                                            employeeName: data.employeeName,
                                            accountName: data.accountName,
                                        }) +
                                        ']&suggestionTypes=2&exposed=1'
                                    "
                                    target="_blank"
                                >
                                    {{ data.commitPushCountSuggestedRead }}
                                </a>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitPushCountSuggestedReadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.commitPushCountSuggestedReadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountRead">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountRead }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluated">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountReadUnevaluated }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluatedRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.suggestionCountReadUnevaluatedRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadExcellent">
                        <template #body="{ data }">
                            <div class="excellent-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadExcellent
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadReasonable">
                        <template #body="{ data }">
                            <div class="reasonable-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadReasonable
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadInvalid">
                        <template #body="{ data }">
                            <div class="invalid-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadInvalid
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadWrong">
                        <template #body="{ data }">
                            <div class="wrong-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadWrong
                                }}</span>
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </section>

            <!-------------��化建议处理情况------------->
            <section class="suggestion-report">
                <h3>优化建议处理情况</h3>

                <DataTable
                    :value="weeklyReport.weeklySuggestions"
                    :loading="table.loading"
                    showGridlines
                >
                    <ColumnGroup type="header">
                        <Row>
                            <Column
                                header="优化建议分类"
                                :rowspan="3"
                                class="dept-header"
                            />
                            <Column
                                header="优化建议数"
                                :rowspan="3"
                                sortable
                                sortField="suggestionCount"
                                class="count-header"
                            />
                            <Column
                                header="未读"
                                :rowspan="3"
                                sortable
                                sortField="suggestionCountUnread"
                                class="status-header"
                            />
                            <Column
                                header="未读率"
                                :rowspan="3"
                                sortable
                                sortField="suggestionCountUnreadRate"
                                class="status-header"
                            />
                            <Column
                                header="已读后反馈情况"
                                :colspan="8"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column
                                header="已读"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountRead"
                                class="count-header"
                            />
                            <Column
                                header="已读率"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountReadRate"
                                class="count-header"
                            />
                            <Column
                                header="未反馈数"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountReadUnevaluated"
                                class="count-header"
                            />
                            <Column
                                header="未反馈率"
                                :rowspan="2"
                                sortable
                                sortField="suggestionCountReadUnevaluatedRate"
                                class="count-header"
                            />
                            <Column
                                header="已反馈"
                                :colspan="4"
                                class="colspan-header status-header"
                            />
                        </Row>
                        <Row>
                            <Column
                                header="优秀"
                                sortable
                                sortField="suggestionCountReadExcellent"
                                class="excellent-header"
                            />
                            <Column
                                header="合理"
                                sortable
                                sortField="suggestionCountReadReasonable"
                                class="reasonable-header"
                            />
                            <Column
                                header="无营养"
                                sortable
                                sortField="suggestionCountReadInvalid"
                                class="invalid-header"
                            />
                            <Column
                                header="错误"
                                sortable
                                sortField="suggestionCountReadWrong"
                                class="wrong-header"
                            />
                        </Row>
                    </ColumnGroup>
                    <Column field="suggestionCategory">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCategory }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCount">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCount }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountUnread">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountUnread }}</div>
                        </template>
                    </Column>
                    <Column header="未读率" field="suggestionCountUnreadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.suggestionCountUnreadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountRead">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountRead }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.suggestionCountReadRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluated">
                        <template #body="{ data }">
                            <div>{{ data.suggestionCountReadUnevaluated }}</div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadUnevaluatedRate">
                        <template #body="{ data }">
                            <div>
                                {{
                                    formatPercentage(
                                        data.suggestionCountReadUnevaluatedRate
                                    )
                                }}
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadExcellent">
                        <template #body="{ data }">
                            <div class="excellent-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadExcellent
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadReasonable">
                        <template #body="{ data }">
                            <div class="reasonable-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadReasonable
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadInvalid">
                        <template #body="{ data }">
                            <div class="invalid-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadInvalid
                                }}</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="suggestionCountReadWrong">
                        <template #body="{ data }">
                            <div class="wrong-cell">
                                <span class="feedback-value">{{
                                    data.suggestionCountReadWrong
                                }}</span>
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </section>
        </div>
    </div>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia';
import useCodereviewStore from '@/app/stores/codereview';

import dayjs from 'dayjs';

export default {
    name: 'Codereview',
    components: {},
    data() {
        return {
            minDate: new Date('2023'),
            maxDate: new Date('2024'),
            form: {
                year: '',
                week: '',
                deptId: '',
            },
            table: {
                loading: false,
                first: 0,
                totalRecords: 0,
            },
        };
    },
    computed: {
        ...mapWritableState(useCodereviewStore, [
            'weeklyReport',
            'weeklyReportYear',
            'weeklyReportWeek',
            'depts',
        ]),
    },
    methods: {
        ...mapActions(useCodereviewStore, [
            'getDeptList',
            'getWeeklyReport',
            'getWeeklyReportYear',
            'getWeeklyReportWeek',
        ]),
        async getWeeks() {
            await this.getWeeklyReportWeek({
                year: this.formatDate(this.form.year, 'YYYY'),
            });
            if (this.weeklyReportWeek) {
                this.form.week = this.weeklyReportWeek[0];
            }
        },
        async init() {
            await this.getDeptList();
            await this.getWeeklyReportYear();
            if (this.weeklyReportYear) {
                this.form.year = new Date(this.weeklyReportYear[0]);
                await this.getWeeks();
            }

            this.depts.forEach((item) => {
                if (item.selected) {
                    this.form.deptId = item.deptId;
                }
            });
            if (!this.form.deptId && this.depts) {
                this.form.deptId = this.depts[0].deptId;
            }

            await this.getReport();
        },
        async getReport() {
            await this.getWeeklyReport({
                year: this.formatDate(this.form.year, 'YYYY'),
                week: this.form.week.week,
                deptId: this.form.deptId,
            });
        },
        formatDate(value, pattern) {
            if (value) {
                return dayjs(value).format(
                    pattern ? pattern : 'YYYY-MM-DD HH:mm:ss'
                );
            }
            return '';
        },
        formatPercentage(percentage) {
            if (percentage === -1) {
                return '-'; // 或者可以返回 "0.00%"，取决于你的需求
            }
            percentage = percentage * 100;
            // 如果百分比是整数，则不显示小数点后的数字
            let percentageStr = percentage.toFixed(2);
            // 使用正则表达式去除末尾的零和不必要的小数点
            percentageStr = percentageStr.replace(
                /(\.\d*?[1-9])0+$|\.0*$/,
                '$1'
            );
            return percentageStr + '%';
        },
    },
    mounted() {
        this.init();
    },
};
</script>

<style lang="sass" scoped>
.codereview-report-wrap
  height: 100%
  overflow-y: scroll
  background-color: #f5f7fa
  .codereview-report-content
    max-width: 1200px
    margin: 20px auto
    padding: 20px

  // 各个section的通用样式
  .dept-report, .suggestion-report, .employee-report
    background: #fff
    border-radius: 8px
    padding: 20px
    box-shadow: 0 2px 12px rgba(0,0,0,0.05)
    margin-bottom: 24px

    h3
      color: #2c3e50
      font-size: 18px
      margin: 0 0 20px 0
      padding-left: 12px
      border-left: 4px solid #3F51B5
      line-height: 1.4

  // 搜索表单样式优化
  form
    background: #fff
    padding: 24px
    border-radius: 8px
    box-shadow: 0 2px 12px rgba(0,0,0,0.05)
    display: flex
    justify-content: space-between
    align-items: top
    flex-wrap: wrap
    margin-bottom: 24px

    .col-4
      display: flex
      flex: 0
      flex-basis: calc( 33.33% - 2.8rem )
      justify-content: space-between
      align-items: center
      margin: 0 1rem 1rem

      label
        white-space: nowrap
        margin-right: 1rem
        color: #606266
        font-weight: 500

      .search
        width: 8rem
        margin-left: auto
        background: #3F51B5
        border: none
        &:hover
          background: #303f9f

      .p-dropdown, .p-calendar
        width: 100%
        .p-dropdown-label
          padding: 8px 12px

    .col-search
      margin-left: auto

  // 表格样式优化
  .p-datatable
    margin: 0
    width: 100%

    .p-datatable-header
      background: #f8f9fa

    .p-column-header-content
      font-weight: 600
      color: #2c3e50

    .colspan-header
      background: #e3f2fd

    a
      color: #1976D2
      text-decoration: none
      transition: color 0.2s
      &:hover
        color: #1565C0
        text-decoration: underline

    .p-table-empty-data
      color: #909399
      text-align: center
      padding: 24px

    // 表格条纹样式
    .p-datatable-tbody > tr:nth-child(even)
      background: #f8f9fa

    .p-datatable-tbody > tr > td
      padding: 12px 8px
      border-color: #e0e0e0

    // 单元格背景色和字体样式
    .cell-bg
      padding: 8px
      border-radius: 4px

    .excellent-cell
      background-color: #e8f5e9
      .feedback-value
        color: #2e7d32
        font-weight: bold

    .reasonable-cell
      background-color: #e3f2fd
      .feedback-value
        color: #1976d2
        font-weight: bold

    .invalid-cell
      background-color: #fff3e0
      .feedback-value
        color: #e65100
        font-weight: bold

    .wrong-cell
      background-color: #ffebee
      .feedback-value
        color: #b71c1c
        font-weight: bold

    // 表头样式
    .p-column-header
      background: #e8eaf6
      border-color: #c5cae9
      font-weight: bold
      color: #3f51b5

    // 数据单元格悬停效果
    .p-datatable-tbody > tr:hover
      background: #ede7f6
      transition: background-color 0.2s

    // 链接样式
    .p-datatable-tbody > tr > td a
      color: #3f51b5
      font-weight: 500
      &:hover
        color: #283593

  // 修改表格表头样式
  ::v-deep .p-datatable
    .p-datatable-thead > tr > th
      border: 1px solid #dee2e6 !important
      background-color: #e8eaf6 !important

      // 评价类型相关表头
      &.excellent-header
        background-color: rgba(25, 118, 210, 0.1) !important  // 蓝色背景
        .p-column-title
          color: #1976d2  // 蓝色文字
        &:hover
          background-color: rgba(25, 118, 210, 0.15) !important

      &.reasonable-header
        background-color: rgba(46, 125, 50, 0.1) !important  // 绿色背景
        .p-column-title
          color: #2e7d32  // 绿色文字
        &:hover
          background-color: rgba(46, 125, 50, 0.15) !important

      &.invalid-header
        background-color: rgba(237, 108, 2, 0.1) !important
        .p-column-title
          color: #ed6c02
        &:hover
          background-color: rgba(237, 108, 2, 0.15) !important

      &.wrong-header
        background-color: rgba(211, 47, 47, 0.1) !important
        .p-column-title
          color: #d32f2f
        &:hover
          background-color: rgba(211, 47, 47, 0.15) !important

      // 主要信息表头（如姓名、ID等）
      &.primary-header
        background-color: rgba(103, 58, 183, 0.1) !important // 紫色
        .p-column-title
          color: #673ab7
        &:hover
          background-color: rgba(103, 58, 183, 0.15) !important

      // 时间相关表头
      &.time-header
        background-color: rgba(0, 150, 136, 0.1) !important // 青色
        .p-column-title
          color: #009688
        &:hover
          background-color: rgba(0, 150, 136, 0.15) !important

      // 数量统计相关表头
      &.count-header
        background-color: rgba(121, 85, 72, 0.1) !important // 棕色
        .p-column-title
          color: #795548
        &:hover
          background-color: rgba(121, 85, 72, 0.15) !important

      // 状态相关表头
      &.status-header
        background-color: rgba(96, 125, 139, 0.1) !important // 蓝灰
        .p-column-title
          color: #607d8b
        &:hover
          background-color: rgba(96, 125, 139, 0.15) !important

      // 排序图标颜色跟随文字颜色
      .p-sortable-column-icon
        color: inherit

    // 激活状态的表头样式
    .p-datatable-thead > tr > th.p-highlight
      &.excellent-header
        background-color: rgba(25, 118, 210, 0.2) !important  // 蓝色加深
      &.reasonable-header
        background-color: rgba(46, 125, 50, 0.2) !important   // 绿色加深
      &.invalid-header
        background-color: rgba(237, 108, 2, 0.2) !important
      &.wrong-header
        background-color: rgba(211, 47, 47, 0.2) !important

    // 确保表格单元格也有边框
    .p-datatable-tbody > tr > td
      border: 1px solid #dee2e6 !important

    // 表格边框折叠
    border-collapse: collapse

    // 确保表格外边框完整
    border: 1px solid #dee2e6

    ::v-deep
      .p-datatable-thead > tr > th
        padding: 16px 12px  // 增加表头内边距

      .p-datatable-tbody > tr > td
        padding: 16px 12px  // 增加单元格内边距

    // 团队统计周报表格
    &.dept-header
      width: 150px
    &.time-header
      width: 200px
    &.count-header
      width: 100px
    &.status-header
      width: 120px
    &.excellent-header,
    &.reasonable-header,
    &.invalid-header,
    &.wrong-header
      width: 100px

    // 个人已读率排名表格
    &[data-field="employeeName"]
      width: 120px
    &[data-field="commitPushCount"]
      width: 100px
    &[data-field="suggestionCountRead"]
      width: 100px
    &[data-field="suggestionCountReadUnevaluated"]
      width: 120px

    // 优化建议处理情况表格
    &[data-field="suggestionCategory"]
      width: 200px
    &[data-field="suggestionCount"]
      width: 100px
    &[data-field="suggestionCountUnread"]
      width: 100px
</style>
