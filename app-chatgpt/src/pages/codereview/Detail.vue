<template>
    <div class="codereview-detail-wrap">
        <div class="codereview-detail-content">
            <ProgressBar
                v-show="loading"
                mode="indeterminate"
                style="height: 6px"
            ></ProgressBar>
            <section class="project-info flex flex-column">
                <p>
                    <router-link
                        :to="{
                            name:
                                from === 'codereview'
                                    ? 'codereview'
                                    : 'my-review',
                        }"
                    >
                        <Button
                            label="返回列表页"
                            outlined
                            icon="pi pi-arrow-left"
                        ></Button>
                    </router-link>
                </p>
                <h3>项目信息</h3>
                <DataTable showGridlines :value="table.commitInfo">
                    <Column field="deptName" header="团队"></Column>
                    <Column field="appNames" header="应用"></Column>
                    <Column field="gitUrl" header="Git地址">
                        <template #body="{ data }">
                            <Button label="Link" link class="p-0"
                                ><a
                                    :href="data.gitUrl"
                                    target="_blank"
                                    class=""
                                    >{{ data.gitUrl }}</a
                                ></Button
                            >
                        </template>
                    </Column>
                </DataTable>
            </section>
            <section class="commit-info">
                <h3>提交信息</h3>
                <DataTable showGridlines :value="table.commitInfo">
                    <Column field="commitEmployeeName" header="提交人"></Column>
                    <Column field="createdStime" header="时间">
                        <template #body="{ data }">
                            {{ formatDate(data.createdStime) }}
                        </template>
                    </Column>
                    <Column field="commitSha" header="版本号">
                        <template #body="{ data }">
                            <div v-if="data.type == 0">
                                <Button label="Link" link class="p-0"
                                    ><a :href="data.commitLink" target="_blank"
                                        >普通提交</a
                                    ></Button
                                >
                            </div>
                            <div v-else-if="data.type == 1">
                                <Button label="Link" link class="p-0"
                                    ><a
                                        :href="
                                            data.gitUrl.slice(0, -4) +
                                            '/merge_requests/' +
                                            data.mergeRequest
                                                .gitlabProjectMergeRequestId
                                        "
                                        target="_blank"
                                        >合并请求</a
                                    ></Button
                                >
                            </div>
                            <div v-else-if="data.type == 2">
                                <Button label="Link" link class="p-0"
                                    ><a
                                        :href="
                                            data.gitUrl.slice(0, -4) +
                                            '/compare/' +
                                            data.pushEvent.commitFrom +
                                            '...' +
                                            data.pushEvent.commitTo
                                        "
                                        target="_blank"
                                        >推送事件</a
                                    ></Button
                                >
                            </div>
                        </template>
                    </Column>
                    <Column field="diffCount" header="变更文件数"></Column>
                    <Column field="reviewCount" header="Review文件数"></Column>
                    <Column
                        field="suggestionCount"
                        header="有建议的文件个数"
                    ></Column>
                </DataTable>
            </section>
            <section class="review-info">
                <h3>GPT Review 建议</h3>
                <!-- 判断 : codeCommitDetail.diffs 为空状态 -->
                <Card
                    v-if="
                        codeCommitDetail.diffs &&
                        codeCommitDetail.diffs.length < 1
                    "
                >
                    <template #content> 无 GPT Review 记录 </template>
                </Card>
                <!-- 判断 : codeCommitDetail.diffs 非空状态 -->
                <Fieldset
                    v-else
                    :toggleable="true"
                    v-for="(item, index) in codeCommitDetail.diffs"
                    :key="index"
                    class="mb-5"
                >
                    <template #legend>
                        <div class="flex align-items-center">
                            <i
                                v-if="item.reviewStatus == 2"
                                class="pi pi-verified"
                                style="
                                    font-size: 17px;
                                    color: green;
                                    margin-right: 10px;
                                "
                            ></i>
                            <span class="font-bold p-0">{{
                                item.filePath
                            }}</span>
                        </div>
                    </template>
                    <div class="diff" :class="`item-diff${index}`">
                        <div class="diff-btns">
                            <div class="flex align-items-center">
                                <Button
                                    text
                                    icon="pi pi-copy"
                                    label="复制提示词"
                                    size="small"
                                    @click="copy(item.promptToClipboard)"
                                />
                                <a
                                    :href="item.diffLink"
                                    target="_blank"
                                    rel="noopener"
                                    class="flex align-items-center"
                                >
                                    <Button
                                        text
                                        size="small"
                                        label="查看Commit"
                                        icon="pi pi-external-link"
                                        @click="
                                            reportUserEvent(
                                                'codereview-detail-hrefToCommit'
                                            )
                                        "
                                    />
                                </a>
                                <a
                                    :href="item.fileLink"
                                    target="_blank"
                                    rel="noopener"
                                    class="flex align-items-center"
                                >
                                    <Button
                                        text
                                        size="small"
                                        label="查看文件"
                                        icon="pi pi-file"
                                        @click="
                                            reportUserEvent(
                                                'codereview-detail-hrefToFile'
                                            )
                                        "
                                    />
                                </a>
                            </div>
                        </div>
                        <div
                            class="diff-render"
                            v-html="prettyDiff(item.diff)"
                        />
                    </div>
                    <div class="suggestion" v-if="item.suggestionType == 2">
                        <template v-if="item.gptOptimizationSuggestion == null">
                            <Card
                                v-for="sgst in item.suggestions"
                                :key="sgst.id"
                                class="suggestion-item"
                                :class="{
                                    excellent: sgst.processType == 1,
                                    usable: sgst.processType == 2,
                                    worthless: sgst.processType == 3,
                                    wrong: sgst.processType == 2,
                                }"
                            >
                                <template #title>
                                    <div class="suggestion-category">
                                        {{ sgst.suggestionCategory }}
                                    </div>
                                </template>
                                <template #content>
                                    <div
                                        class="markdown-body"
                                        :class="{
                                            excellent: sgst.processType == 1,
                                            reasonable: sgst.processType == 2,
                                            worthless: sgst.processType == 3,
                                            wrong: sgst.processType == 2,
                                        }"
                                        v-html="
                                            renderMarkdown(
                                                sgst.optimizationSuggestion ||
                                                    ''
                                            )
                                        "
                                    ></div>
                                    <p class="btns-bar mb-0">
                                        <Button
                                            :label="sgst.evaluationButton"
                                            :severity="
                                                sgst.evaluationButtonSeverity
                                            "
                                            @click="
                                                showEvaluationDialog(
                                                    $event,
                                                    sgst
                                                )
                                            "
                                        />
                                        <template
                                            v-if="
                                                sgst.evaluationContent &&
                                                sgst.evaluationContent.trim()
                                            "
                                            >（{{
                                                sgst.evaluationContent
                                            }}）</template
                                        >
                                    </p>
                                    <ConfirmPopup
                                        class="p-4"
                                        :group="'evaluation-options-' + sgst.id"
                                    >
                                        <template #message="slotProps">
                                            <div class="content">
                                                <h4
                                                    class="mt-0"
                                                    style="color: #3f51b5"
                                                >
                                                    请对GPT给出的优化建议做出评价
                                                </h4>
                                                <form>
                                                    <div
                                                        class="evaluationGroup flex gap-4"
                                                    >
                                                        <div
                                                            class="flex align-items-center"
                                                        >
                                                            <RadioButton
                                                                v-model="
                                                                    sgst.evaluationRadio
                                                                "
                                                                inputId="ingredient1"
                                                                name="evaluation"
                                                                value="1"
                                                            />
                                                            <label
                                                                class="ml-2"
                                                                for="ingredient1"
                                                                >优秀</label
                                                            >
                                                        </div>
                                                        <div
                                                            class="flex align-items-center"
                                                        >
                                                            <RadioButton
                                                                v-model="
                                                                    sgst.evaluationRadio
                                                                "
                                                                inputId="ingredient2"
                                                                name="evaluation"
                                                                value="2"
                                                            />
                                                            <label
                                                                class="ml-2"
                                                                for="ingredient2"
                                                                >合理</label
                                                            >
                                                        </div>
                                                        <div
                                                            class="flex align-items-center"
                                                        >
                                                            <RadioButton
                                                                v-model="
                                                                    sgst.evaluationRadio
                                                                "
                                                                inputId="ingredient3"
                                                                name="evaluation"
                                                                value="3"
                                                            />
                                                            <label
                                                                class="ml-2"
                                                                for="ingredient3"
                                                                >无营养</label
                                                            >
                                                        </div>
                                                        <div
                                                            class="flex align-items-center"
                                                        >
                                                            <RadioButton
                                                                v-model="
                                                                    sgst.evaluationRadio
                                                                "
                                                                inputId="ingredient4"
                                                                name="evaluation"
                                                                value="4"
                                                            />
                                                            <label
                                                                class="ml-2"
                                                                for="ingredient4"
                                                                >错误</label
                                                            >
                                                        </div>
                                                    </div>
                                                    <p>
                                                        <Textarea
                                                            v-model="
                                                                sgst.evaluationText
                                                            "
                                                            rows="5"
                                                            placeholder="备注"
                                                            class="w-full"
                                                        />
                                                    </p>
                                                </form>
                                                <p style="display: none">
                                                    {{
                                                        slotProps.message
                                                            .message
                                                    }}
                                                </p>
                                            </div>
                                        </template>
                                    </ConfirmPopup>
                                </template>
                            </Card>
                        </template>
                        <template v-else>
                            <Card>
                                <template #content>
                                    <div
                                        class="markdown-body"
                                        v-html="
                                            renderMarkdown(
                                                item.gptOptimizationSuggestion ||
                                                    ''
                                            )
                                        "
                                    ></div>
                                    <p class="btns-bar">
                                        <Button
                                            icon="pi pi-thumbs-up-fill"
                                            plain
                                            text
                                            rounded
                                            v-tooltip.top="'好的答案'"
                                            @click="
                                                sendFeedback(
                                                    'thumbUp',
                                                    item,
                                                    $event
                                                )
                                            "
                                            :class="{
                                                active: item.acceptedType === 1,
                                            }"
                                        />
                                        <Button
                                            icon="pi pi-thumbs-down-fill"
                                            plain
                                            text
                                            rounded
                                            :class="{
                                                active: item.acceptedType === 2,
                                            }"
                                            v-tooltip.top="'糟糕的答案'"
                                            @click="
                                                sendFeedback(
                                                    'thumbDown',
                                                    item,
                                                    $event
                                                )
                                            "
                                        />
                                    </p>
                                </template>
                            </Card>
                        </template>
                    </div>
                    <div class="suggestion" v-else>
                        <Card>
                            <template #content>
                                <p style="padding-left: 1rem; margin: 0">
                                    已通过GPT Review，无修改建议
                                </p>
                            </template>
                        </Card>
                    </div>
                </Fieldset>
            </section>

            <!-- 弹窗 : CodeReview 改进意见的反馈 -->
            <ConfirmPopup class="p-2" group="feedback-thumbDown-options">
                <template #message="slotProps">
                    <div class="content">
                        <h4 class="mt-0">你的反馈将帮助我们优化进步</h4>
                        <form>
                            <p class="btns">
                                <ToggleButton
                                    v-for="item in acceptedReasonList"
                                    :key="item"
                                    :onLabel="item.acceptedReasonName"
                                    :offLabel="item.acceptedReasonName"
                                    onIcon="pi pi-check"
                                    offIcon="pi"
                                    v-model="item.active"
                                />
                            </p>
                            <p>
                                <Textarea
                                    v-model="
                                        form.feedback.acceptedReasonContent
                                    "
                                    rows="5"
                                    placeholder="其他原因"
                                />
                            </p>
                        </form>
                        <p style="display: none">
                            {{ slotProps.message.message }}
                        </p>
                    </div>
                </template>
            </ConfirmPopup>
        </div>
    </div>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia';
import useAppStore from '@/app/stores/app';
import useCodereviewStore from '@/app/stores/codereview';

import dayjs from 'dayjs';
// Diff
import * as Diff2Html from 'diff2html';
import 'diff2html/bundles/css/diff2html.min.css';
// Markdown
import MarkdownIt from 'markdown-it';
const md = new MarkdownIt();

import { copyToClipboard } from '@/app/utils/index';

export default {
    name: 'CodereviewDetail',
    components: {},
    data() {
        return {
            from: 'codereview',
            loading: false,
            table: {
                commitInfo: [],
            },
            dialog: {},
            ingredient: '',
            form: {
                feedback: {
                    acceptedReasonContent: '',
                },
            },
        };
    },
    computed: {
        ...mapWritableState(useCodereviewStore, [
            'codeCommitDetail',
            'acceptedReasonList',
        ]),
    },
    methods: {
        ...mapActions(useAppStore, ['reportUserEvent']),
        ...mapActions(useCodereviewStore, [
            'getCodeCommitDetail',
            'getAcceptedReasonList',
            'updateAcceptedReason',
            'updateAcceptSuggestion',
            'evaluate',
        ]),
        async init() {
            this.loading = true;
            this.from = this.$route.query.from;
            const params = {
                codeCommitId: this.$route.params.id,
            };
            try {
                await this.getCodeCommitDetail(params);
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                this.loading = false;
                return;
            }
            this.loading = false;
            this.table.commitInfo[0] = this.codeCommitDetail;

            // 反馈类型列表
            await this.getAcceptedReasonList();
        },
        async sendFeedback(type, data, event) {
            const { acceptedType, codeCommitDiffId } = data;
            const body = {
                codeCommitDiffId,
                acceptedType: null,
            };
            if (acceptedType === 0 || acceptedType == null) {
                if (type === 'thumbUp') {
                    body.acceptedType = 1;
                } else {
                    body.acceptedType = 2;
                }
            } else if (acceptedType === 1) {
                if (type === 'thumbUp') {
                    body.acceptedType = 0;
                } else {
                    body.acceptedType = 2;
                }
            } else if (acceptedType === 2) {
                if (type === 'thumbUp') {
                    body.acceptedType = 1;
                } else {
                    body.acceptedType = 0;
                }
            }
            try {
                if (body.acceptedType === 2) {
                    // WARNING : 此处的 this.showFeedbackDialog(event), 如果放在 await this.updateAcceptSuggestion(body); 之后, event.currentTarget 值会变为 Null
                    this.showFeedbackDialog(event, data);
                    console.log(event);
                }
                await this.updateAcceptSuggestion(body);
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
            if (body.acceptedType === 0) {
                this.$toast.add({
                    severity: 'info',
                    summary: '已取消反馈',
                    life: 3000,
                });
            }
            this.init();
            await this.reportUserEvent(`codereview-detail-${type}`);
        },
        showFeedbackDialog(event, data) {
            this.$confirm.require({
                target: event.currentTarget,
                group: 'feedback-thumbDown-options',
                message: 'Please confirm to proceed moving forward.',
                icon: 'pi pi-exclamation-circle',
                rejectLabel: '取消',
                rejectIcon: 'pi pi-times',
                rejectClass: 'p-button-sm',
                acceptLabel: '确认',
                acceptIcon: 'pi pi-check',
                acceptClass: 'p-button-outlined p-button-sm',
                accept: async () => {
                    const body = {
                        codeCommitDiffId: data.codeCommitDiffId,
                        acceptedReasonIds: [],
                        acceptedReasonContent:
                            this.form.feedback.acceptedReasonContent,
                    };
                    const acceptedReasonIds = [];
                    this.acceptedReasonList.forEach((item) => {
                        if (item.active) {
                            acceptedReasonIds.push(item.acceptedReasonId);
                        }
                    });
                    body.acceptedReasonIds = acceptedReasonIds;
                    try {
                        await this.updateAcceptedReason(body);
                        this.$toast.add({
                            severity: 'success',
                            summary: '成功',
                            detail: '感谢您的反馈!',
                            life: 3000,
                        });
                    } catch (err) {
                        console.log(err);
                        this.$toast.add({
                            severity: 'error',
                            summary: '失败',
                            detail: err.message,
                            life: 3000,
                        });
                    }
                    this.form.feedback.acceptedReasonContent = '';
                },
                reject: () => {
                    this.form.feedback.acceptedReasonContent = '';
                },
            });
        },
        showEvaluationDialog(event, data) {
            this.$confirm.require({
                target: event.currentTarget,
                group: 'evaluation-options-' + data.id,
                message: 'Please confirm to proceed moving forward.',
                icon: 'pi pi-exclamation-circle',
                rejectLabel: '取消',
                rejectIcon: 'pi pi-times',
                rejectClass: 'p-button-sm',
                acceptLabel: '确认',
                acceptIcon: 'pi pi-check',
                acceptClass: 'p-button-outlined p-button-sm',
                accept: async () => {
                    const body = {
                        codeCommitSuggestionId: data.id,
                        codeCommitId: data.codeCommitId,
                        evaluationType: data.evaluationRadio,
                        evaluationContent: data.evaluationText,
                    };
                    try {
                        await this.evaluate(body);
                        await this.init();
                        this.$toast.add({
                            severity: 'success',
                            summary: '成功',
                            detail: '感谢您的评价!',
                            life: 3000,
                        });
                    } catch (err) {
                        console.log(err);
                        data.evaluationRadio = data.evaluationType + '';
                        data.evaluationText = data.evaluationContent;
                        this.$toast.add({
                            severity: 'error',
                            summary: '失败',
                            detail: err.message,
                            life: 3000,
                        });
                    }
                },
                reject: () => {
                    data.evaluationRadio = data.evaluationType + '';
                    data.evaluationText = data.evaluationContent;
                },
            });
        },
        prettyDiff(diffStr) {
            return Diff2Html.html(diffStr, {
                // matching: 'lines',
                // outputFormat: 'line-by-line',
                colorScheme: 'light',
            });
        },
        renderMarkdown(markdownContent) {
            return md.render(markdownContent);
        },
        formatDate(value) {
            if (value) {
                return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            }
            return '';
        },
        async copy(input, type) {
            try {
                await copyToClipboard(input, type);
                this.$toast.add({
                    severity: 'success',
                    summary: '成功',
                    detail: '已复制到剪贴板',
                    life: 3000,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'err',
                    summary: '失败',
                    detail: err,
                    life: 3000,
                });
            }
            await this.reportUserEvent('codereview-detail-copy');
        },
    },
    mounted() {
        this.init();
        // TODO : 上报详情页访问 PV
        const query = this.$route.query;
        if (query.referer_from === 'dingtalk') {
            this.reportUserEvent('codereview-detail-pv-dingtalk');
        }
        this.reportUserEvent('codereview-detail-pv');
    },
};
</script>

<style lang="scss" scoped>
.codereview-detail-wrap {
    height: calc(100vh - 60px);
    overflow-y: auto;
    background-color: #f5f7fa;
    -webkit-overflow-scrolling: touch;

    .codereview-detail-content {
        height: 100%;
        max-width: 1200px;
        margin: 10px auto;
        padding: 20px;

        a {
            color: #1976d2;
            text-decoration: none;
            transition: color 0.2s;
            &:hover {
                color: #1565c0;
                text-decoration: underline;
            }
        }

        .p-progressbar {
            position: fixed;
            left: 0;
            top: 55px;
            width: 100%;
            z-index: 9;
        }

        section {
            background: #fff;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
            margin-bottom: 24px;

            .p-datatable {
                margin: 0;
                width: 100%;
            }
        }

        .commit-info,
        .review-info {
            margin-top: 2rem;
        }

        .review-info {
            .diff {
                position: relative;
                z-index: 9;

                .diff-btns {
                    position: absolute;
                    top: 3px;
                    left: 6px;
                    z-index: 99;
                }
            }

            .suggestion {
                margin-top: 2rem;

                .suggestion-item {
                    margin-bottom: 20px;

                    .suggestion-category {
                        margin-bottom: 8px;
                        font-size: 25px;
                        font-weight: 700;
                    }
                }

                .btns-bar {
                    button {
                        margin-right: 0.5rem;
                    }

                    .active {
                        color: #6366f1;
                    }
                }
            }
        }

        :deep {
            .diff-render {
                .d2h-file-name-wrapper {
                    display: none;
                }
            }

            .d2h-file-list-wrapper {
                display: none;
            }

            .d2h-code-line-ctn {
                white-space: pre-wrap;
            }

            .p-datatable-thead > tr > th {
                border: 1px solid #dee2e6 !important;
                background-color: #e8eaf6 !important;
                font-weight: bold;
                color: #3f51b5;
                padding: 16px 12px;
            }

            .p-datatable-tbody > tr {
                &:nth-child(even) {
                    background: #f8f9fa;
                }
                &:hover {
                    background: #ede7f6;
                    transition: background-color 0.2s;
                }

                > td {
                    padding: 16px 12px;
                    border: 1px solid #e0e0e0 !important;
                }
            }

            .p-datatable {
                .p-datatable-thead > tr > th {
                    &[data-field="deptName"] {
                        width: 120px;
                    }
                    &[data-field="appNames"] {
                        width: 200px;
                    }
                    &[data-field="gitUrl"] {
                        width: 300px;
                    }
                    &[data-field="commitEmployeeName"] {
                        width: 100px;
                    }
                    &[data-field="createdStime"] {
                        width: 180px;
                    }
                    &[data-field="commitSha"] {
                        width: 120px;
                    }
                    &[data-field="diffCount"],
                    &[data-field="reviewCount"],
                    &[data-field="suggestionCount"] {
                        width: 100px;
                    }
                }
            }
        }
    }
}
</style>
