<template>
    <div class="w-full h-full overflow-y-auto" style="background-color: #f5f7fa">
        <div class="sonar-detail-container">
            <!-- 顶部标题区域，添加logo和优化标题 -->
            <div class="page-header">
                <div class="logo-title-container">
                    <!-- 点击 logo 支持跳转到 sonar 首页 -->
                    <img src="@/app/assets/sonarqube.svg" alt="Sonar Logo" class="sonar-logo" @click="goSonar" />
                    <h1 class="page-title gradient-title">增量扫描详情</h1>
                </div>
                <Button icon="pi pi-arrow-left" label="返回" class="p-button-secondary" @click="goBack" />
            </div>

            <Loading v-if="loading" />

            <div v-else-if="!sonarDetail" class="flex justify-center my-8">
                <Message severity="error" :closable="false">
                    <div class="mb-2 font-bold">加载失败</div>
                    <p>无法获取扫描详情数据，请稍后重试。</p>
                </Message>
            </div>

            <div v-else>
                <!-- 项目基本信息 - 优化为更简约的样式 -->
                <Panel header="基本信息" class="data-panel mb-4" toggleable :collapsed="false">
                    <div class="basic-info-container">
                        <div class="info-pill">
                            <i class="pi pi-folder-open info-icon"></i>
                            <span class="info-text">{{ sonarDetail.projectName || '-' }}</span>
                        </div>
                        <div class="info-pill">
                            <i class="pi pi-sitemap info-icon"></i>
                            <span class="info-text">{{ sonarDetail.branchName || '-' }}</span>
                        </div>
                        <div
                            class="info-pill"
                            @click="copyCommitSha(sonarDetail.commitSha)"
                            v-if="sonarDetail.commitSha"
                        >
                            <i class="pi pi-hashtag info-icon"></i>
                            <span class="info-text commit-link">{{ sonarDetail.commitSha }}</span>
                        </div>
                        <div class="info-pill" v-if="sonarDetail.gitUrl">
                            <i class="pi pi-github info-icon"></i>
                            <a
                                :href="formatGitUrl(sonarDetail.gitUrl)"
                                target="_blank"
                                class="info-text git-link"
                                :title="sonarDetail.gitUrl"
                            >
                                {{ sonarDetail.gitUrl }}
                            </a>
                        </div>
                    </div>
                </Panel>

                <!-- 代码增量问题列表 -->
                <Panel header="问题列表" class="data-panel mb-4" toggleable>
                    <DataTable
                        :value="issuesList"
                        :rows="issuesParams.pageSize"
                        :totalRecords="issuesTotal"
                        :rowsPerPageOptions="[10, 20, 50]"
                        lazy
                        paginator
                        stripedRows
                        showGridlines
                        rowHover
                        :loading="issuesLoading"
                        @page="onIssuesPageChange($event)"
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
                        currentPageReportTemplate="{first} - {last} 共 {totalRecords} 条记录"
                        responsiveLayout="stack"
                        breakpoint="768px"
                        class="p-datatable-sm table-container"
                    >
                        <Column field="ruleName" header="规范名称" sortable style="width: 15%"></Column>
                        <Column field="issueMessage" header="问题描述" style="width: 30%">
                            <template #body="slotProps">
                                <div
                                    :title="slotProps.data.issueMessage"
                                    class="leading-6 break-words text-red-600 issue-message"
                                >
                                    {{ slotProps.data.issueMessage }}
                                </div>
                            </template>
                        </Column>
                        <Column field="severity" header="问题级别" sortable style="width: 12%">
                            <template #body="slotProps">
                                <Tag
                                    :severity="getSeverityType(slotProps.data.severity)"
                                    :value="getSeverityName(slotProps.data.severity)"
                                    class="custom-tag"
                                    :class="getSeverityType(slotProps.data.severity) + '-tag'"
                                />
                            </template>
                        </Column>
                        <Column field="issueType" header="问题类型" sortable style="width: 12%">
                            <template #body="slotProps">
                                <Tag
                                    :severity="getIssueTypeTag(slotProps.data.issueType)"
                                    :value="getIssueTypeName(slotProps.data.issueType)"
                                    class="custom-tag"
                                    :class="getIssueTypeTag(slotProps.data.issueType) + '-tag'"
                                />
                            </template>
                        </Column>
                        <Column field="issueStatus" header="问题状态" sortable style="width: 10%">
                            <template #body="slotProps">
                                <span>{{ slotProps.data.issueStatus === 'OPEN' ? '未解决' : '已解决' }}</span>
                            </template>
                        </Column>
                        <Column header="操作" style="width: 8%">
                            <template #body="slotProps">
                                <Button
                                    v-if="slotProps.data.issueLink"
                                    icon="pi pi-external-link"
                                    text
                                    label="链接"
                                    @click="openIssueLink(slotProps.data.issueLink)"
                                    :pt="{
                                        root: {
                                            style: {
                                                padding: '0',
                                            },
                                        },
                                    }"
                                />
                                <span v-else>-</span>
                            </template>
                        </Column>

                        <template #empty>
                            <div class="empty-state">
                                <i class="pi pi-search empty-icon"></i>
                                <p>没有找到代码增量问题</p>
                            </div>
                        </template>
                        <template #loading>
                            <div class="loading-state">
                                <i class="pi pi-spin pi-spinner loading-icon"></i>
                                <p>加载中...</p>
                            </div>
                        </template>
                    </DataTable>
                </Panel>

                <!-- 问题统计信息 -->
                <Panel header="问题统计" class="data-panel mb-4" toggleable>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 严重级别统计 - 改为柱状图 -->
                        <div>
                            <h3 class="chart-title">严重性分布</h3>
                            <p class="chart-description">
                                展示不同严重程度问题的数量分布，阻断问题优先级最高，需要立即修复
                            </p>
                            <Chart
                                type="bar"
                                :data="severityBarData"
                                :options="barChartOptions"
                                class="severity-chart"
                            />
                            <div class="chart-descriptions mt-2">
                                <div class="type-desc-item">
                                    <div class="type-desc-icon blocker"><i class="pi pi-ban"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            阻断问题 ({{ sonarDetail?.incrBlockerIssue || 0 }})
                                        </div>
                                        <div class="type-desc-text">
                                            最高优先级问题，可能导致严重错误或系统崩溃，必须立即修复
                                        </div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon critical">
                                        <i class="pi pi-exclamation-triangle"></i>
                                    </div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            严重问题 ({{ sonarDetail?.incrCriticalIssue || 0 }})
                                        </div>
                                        <div class="type-desc-text">高优先级问题，可能导致程序错误或性能严重下降</div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon major"><i class="pi pi-exclamation-circle"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            主要问题 ({{ sonarDetail?.incrMajorIssue || 0 }})
                                        </div>
                                        <div class="type-desc-text">
                                            中等优先级问题，对代码质量有明显影响，应尽快修复
                                        </div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon minor"><i class="pi pi-info-circle"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            次要问题 ({{ sonarDetail?.incrMinorIssue || 0 }})
                                        </div>
                                        <div class="type-desc-text">
                                            低优先级问题，对功能影响不大，但会影响代码可维护性
                                        </div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon info"><i class="pi pi-comment"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            提示问题 ({{ sonarDetail?.incrInfoIssue || 0 }})
                                        </div>
                                        <div class="type-desc-text">信息性问题，主要是代码风格或格式方面的建议</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 问题类型统计 -->
                        <div>
                            <h3 class="chart-title">类型分布</h3>
                            <p class="chart-description">
                                展示不同类别问题的占比，包括代码可靠性问题、安全漏洞和代码异味
                            </p>
                            <Chart
                                type="doughnut"
                                :data="typeChartData"
                                :options="doughnutChartOptions"
                                class="type-chart"
                            />
                            <div class="type-descriptions mt-2">
                                <div class="type-desc-item">
                                    <div class="type-desc-icon bug"><i class="pi pi-exclamation-circle"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">Bugs ({{ sonarDetail?.incrBugs || 0 }})</div>
                                        <div class="type-desc-text">可能导致程序错误或异常行为的代码</div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon vuln"><i class="pi pi-shield"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            漏洞 ({{ sonarDetail?.incrVulnerabilities || 0 }})
                                        </div>
                                        <div class="type-desc-text">可能被利用的安全风险点，如注入攻击风险</div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon smell"><i class="pi pi-code"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">
                                            代码坏味道 ({{ sonarDetail?.incrCodeSmells || 0 }})
                                        </div>
                                        <div class="type-desc-text">不影响功能但降低可维护性的设计或实现问题</div>
                                    </div>
                                </div>
                                <div class="type-desc-item">
                                    <div class="type-desc-icon owasp"><i class="pi pi-exclamation-circle"></i></div>
                                    <div class="type-desc-content">
                                        <div class="type-desc-title">OWASP漏洞 ({{ sonarDetail?.incrOwasp || 0 }})</div>
                                        <div class="type-desc-text">可能导致数据泄露、未授权访问或服务中断的安全风险</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Panel>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapState } from 'pinia';
import useSonarStore from '@/app/stores/sonar';
import { copyToClipboard } from '@/app/utils';
import Loading from '@/app/components/Loading/index.vue';
import {
    SEVERITY,
    SEVERITY_NAMES,
    ISSUE_TYPE,
    ISSUE_TYPE_NAMES,
    getSeverityTagStyle,
    getIssueTypeTagStyle,
    getIssueTypeName,
    getSeverityName,
} from '@/app/constants/sonar';

export default {
    name: 'SonarDetail',
    components: {
        Loading,
    },
    data() {
        return {
            loading: true,
            chartOptions: {
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                },
                responsive: true,
                maintainAspectRatio: false,
            },
            // 饼图配置
            pieChartOptions: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const dataset = context.dataset;
                                const total = dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}个 (${percentage}%)`;
                            },
                        },
                        titleFont: {
                            size: 12,
                        },
                        bodyFont: {
                            size: 11,
                        },
                        padding: 8,
                    },
                },
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
            },
            // 柱状图配置
            barChartOptions: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                return `数量: ${context.raw}`;
                            },
                        },
                        titleFont: {
                            size: 14,
                        },
                        bodyFont: {
                            size: 14,
                        },
                        padding: 8,
                    },
                },
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                        },
                        ticks: {
                            precision: 0,
                            font: {
                                size: 12,
                            },
                        },
                    },
                    x: {
                        grid: {
                            display: false,
                        },
                        ticks: {
                            font: {
                                size: 12,
                            },
                        },
                    },
                },
                barPercentage: 0.5,
                categoryPercentage: 0.7,
            },
            // 环形图配置
            doughnutChartOptions: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const dataset = context.dataset;
                                const total = dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value}个 (${percentage}%)`;
                            },
                        },
                        titleFont: {
                            size: 12,
                        },
                        bodyFont: {
                            size: 11,
                        },
                        padding: 8,
                    },
                },
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
            },
            issuesParams: {
                projectExecutionId: '',
                pageIndex: 1,
                pageSize: 10,
            },
            SEVERITY,
            SEVERITY_NAMES,
            ISSUE_TYPE,
            ISSUE_TYPE_NAMES,
        };
    },
    computed: {
        ...mapState(useSonarStore, ['sonarDetail', 'issuesList', 'issuesTotal', 'issuesLoading']),

        // 问题严重程度饼图数据
        severityChartData() {
            if (!this.sonarDetail) return null;

            return {
                labels: [
                    SEVERITY_NAMES[SEVERITY.BLOCKER],
                    SEVERITY_NAMES[SEVERITY.CRITICAL],
                    SEVERITY_NAMES[SEVERITY.MAJOR],
                    SEVERITY_NAMES[SEVERITY.MINOR],
                    SEVERITY_NAMES[SEVERITY.INFO],
                ],
                datasets: [
                    {
                        data: [
                            this.sonarDetail.incrBlockerIssue || 0,
                            this.sonarDetail.incrCriticalIssue || 0,
                            this.sonarDetail.incrMajorIssue || 0,
                            this.sonarDetail.incrMinorIssue || 0,
                            this.sonarDetail.incrInfoIssue || 0,
                        ],
                        backgroundColor: [
                            '#FF4444', // 阻断问题 - 红色
                            '#FF8800', // 严重问题 - 橙色
                            '#FFBB33', // 主要问题 - 黄色
                            '#00C851', // 次要问题 - 绿色
                            '#33B5E5', // 提示问题 - 蓝色
                        ],
                        hoverBackgroundColor: [
                            '#E03131', // 阻断问题 - 深红色
                            '#E67700', // 严重问题 - 深橙色
                            '#E6A700', // 主要问题 - 深黄色
                            '#00A844', // 次要问题 - 深绿色
                            '#0095C8', // 提示问题 - 深蓝色
                        ],
                    },
                ],
            };
        },

        // 问题类型饼图数据
        typeChartData() {
            if (!this.sonarDetail) return null;

            return {
                labels: [
                    ISSUE_TYPE_NAMES[ISSUE_TYPE.RELIABILITY],
                    ISSUE_TYPE_NAMES[ISSUE_TYPE.VULNERABILITY],
                    ISSUE_TYPE_NAMES[ISSUE_TYPE.CODE_SMELL],
                    ISSUE_TYPE_NAMES[ISSUE_TYPE.OWASP],
                ],
                datasets: [
                    {
                        data: [
                            this.sonarDetail.incrBugs || 0,
                            this.sonarDetail.incrVulnerabilities || 0,
                            this.sonarDetail.incrCodeSmells || 0,
                            this.sonarDetail.incrOwasp || 0,
                        ],
                        backgroundColor: [
                            '#F44336', // Bugs - 红色
                            '#FF9800', // 漏洞 - 橙色
                            '#2196F3', // 代码坏味道 - 蓝色
                            '#9C27B0', // OWASP漏洞 - 紫色
                        ],
                        hoverBackgroundColor: [
                            '#D32F2F', // Bugs - 深红色
                            '#E65100', // 漏洞 - 深橙色
                            '#1565C0', // 代码坏味道 - 深蓝色
                            '#7B1FA2', // OWASP漏洞 - 深紫色
                        ],
                    },
                ],
            };
        },

        // 问题严重程度柱状图数据
        severityBarData() {
            if (!this.sonarDetail) return null;

            return {
                labels: [
                    SEVERITY_NAMES[SEVERITY.BLOCKER],
                    SEVERITY_NAMES[SEVERITY.CRITICAL],
                    SEVERITY_NAMES[SEVERITY.MAJOR],
                    SEVERITY_NAMES[SEVERITY.MINOR],
                    SEVERITY_NAMES[SEVERITY.INFO],
                ],
                datasets: [
                    {
                        label: '问题数量',
                        data: [
                            this.sonarDetail.incrBlockerIssue || 0,
                            this.sonarDetail.incrCriticalIssue || 0,
                            this.sonarDetail.incrMajorIssue || 0,
                            this.sonarDetail.incrMinorIssue || 0,
                            this.sonarDetail.incrInfoIssue || 0,
                        ],
                        backgroundColor: [
                            '#FF4444', // 阻断问题 - 红色
                            '#FF8800', // 严重问题 - 橙色
                            '#FFBB33', // 主要问题 - 黄色
                            '#00C851', // 次要问题 - 绿色
                            '#33B5E5', // 提示问题 - 蓝色
                        ],
                        borderRadius: 6,
                        maxBarThickness: 40,
                    },
                ],
            };
        },
    },
    methods: {
        ...mapActions(useSonarStore, ['getSonarDetail', 'getSonarIssues']),

        // 跳转到 /sonar 页面
        goSonar() {
            this.$router.push('/sonar');
        },

        // 计算百分比
        calculatePercentage(value, dataArray) {
            const total = dataArray.reduce((acc, curr) => acc + curr, 0);
            if (total === 0) return '0.0';
            return ((value / total) * 100).toFixed(1);
        },

        // 复制 Commit SHA
        copyCommitSha(sha) {
            copyToClipboard(sha);
            this.$toast.add({ severity: 'success', summary: '成功', detail: 'Commit SHA 已复制到剪贴板', life: 3000 });
        },

        // 返回上一页
        goBack() {
            this.$router.push('/sonar');
        },

        // 加载详情数据
        async loadSonarDetail() {
            this.loading = true;
            try {
                const id = this.$route.query.id;
                if (!id) {
                    this.$toast.add({ severity: 'error', summary: '错误', detail: '缺少必要的扫描ID参数', life: 3000 });
                    return;
                }

                await this.getSonarDetail(id);
                // 设置问题列表查询参数
                this.issuesParams.projectExecutionId = id;
            } catch (error) {
                console.error('获取Sonar详情失败:', error);
                this.$toast.add({ severity: 'error', summary: '错误', detail: '获取Sonar详情数据失败', life: 3000 });
            } finally {
                this.loading = false;
            }
        },

        // 获取问题级别类型
        getSeverityType(severity) {
            return getSeverityTagStyle(severity);
        },

        getSeverityName(severity) {
            return getSeverityName(severity);
        },

        // 获取问题类型标签
        getIssueTypeTag(issueType) {
            return getIssueTypeTagStyle(issueType);
        },

        // 获取问题类型名称
        getIssueTypeName(issueType) {
            return getIssueTypeName(issueType);
        },

        // 格式化Git URL确保可以正确跳转
        formatGitUrl(url) {
            if (!url) return '#';
            // 如果不是以http或https开头，则添加https前缀
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return 'https://' + url;
            }
            return url;
        },

        // 加载代码增量问题列表
        async loadIssues() {
            try {
                if (!this.issuesParams.projectExecutionId) {
                    return;
                }
                await this.getSonarIssues(this.issuesParams);
            } catch (error) {
                console.error('获取代码增量问题失败:', error);
                this.$toast.add({ severity: 'error', summary: '错误', detail: '获取代码增量问题数据失败', life: 3000 });
            }
        },

        // 处理分页变化
        onIssuesPageChange(event) {
            this.issuesParams.pageIndex = event.page + 1;
            this.issuesParams.pageSize = event.rows;
            this.loadIssues();
        },

        // 打开问题链接
        openIssueLink(link) {
            if (link) {
                window.open(link, '_blank');
            }
        },
    },
    async mounted() {
        await this.loadSonarDetail();
        this.loadIssues();
    },
};
</script>

<style scoped>
.sonar-detail-container {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    padding: 24px 0;
    max-width: 1400px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eef2f7;
}

.logo-title-container {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
}

.sonar-logo {
    height: 2.5rem;
    width: auto;
    cursor: pointer;
}

.page-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.gradient-title {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    letter-spacing: 0.5px;
    margin: 0;
    padding: 0;
    display: inline-block;
    position: relative;
}

.gradient-title::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    bottom: -5px;
    left: 0;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    border-radius: 3px;
}

/* 信息图标与标签样式 */
.info-icon {
    color: #6366f1;
    font-size: 1rem;
}

.form-label {
    font-weight: 500;
    color: #4b5563;
}

/* 优化后的基本信息样式 */
.basic-info-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.info-pill {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background-color: #f8fafc;
    border-radius: 40px;
    transition: all 0.2s ease;
    border: 1px solid #eef2f7;
    max-width: fit-content;
    cursor: default;
}

.info-pill:hover {
    background-color: #f1f5f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.info-text {
    font-size: 1rem;
    color: #334155;
    word-break: break-all;
}

.commit-link {
    cursor: pointer;
    color: #6366f1;
}

.git-link {
    color: #6366f1;
    text-decoration: none;
}

.git-link:hover {
    text-decoration: underline;
}

/* 面板样式 */
.data-panel {
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

:deep(.p-panel) {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

:deep(.p-panel-header) {
    padding: 1rem 1.5rem;
    background: linear-gradient(to right, #f8f9fa, #eef2ff);
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

:deep(.p-panel-content) {
    padding: 2rem;
    background-color: #fff;
}

:deep(.p-panel-title) {
    font-weight: 600;
    font-size: 16px;
    color: #334155;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* 表格样式 */
.table-container {
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
}

:deep(.p-datatable-header) {
    background-color: #f8fafc;
    border: none;
    padding: 1rem 1.5rem;
}

:deep(.p-datatable-thead > tr > th) {
    background-color: #f1f5f9;
    color: #334155;
    font-weight: 600;
    padding: 1rem 1rem;
    border-color: #e2e8f0;
}

:deep(.p-datatable-tbody > tr) {
    transition: all 0.2s ease;
}

:deep(.p-datatable-tbody > tr:hover) {
    background-color: #f8fafc;
}

:deep(.p-datatable-tbody > tr > td) {
    padding: 1rem 1rem;
    border-color: #e2e8f0;
}

:deep(.p-paginator) {
    padding: 1rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* 自定义标签样式 */
.custom-tag {
    border-radius: 6px;
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.custom-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.primary-tag {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #9c27b0 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

.blocker-tag,
.danger-tag {
    background-color: rgba(255, 68, 68, 0.15) !important;
    color: #ff4444 !important;
    border: 1px solid rgba(255, 68, 68, 0.3) !important;
}

.critical-tag,
.warning-tag {
    background-color: rgba(255, 136, 0, 0.15) !important;
    color: #ff8800 !important;
    border: 1px solid rgba(255, 136, 0, 0.3) !important;
}

.major-tag,
.info-tag {
    background-color: rgba(255, 187, 51, 0.15) !important;
    color: #ffbb33 !important;
    border: 1px solid rgba(255, 187, 51, 0.3) !important;
}

.minor-tag,
.success-tag {
    background-color: rgba(0, 200, 81, 0.15) !important;
    color: #00c851 !important;
    border: 1px solid rgba(0, 200, 81, 0.3) !important;
}

.info-tag,
.secondary-tag {
    background-color: rgba(51, 181, 229, 0.15) !important;
    color: #33b5e5 !important;
    border: 1px solid rgba(51, 181, 229, 0.3) !important;
}

/* 问题类型颜色 */
.type-desc-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: white;
}

.type-desc-icon.blocker {
    background-color: #ff4444;
}
.type-desc-icon.critical {
    background-color: #ff8800;
}
.type-desc-icon.major {
    background-color: #ffbb33;
}
.type-desc-icon.minor {
    background-color: #00c851;
}
.type-desc-icon.info {
    background-color: #33b5e5;
}
.type-desc-icon.bug {
    background-color: #f44336;
}
.type-desc-icon.vuln {
    background-color: #ff9800;
}
.type-desc-icon.smell {
    background-color: #2196f3;
}
.type-desc-icon.owasp {
    background-color: #9c27b0;
}

/* 描述项样式 */
.type-desc-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.type-desc-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.type-desc-content {
    flex: 1;
    min-width: 0;
}

.type-desc-title {
    font-weight: 500;
    font-size: 1rem;
    color: #334155;
    margin-bottom: 0.25rem;
}

.type-desc-text {
    font-size: 0.875rem;
    color: #6b7280;
}

.type-descriptions,
.chart-descriptions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    background-color: #f9fafb;
    margin-top: 0.5rem;
}

/* 图表样式 */
:deep(.p-chart) {
    height: 180px;
}

.chart-card {
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-title {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
    position: relative;
    padding-bottom: 0.5rem;
}

.chart-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    border-radius: 2px;
}

.chart-container {
    position: relative;
    min-height: 200px;
    margin-bottom: 1rem;
}

.chart-description {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.severity-chart,
.type-chart {
    height: 180px !important;
}

/* 图表说明部分 */
.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: #f8fafc;
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    padding: 0.25rem 0.35rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.legend-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    margin-right: 6px;
}

.legend-label {
    flex: 1;
    font-size: 1rem;
    color: #4b5563;
}

.legend-value {
    font-weight: 500;
    color: #1f2937;
    margin-right: 6px;
    font-size: 1rem;
}

.legend-percent {
    font-size: 0.7rem;
    color: #6b7280;
    background-color: rgba(99, 102, 241, 0.1);
    padding: 0.1rem 0.3rem;
    border-radius: 10px;
    font-weight: 500;
    min-width: 38px;
    text-align: center;
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #fff;
}

.empty-icon,
.loading-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.issue-message {
    max-width: 440px;
    word-break: break-all;
}
</style>
