<template>
    <div class="w-full h-full overflow-y-auto" style="background-color: #f5f7fa">
        <div class="sonar-container">
            <!-- 顶部标题区域，添加logo和优化标题 -->
            <div class="page-header">
                <div class="logo-title-container">
                    <img src="@/app/assets/sonarqube.svg" alt="Sonar Logo" class="sonar-logo" />
                    <h1 class="page-title gradient-title">扫描记录</h1>
                </div>
            </div>

            <!-- 搜索表单 -->
            <Panel header="搜索条件" toggleable class="search-panel mb-4" :toggleableHeader="true">
                <div class="flex flex-wrap gap-4 search-form">
                    <div class="flex flex-1 min-w-[200px] items-center gap-2">
                        <label for="startTime" class="form-label whitespace-nowrap w-20">开始时间:</label>
                        <Calendar
                            id="startTime"
                            v-model="searchParams.startTime"
                            showIcon
                            showTime
                            showSeconds
                            hourFormat="24"
                            class="w-full"
                            @date-select="debouncedSearch"
                            @hide="debouncedSearch"
                        />
                    </div>
                    <div class="flex flex-1 min-w-[200px] items-center gap-2">
                        <label for="endTime" class="form-label whitespace-nowrap w-20">结束时间:</label>
                        <Calendar
                            id="endTime"
                            v-model="searchParams.endTime"
                            showIcon
                            showTime
                            showSeconds
                            hourFormat="24"
                            class="w-full"
                            @date-select="debouncedSearch"
                            @hide="debouncedSearch"
                        />
                    </div>
                    <div class="flex flex-1 min-w-[200px] items-center gap-2">
                        <label for="gitUrl" class="form-label whitespace-nowrap w-20">Git地址:</label>
                        <AutoComplete
                            id="gitUrl"
                            v-model="searchParams.gitUrl"
                            :suggestions="gitUrls"
                            @complete="searchGitUrls"
                            @item-select="debouncedSearch"
                            @change="debouncedSearch"
                            delay="400"
                            class="w-full"
                        />
                    </div>
                    <div class="flex flex-1 min-w-[200px] items-center gap-2">
                        <label for="accountName" class="form-label whitespace-nowrap w-20">提交人:</label>
                        <AutoComplete
                            id="accountName"
                            v-model="searchParams.accountName"
                            :suggestions="accounts"
                            :optionLabel="(option) => `${option.employeeName}(${option.accountName})`"
                            @complete="searchAccounts"
                            @item-select="handleAccountSelect"
                            @change="handleAccountChange"
                            delay="400"
                            class="w-full"
                        />
                    </div>
                </div>
                <div class="flex justify-end mt-4">
                    <Button
                        label="搜索"
                        icon="pi pi-search"
                        class="search-button mr-4"
                        size="small"
                        @click="loadScanRecords"
                    />
                    <Button
                        label="重置"
                        icon="pi pi-refresh"
                        severity="secondary"
                        class="reset-button"
                        size="small"
                        @click="resetSearch"
                    />
                </div>
            </Panel>

            <!-- 数据表格 -->
            <Panel header="扫描记录列表" class="data-panel">
                <DataTable
                    :value="scanRecords"
                    :rows="searchParams.pageSize"
                    :totalRecords="totalRecords"
                    :rowsPerPageOptions="[10, 20, 50]"
                    :loading="tableLoading"
                    @page="onPageChange($event)"
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown CurrentPageReport"
                    currentPageReportTemplate="{first} - {last} 共 {totalRecords} 条记录"
                    responsiveLayout="stack"
                    breakpoint="768px"
                    stripedRows
                    showGridlines
                    lazy
                    paginator
                    rowHover
                    class="p-datatable-sm table-container"
                >
                    <Column field="projectName" header="项目名称" sortable style="width: 15%"></Column>
                    <Column field="branchName" header="分支名称" sortable style="width: 12%">
                        <template #body="slotProps">
                            <div class="branch-name-container" :title="slotProps.data.branchName">
                                {{ slotProps.data.branchName }}
                            </div>
                        </template>
                    </Column>
                    <Column field="gitUrl" header="Git地址" style="width: 20%">
                        <template #body="slotProps">
                            <div class="git-url-container" :title="slotProps.data.gitUrl">
                                <a
                                    v-if="slotProps.data.gitUrl"
                                    :href="formatGitUrl(slotProps.data.gitUrl)"
                                    target="_blank"
                                    class="git-link"
                                >
                                    {{ slotProps.data.gitUrl }}
                                </a>
                                <span v-else>-</span>
                            </div>
                        </template>
                    </Column>
                    <Column field="commitSha" header="Commit SHA" style="width: 10%">
                        <template #body="slotProps">
                            <span
                                v-if="slotProps.data.commitSha"
                                class="commit-sha"
                                :title="'点击复制: ' + slotProps.data.commitSha"
                                @click="copyCommitSha(slotProps.data.commitSha)"
                            >
                                {{ slotProps.data.commitSha.substring(0, 8) }}
                            </span>
                        </template>
                    </Column>
                    <Column header="问题级别" style="width: 18%">
                        <template #body="slotProps">
                            <div class="flex flex-wrap gap-2">
                                <Tag
                                    v-if="slotProps.data.incrBlockerIssue"
                                    severity="danger"
                                    class="custom-tag blocker-tag"
                                    :value="`${SEVERITY_NAMES.BLOCKER}: ${slotProps.data.incrBlockerIssue}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrCriticalIssue"
                                    severity="warning"
                                    class="custom-tag critical-tag"
                                    :value="`${SEVERITY_NAMES.CRITICAL}: ${slotProps.data.incrCriticalIssue}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrMajorIssue"
                                    severity="info"
                                    class="custom-tag major-tag"
                                    :value="`${SEVERITY_NAMES.MAJOR}: ${slotProps.data.incrMajorIssue}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrMinorIssue"
                                    severity="success"
                                    class="custom-tag minor-tag"
                                    :value="`${SEVERITY_NAMES.MINOR}: ${slotProps.data.incrMinorIssue}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrInfoIssue"
                                    severity="secondary"
                                    class="custom-tag info-tag"
                                    :value="`${SEVERITY_NAMES.INFO}: ${slotProps.data.incrInfoIssue}`"
                                />
                            </div>
                        </template>
                    </Column>
                    <Column header="问题类型" style="width: 18%">
                        <template #body="slotProps">
                            <div class="flex flex-wrap gap-2">
                                <Tag
                                    v-if="slotProps.data.incrCodeSmells"
                                    severity="info"
                                    class="custom-tag smell-tag"
                                    :value="`${ISSUE_TYPE_NAMES[1]}: ${slotProps.data.incrCodeSmells}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrBugs"
                                    severity="danger"
                                    class="custom-tag bug-tag"
                                    :value="`${ISSUE_TYPE_NAMES[2]}: ${slotProps.data.incrBugs}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrVulnerabilities"
                                    severity="warning"
                                    class="custom-tag vuln-tag"
                                    :value="`${ISSUE_TYPE_NAMES[3]}: ${slotProps.data.incrVulnerabilities}`"
                                />
                                <Tag
                                    v-if="slotProps.data.incrOwasp"
                                    severity="primary"
                                    class="custom-tag owasp-tag"
                                    :value="`${ISSUE_TYPE_NAMES[4]}: ${slotProps.data.incrOwasp}`"
                                />
                            </div>
                        </template>
                    </Column>
                    <Column header="操作" style="width: 13%">
                        <template #body="slotProps">
                            <Button
                                icon="pi pi-external-link"
                                text
                                label="详情"
                                @click="viewDetail(slotProps.data)"
                                :pt="{
                                    root: {
                                        style: {
                                            padding: '0',
                                        },
                                    },
                                }"
                            />
                        </template>
                    </Column>

                    <template #empty>
                        <div class="empty-state">
                            <i class="pi pi-search empty-icon"></i>
                            <p>没有找到匹配的扫描记录</p>
                        </div>
                    </template>
                    <template #loading>
                        <div class="gentle-loading-overlay" v-if="tableLoading">
                            <div class="loading-spinner-container">
                                <i class="pi pi-spin pi-spinner loading-icon"></i>
                                <span class="loading-text">加载中...</span>
                            </div>
                        </div>
                    </template>
                </DataTable>
            </Panel>
        </div>
    </div>
</template>

<script>
import useSonarStore from '@/app/stores/sonar';
import { mapActions, mapWritableState } from 'pinia';
import { formatDate, copyToClipboard } from '@/app/utils';
import Panel from 'primevue/panel';
import { SEVERITY_NAMES, ISSUE_TYPE_NAMES } from '@/app/constants/sonar';

export default {
    name: 'Sonar',
    components: {
        Panel,
    },
    data() {
        return {
            searchParams: {
                startTime: this.getDefaultStartDate(),
                endTime: new Date(),
                gitUrl: '',
                accountName: '',
                pageIndex: 1,
                pageSize: 10,
            },
            SEVERITY_NAMES,
            ISSUE_TYPE_NAMES,
            searchTimeout: null,
            tableLoading: false,
            loadingDelay: null,
        };
    },
    computed: {
        ...mapWritableState(useSonarStore, ['scanRecords', 'totalRecords', 'loading', 'gitUrls', 'accounts']),
    },
    methods: {
        ...mapActions(useSonarStore, ['getSonarMessage', 'getGitUrls', 'getAccounts']),

        // 防抖函数，避免频繁触发搜索
        debouncedSearch() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.loadScanRecords();
            }, 500);
        },

        // 搜索Git地址
        async searchGitUrls(event) {
            const params = {
                words: event.query,
            };
            await this.getGitUrls(params);
        },

        // 搜索账户名
        async searchAccounts(event) {
            const params = {
                words: event.query,
            };
            await this.getAccounts(params);
        },

        // 复制 Commit SHA
        copyCommitSha(sha) {
            copyToClipboard(sha);
            this.$toast.add({ severity: 'success', summary: '成功', detail: 'Commit SHA 已复制到剪贴板', life: 3000 });
        },

        // 获取默认的开始时间（当前日期前30天）
        getDefaultStartDate() {
            const date = new Date();
            date.setDate(date.getDate() - 30);
            return date;
        },

        // 延迟显示loading状态
        setDelayedLoading(isLoading) {
            if (!isLoading) {
                clearTimeout(this.loadingDelay);
                this.tableLoading = false;
                return;
            }

            clearTimeout(this.loadingDelay);
            this.loadingDelay = setTimeout(() => {
                this.tableLoading = true;
            }, 300);
        },

        // 加载扫描记录
        async loadScanRecords() {
            try {
                this.setDelayedLoading(true);

                const params = {
                    ...this.searchParams,
                    startTime: formatDate(this.searchParams.startTime, 'YYYY-MM-DD HH:mm:ss'),
                    endTime: formatDate(this.searchParams.endTime, 'YYYY-MM-DD HH:mm:ss'),
                };

                const response = await this.getSonarMessage(params);
                if (response && response.result) {
                    this.scanRecords = response.result.list || [];
                    this.totalRecords = response.result.rowcount || 0;
                }

                this.setDelayedLoading(false);
            } catch (error) {
                console.error('获取扫描记录失败:', error);
                const errorMessage = error.response?.data?.message || error.message || '获取扫描记录失败';
                this.$toast.add({ severity: 'error', summary: '错误', detail: errorMessage, life: 3000 });

                this.setDelayedLoading(false);
            }
        },

        // 页面改变事件
        onPageChange(event) {
            this.searchParams.pageIndex = event.page + 1;
            this.searchParams.pageSize = event.rows;
            this.loadScanRecords();
        },

        // 重置搜索条件
        resetSearch() {
            this.searchParams = {
                startTime: this.getDefaultStartDate(),
                endTime: new Date(),
                gitUrl: '',
                accountName: '',
                pageIndex: 1,
                pageSize: 10,
            };
            this.loadScanRecords();
        },

        // 格式化Git URL确保可以正确跳转
        formatGitUrl(url) {
            if (!url) return '#';
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return 'https://' + url;
            }
            return url;
        },

        // 查看详情
        viewDetail(record) {
            this.$router.push({
                path: '/sonar-detail',
                query: { id: record.projectExecutionId },
            });
        },

        // 处理账户选择
        handleAccountSelect(event) {
            this.searchParams.accountName = event.value.accountName;
            this.debouncedSearch();
        },

        // 处理账户变化
        handleAccountChange() {
            this.debouncedSearch();
        },
    },
    mounted() {
        this.loadScanRecords();
    },
};
</script>

<style scoped>
.sonar-container {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    padding: 24px 0;
    max-width: 1400px;
}

.page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eef2f7;
}

.logo-title-container {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
}

.sonar-logo {
    height: 2.5rem;
    width: auto;
}

.page-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.form-label {
    margin-bottom: 0;
    font-weight: 500;
    color: #4b5563;
}

.branch-name-container {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 150px;
}

.git-url-container {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.commit-sha {
    cursor: pointer;
    color: #334155;
}

.commit-sha:hover {
    color: #6366f1;
}

.git-link {
    color: #4361ee;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 250px;
    display: inline-block;
}

.git-link:hover {
    text-decoration: underline;
}

/* 面板样式 */
.search-panel,
.data-panel {
    border: none !important;
    border-radius: 12px !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

:deep(.p-panel) {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

:deep(.p-panel-header) {
    padding: 1rem 1.5rem;
    background: linear-gradient(to right, #f8f9fa, #eef2ff);
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

:deep(.p-panel-content) {
    padding: 1.5rem;
    background-color: #fff;
}

:deep(.p-panel-title) {
    font-weight: 600;
    font-size: 1.1rem;
    color: #334155;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* 页面标题渐变效果 */
.gradient-title {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    letter-spacing: 0.5px;
    margin: 0;
    padding: 0;
    display: inline-block;
    position: relative;
}

.gradient-title::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 3px;
    bottom: -5px;
    left: 0;
    background: linear-gradient(90deg, #3366ff 0%, #7048e8 100%);
    border-radius: 3px;
}

/* 表格样式 */
.table-container {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    width: 100%;
}

:deep(.p-datatable-header) {
    background-color: #f8fafc;
    border: none;
    padding: 1rem 1.5rem;
}

:deep(.p-datatable-thead > tr > th) {
    background-color: #f1f5f9;
    color: #334155;
    font-weight: 600;
    padding: 0.75rem 1rem;
    border-color: #e2e8f0;
}

:deep(.p-datatable-tbody > tr) {
    transition: all 0.2s ease;
}

:deep(.p-datatable-tbody > tr:hover) {
    background-color: #f8fafc;
}

:deep(.p-datatable-tbody > tr > td) {
    padding: 0.75rem 1rem;
    border-color: #e2e8f0;
}

:deep(.p-paginator) {
    padding: 0.75rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* 标签样式 */
.custom-tag {
    border-radius: 6px;
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.custom-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.blocker-tag {
    background-color: rgba(255, 68, 68, 0.15) !important;
    color: #ff4444 !important;
    border: 1px solid rgba(255, 68, 68, 0.3) !important;
}

.critical-tag {
    background-color: rgba(255, 136, 0, 0.15) !important;
    color: #ff8800 !important;
    border: 1px solid rgba(255, 136, 0, 0.3) !important;
}

.major-tag {
    background-color: rgba(255, 187, 51, 0.15) !important;
    color: #ffbb33 !important;
    border: 1px solid rgba(255, 187, 51, 0.3) !important;
}

.minor-tag {
    background-color: rgba(0, 200, 81, 0.15) !important;
    color: #00c851 !important;
    border: 1px solid rgba(0, 200, 81, 0.3) !important;
}

.info-tag {
    background-color: rgba(51, 181, 229, 0.15) !important;
    color: #33b5e5 !important;
    border: 1px solid rgba(51, 181, 229, 0.3) !important;
}

.bug-tag {
    background-color: rgba(244, 67, 54, 0.15) !important;
    color: #f44336 !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
}

.vuln-tag {
    background-color: rgba(255, 152, 0, 0.15) !important;
    color: #ff9800 !important;
    border: 1px solid rgba(255, 152, 0, 0.3) !important;
}

.smell-tag {
    background-color: rgba(33, 150, 243, 0.15) !important;
    color: #2196f3 !important;
    border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

.owasp-tag {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #9c27b0 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #fff;
}

.empty-icon,
.loading-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

/* AutoComplete 样式优化 */
:deep(.p-autocomplete) {
    width: 100%;
}

:deep(.p-autocomplete-input) {
    width: 100%;
}

/* 优化loading效果 */
.gentle-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    backdrop-filter: blur(1px);
    animation: fadeIn 0.3s ease;
    border-radius: 8px;
}

.loading-spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
}

.loading-icon {
    font-size: 1.5rem;
    color: #3366ff;
    margin-bottom: 8px;
}

.loading-text {
    font-size: 0.9rem;
    color: #4b5563;
    font-weight: 500;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>
