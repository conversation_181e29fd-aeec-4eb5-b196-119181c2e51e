<template>
    <Dialog
        v-model:visible="visible"
        modal
        :style="{ width: '800px' }"
        :header="'获取解决方案'"
        :closable="true"
        @hide="closeModal"
    >
        <form @submit.prevent="handleSubmit">
            <!-- 规则名称 -->
            <div class="mb-3">
                <div class="">
                    <label for="ruleName" class="block mb-2 ml-1 text-sm" style="color: #333">规范名称</label>
                    <Textarea id="ruleName" v-model="codeRuleForm.ruleName" rows="4" class="w-full" />
                </div>
            </div>

            <!-- 问题代码 -->
            <div class="mb-3">
                <div class="">
                    <label for="problemCode" class="block mb-2 ml-1 text-sm" style="color: #333">问题代码</label>
                    <Textarea id="problemCode" v-model="codeRuleForm.problemCode" rows="6" class="w-full" />
                </div>
            </div>

            <!-- 问题类型 -->
            <div class="mb-3">
                <div class="">
                    <label for="problemType" class="block mb-2 ml-1 text-sm" style="color: #333">问题类型</label>
                    <Dropdown
                        id="problemType"
                        v-model="codeRuleForm.problemType"
                        :options="codeIssueTypes"
                        class="w-full"
                    />
                </div>
            </div>
        </form>

        <template #footer>
            <div class="flex justify-end gap-2">
                <Button label="取消" class="p-button-secondary mr-4" @click="closeModal" />
                <Button label="询问大模型" icon="pi pi-send" @click="handleSubmit" />
            </div>
        </template>
    </Dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';

// 添加 visible 用于控制对话框显示
const visible = ref(true);

const props = defineProps({
    initialData: {
        type: Object,
        default: () => ({
            ruleName: '',
            problemCode: '',
            problemType: '',
        }),
    },
});

const emit = defineEmits(['close']);

const codeRuleForm = ref({
    ruleName: props.initialData.ruleName,
    problemCode: props.initialData.problemCode,
    problemType: props.initialData.problemType,
});

const codeIssueTypes = ref(['语法错误', '代码规范', '性能优化', '漏洞', '逻辑错误', '兼容性问题']);

const router = useRouter();

const closeModal = () => {
    visible.value = false;
    emit('close');
};

const handleSubmit = () => {
    // 存储表单数据到 localStorage
    const codeData = {
        ruleName: codeRuleForm.value.ruleName,
        problemCode: codeRuleForm.value.problemCode,
        problemType: codeRuleForm.value.problemType,
    };
    localStorage.setItem('codeReviewData', JSON.stringify(codeData));
    localStorage.setItem('shouldAutoSendMessage', 'true'); // 或 'false'，根据具体需求设置

    // 关闭对话框
    closeModal();

    // 跳转到 chat 页面
    router.push('/');
};
</script>
