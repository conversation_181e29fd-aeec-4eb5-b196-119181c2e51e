<template>
    <div class="mermaid-generator-container">
        <div class="mermaid-header">
            <h3 class="flex align-items-center">
                <img
                    class="mermaid-logo"
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-chatgpt/mermaid-logo.png"
                    alt=""
                    srcset=""
                />
                Mermaid 专业图表生成器
            </h3>
        </div>

        <!-- 模式选择 -->
        <div class="mode-selection mb-4">
            <div class="flex align-items-center justify-content-center gap-3 mb-3">
                <Button
                    @click="setMode('generate')"
                    :severity="currentMode === 'generate' ? 'primary' : 'secondary'"
                    :outlined="currentMode !== 'generate'"
                    size="small"
                    icon="pi pi-magic-wand"
                    label="AI智能生成"
                    class="mode-button"
                />
                <Button
                    @click="setMode('render')"
                    :severity="currentMode === 'render' ? 'primary' : 'secondary'"
                    :outlined="currentMode !== 'render'"
                    size="small"
                    icon="pi pi-code"
                    label="在线编辑"
                    class="mode-button"
                />
            </div>
            <div class="text-center mt-2">
                <small class="text-color-white font-bold">
                    <span v-if="currentMode === 'generate'">✨ 描述你的想法，AI为你创建专业图表</span>
                    <span v-if="currentMode === 'render'">💻 直接编辑Mermaid代码，实时预览效果</span>
                </small>
            </div>
        </div>

        <!-- AI生成模式内容 -->
        <div class="mermaid-content" v-if="currentMode === 'generate'">
            <!-- 配置选项 -->
            <div class="config-section mb-4">
                <div class="grid">
                    <div class="col-6 flex align-items-center">
                        <label class="block text-base font-medium flex-shrink-0 mr-4">图表类型：</label>
                        <Dropdown
                            v-model="diagramType"
                            :options="diagramTypes"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择图表类型"
                            class="w-full"
                            @change="onDiagramTypeChange"
                        >
                            <template #option="{ option }">
                                <div class="dropdown-item">
                                    <div class="font-medium">{{ option.label }}</div>
                                    <div class="text-sm text-color-secondary">{{ option.description }}</div>
                                </div>
                            </template>
                        </Dropdown>
                    </div>
                    <div class="col-6 flex align-items-center">
                        <label class="block text-base font-medium flex-shrink-0 mr-4">复杂程度：</label>
                        <Dropdown
                            v-model="complexity"
                            :options="complexityOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择复杂度"
                            class="w-full"
                        >
                            <template #option="{ option }">
                                <div class="dropdown-item">
                                    <div class="font-medium">{{ option.label }}</div>
                                    <div class="text-sm text-color-secondary">{{ option.description }}</div>
                                </div>
                            </template>
                        </Dropdown>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-section mb-3">
                <div class="flex align-items-center justify-content-between mb-2">
                    <label class="font-semibold">📝 描述你的想法：</label>
                </div>
                <Textarea v-model="inputText" :placeholder="getPlaceholder()" rows="10" class="w-full" />
                <div class="input-tips mt-2">
                    <div class="text-sm text-color-secondary">
                        <i class="pi pi-lightbulb mr-1"></i>
                        💡 详细描述流程步骤、决策节点和参与角色，AI将为你创建专业的可视化图表
                    </div>
                </div>
            </div>

            <!-- 生成按钮 -->
            <div class="generate-action mb-5">
                <div class="flex gap-2">
                    <Button
                        @click="generateMermaid"
                        :loading="isGenerating && !streamingResult"
                        :disabled="!canGenerate"
                        icon="pi pi-sparkles"
                        size="small"
                        :label="isGenerating ? '🎨 AI创作中...' : '✨ 生成图表'"
                        class="flex-1"
                    />
                    <Button
                        v-if="isGenerating && streamingResult"
                        @click="cancelGeneration"
                        icon="pi pi-check"
                        severity="contrast"
                        outlined
                        v-tooltip.top="'完成生成'"
                        size="small"
                        label="完成"
                    />
                    <Button
                        v-if="generatedCode && !isGenerating"
                        @click="optimizeMermaid"
                        severity="info"
                        outlined
                        size="small"
                        label="🔧 优化代码"
                        v-tooltip.top="'让AI优化你的代码'"
                    />
                </div>
            </div>

            <!-- 结果区域 -->
            <div class="result-section" v-if="generatedCode || isGenerating || streamingResult">
                <div class="flex align-items-center justify-content-between mb-2">
                    <label class="font-semibold">🎨 生成的图表代码</label>
                    <div class="flex gap-2" v-if="!isGenerating && (generatedCode || streamingResult)">
                        <Button
                            @click="copyMermaidCode"
                            severity="contrast"
                            size="small"
                            label="📋 复制代码"
                            outlined
                            v-tooltip.top="'复制Mermaid代码到剪贴板'"
                        />
                        <Button
                            v-if="generatedCode || streamingResult"
                            @click="switchToRenderMode"
                            severity="contrast"
                            outlined
                            size="small"
                            label="📝 在线编辑"
                            v-tooltip.top="'切换到在线编辑器'"
                        />
                    </div>
                </div>

                <!-- 代码预览 -->
                <div class="code-preview mb-3 p-3 border-1 surface-border border-round">
                    <div v-if="isGenerating && !streamingResult" class="flex align-items-center">
                        <i class="pi pi-spin pi-cog mr-2" style="color: #ff3670"></i>
                        <span>🎨 AI正在为你创作专业图表...</span>
                    </div>
                    <div v-else class="mermaid-code" :class="{ streaming: isGenerating }">
                        <pre><code>{{ streamingResult || generatedCode }}</code></pre>
                        <span v-if="isGenerating" class="cursor-blink"></span>
                    </div>
                </div>
            </div>

            <!-- 错误提示 -->
            <Message v-if="errorMessage" severity="error" :closable="true" @close="errorMessage = ''" class="mt-3">
                {{ errorMessage }}
            </Message>

            <!-- 操作历史 -->
            <div class="history-section mt-4" v-if="generationHistory.length > 0">
                <div class="flex align-items-center justify-content-between mb-2">
                    <label class="font-semibold">📚 创作历史</label>
                    <Button
                        @click="clearHistory"
                        icon="pi pi-trash"
                        size="small"
                        text
                        severity="danger"
                        v-tooltip.top="'清空所有历史记录'"
                    />
                </div>
                <div class="history-list">
                    <div
                        v-for="(item, index) in generationHistory"
                        :key="index"
                        class="history-item p-2 mb-2 border-1 surface-border border-round cursor-pointer"
                        @click="selectHistoryItem(item)"
                    >
                        <div class="flex align-items-center justify-content-between">
                            <div class="flex-1">
                                <div class="font-medium text-sm">{{ item.diagramType }} - {{ item.complexity }}</div>
                                <div class="text-xs text-color-secondary line-height-3">
                                    {{ item.description.substring(0, 80)
                                    }}{{ item.description.length > 80 ? '...' : '' }}
                                </div>
                            </div>
                            <div class="text-xs text-color-secondary">
                                {{ formatDate(item.timestamp) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 直接渲染模式内容 -->
        <div class="render-mode-content" v-if="currentMode === 'render'">
            <MermaidEdit :mermaid-code="selectedRendererCode" :height="700" />
        </div>
    </div>
</template>

<script>
import { MermaidService } from '@/services/langchain/index.js';
import { getAPIConfig } from '../Translator/platforms.js';
import MermaidEdit from '../MermaidEdit/index.vue';

export default {
    name: 'MermaidGenerator',
    components: {
        MermaidEdit,
    },
    data() {
        return {
            // 模式相关
            currentMode: 'generate', // 'generate' | 'render'
            selectedRendererCode: '',

            // 配置相关
            selectedPlatform: 'company',
            selectedModel: 'deepseek-v3',
            diagramType: 'flowchart',
            complexity: 'medium',

            // 选项数据
            diagramTypes: [],
            complexityOptions: [],

            // 输入和结果
            inputText: '',
            generatedCode: '',
            streamingResult: '',
            isGenerating: false,
            errorMessage: '',
            cancelFlag: false,

            // 历史记录
            generationHistory: [],

            // 服务实例
            mermaidService: null,
        };
    },
    computed: {
        canGenerate() {
            const config = getAPIConfig(this.selectedPlatform);
            return !this.isGenerating && this.inputText.trim() && config.apiKey && this.mermaidService?.isReady();
        },
    },
    mounted() {
        this.initializeMermaidService();
        this.loadHistoryFromStorage();
    },
    methods: {
        // 设置模式
        setMode(mode) {
            this.currentMode = mode;
            this.errorMessage = '';

            // 如果从生成模式切换到渲染模式，且有生成的代码，则传递给渲染器
            if (mode === 'render' && this.generatedCode) {
                this.selectedRendererCode = this.generatedCode;
            }
        },

        // 切换到渲染模式
        switchToRenderMode() {
            this.selectedRendererCode = this.generatedCode || this.streamingResult;

            console.log(this.selectedRendererCode);
            this.setMode('render');
        },

        // 初始化 Mermaid 服务
        async initializeMermaidService() {
            try {
                const config = getAPIConfig(this.selectedPlatform);

                if (!config.apiKey) {
                    console.warn(`${config.platform} API密钥未配置`);
                    this.mermaidService = null;
                    return;
                }

                // 创建服务实例
                this.mermaidService = new MermaidService();

                // 获取选项数据
                this.diagramTypes = this.mermaidService.getSupportedDiagramTypes();
                this.complexityOptions = this.mermaidService.getComplexityOptions();

                // 初始化服务
                await this.mermaidService.initialize({
                    model: this.selectedModel,
                    apiKey: config.apiKey,
                    baseURL: config.baseURL,
                    temperature: 0.3,
                });

                console.log(`已初始化 ${config.platform} Mermaid服务，模型: ${this.selectedModel}`);
            } catch (error) {
                console.error('Mermaid服务初始化失败:', error);
                this.errorMessage = `AI流程图服务初始化失败: ${error.message}`;
                this.mermaidService = null;
            }
        },

        // 生成流程图
        async generateMermaid() {
            if (!this.mermaidService?.isReady()) {
                this.errorMessage = 'AI流程图服务未正确初始化，请检查API配置';
                return;
            }

            const params = {
                description: this.inputText,
                diagramType: this.diagramType,
                complexity: this.complexity,
            };

            const validation = this.mermaidService.validateMermaidParams(params);
            if (!validation.isValid) {
                this.errorMessage = validation.errors[0];
                return;
            }

            this.isGenerating = true;
            this.errorMessage = '';
            this.generatedCode = '';
            this.streamingResult = '';
            this.cancelFlag = false;

            try {
                const result = await this.mermaidService.generateMermaid(
                    params,
                    (chunk) => {
                        this.streamingResult += chunk;
                    },
                    () => this.cancelFlag
                );

                this.generatedCode = result;
                this.streamingResult = '';

                // 添加到历史记录
                this.addToHistory(params, result);

                // 渲染预览
                this.$nextTick(() => {
                    this.renderMermaidPreview(result);
                });
            } catch (error) {
                console.error('生成失败:', error);
                this.errorMessage = error.message;
            } finally {
                this.isGenerating = false;
                this.cancelFlag = false;
            }
        },

        // 优化流程图
        async optimizeMermaid() {
            if (!this.generatedCode) return;

            this.isGenerating = true;
            this.errorMessage = '';
            this.streamingResult = '';
            this.cancelFlag = false;

            try {
                const result = await this.mermaidService.optimizeMermaid(
                    this.generatedCode,
                    { requirements: '提高可读性、美观度和结构清晰度' },
                    (chunk) => {
                        this.streamingResult += chunk;
                    },
                    () => this.cancelFlag
                );

                this.generatedCode = result;
                this.streamingResult = '';

                // 重新渲染预览
                this.$nextTick(() => {
                    this.renderMermaidPreview(result);
                });
            } catch (error) {
                console.error('优化失败:', error);
                this.errorMessage = error.message;
            } finally {
                this.isGenerating = false;
                this.cancelFlag = false;
            }
        },

        // 渲染 Mermaid 预览（暂时使用简化版本）
        async renderMermaidPreview(mermaidCode) {
            if (!this.$refs.mermaidPreview || !mermaidCode) return;

            try {
                const element = this.$refs.mermaidPreview;
                element.innerHTML = `<div class="preview-placeholder">
                    <i class="pi pi-sitemap text-4xl mb-2"></i>
                    <p>Mermaid 预览</p>
                    <p class="text-sm text-color-secondary">代码生成完成，预览功能需要安装 mermaid 依赖</p>
                </div>`;
            } catch (error) {
                console.error('Mermaid渲染失败:', error);
                this.$refs.mermaidPreview.innerHTML = `<div class="error-message">图表渲染失败: ${error.message}</div>`;
            }
        },

        // 刷新预览
        refreshPreview() {
            const code = this.streamingResult || this.generatedCode;
            if (code) {
                this.renderMermaidPreview(code);
            }
        },

        // 取消生成
        cancelGeneration() {
            this.cancelFlag = true;
            this.generatedCode = this.streamingResult;
            this.isGenerating = false;
        },

        // 复制 Mermaid 代码
        async copyMermaidCode() {
            const codeToCopy = this.streamingResult || this.generatedCode;
            if (!codeToCopy) return;

            try {
                await navigator.clipboard.writeText(codeToCopy);
                this.$toast?.add({
                    severity: 'success',
                    summary: '已复制到剪贴板',
                    detail: '',
                    life: 2000,
                });
            } catch (error) {
                console.error('复制失败:', error);
                this.errorMessage = '复制失败，请手动复制';
            }
        },

        // 获取占位符文本
        getPlaceholder() {
            const typeMap = {
                flowchart: '例如：用户登录系统的完整流程，包括输入用户名密码、验证、登录成功或失败的处理...',
                sequenceDiagram: '例如：用户注册过程中，前端、后端、数据库之间的交互时序...',
                gantt: '例如：项目开发计划，包括需求分析、设计、开发、测试各阶段的时间安排...',
                erDiagram: '例如：电商系统中用户、订单、商品之间的实体关系...',
            };
            return typeMap[this.diagramType] || '请详细描述您要生成的流程图内容...';
        },

        // 图表类型改变
        onDiagramTypeChange() {
            // 可以根据图表类型调整默认复杂度等
        },

        // 添加到历史记录
        addToHistory(params, result) {
            const historyItem = {
                ...params,
                result,
                timestamp: new Date().toISOString(),
            };

            this.generationHistory.unshift(historyItem);

            // 限制历史记录数量
            if (this.generationHistory.length > 10) {
                this.generationHistory = this.generationHistory.slice(0, 10);
            }

            this.saveHistoryToStorage();
        },

        // 选择历史记录项
        selectHistoryItem(item) {
            this.inputText = item.description;
            this.diagramType = item.diagramType;
            this.complexity = item.complexity;
            this.generatedCode = item.result;

            this.$nextTick(() => {
                this.renderMermaidPreview(item.result);
            });
        },

        // 清空历史
        clearHistory() {
            this.generationHistory = [];
            this.saveHistoryToStorage();
        },

        // 保存历史到本地存储
        saveHistoryToStorage() {
            try {
                localStorage.setItem('mermaid-generation-history', JSON.stringify(this.generationHistory));
            } catch (error) {
                console.error('保存历史记录失败:', error);
            }
        },

        // 从本地存储加载历史
        loadHistoryFromStorage() {
            try {
                const stored = localStorage.getItem('mermaid-generation-history');
                if (stored) {
                    this.generationHistory = JSON.parse(stored);
                }
            } catch (error) {
                console.error('加载历史记录失败:', error);
                this.generationHistory = [];
            }
        },

        // 格式化日期
        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.mermaid-generator-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 1.5rem 1.5rem;
}

.mermaid-header {
    text-align: center;
    margin-bottom: 2rem;

    h3 {
        margin: 0 0 0.5rem 0;
        color: #ff3670;
        justify-content: center;
    }

    .mermaid-logo {
        width: 20px;
        height: 20px;
        margin-right: 10px;
    }
}

.config-section {
    .grid {
        gap: 1rem;
    }

    .dropdown-item {
        padding: 0.5rem 0;
    }
}

.code-preview {
    background-color: #f8f9fa;
    min-height: 80px;

    pre {
        margin: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;

        code {
            background: none;
            border: none;
            padding: 0;
        }
    }

    .mermaid-code {
        &.streaming {
            .cursor-blink {
                display: inline-block;
                width: 3px;
                height: 1.2em;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8, #2563eb);
                vertical-align: middle;
                margin-left: 2px;
                border-radius: 2px;
                box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
                animation: modernBlink 1.2s ease-in-out infinite;
            }
        }
    }
}

.history-section {
    .history-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .history-item {
        transition: all 0.2s ease;

        &:hover {
            background-color: #f8f9fa;
            border-color: #3b82f6;
        }
    }
}

.input-tips {
    margin-top: 0.5rem;
}

@keyframes modernBlink {
    0% {
        opacity: 1;
        transform: scaleY(1);
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    }
    50% {
        opacity: 0.3;
        transform: scaleY(0.8);
        box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
    }
    100% {
        opacity: 1;
        transform: scaleY(1);
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    }
}

.mode-selection {
    background: linear-gradient(135deg, #ff3670, #e91e63);
    border: 1px solid #ff3670;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 0 1.5rem;
    color: white;

    .mode-button {
        min-width: 140px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);

        &:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(255, 54, 112, 0.4);
        }

        &.p-button-primary {
            background: white;
            color: #ff3670;

            &:hover {
                background: #f8f9fa;
                color: #e91e63;
            }
        }

        &.p-button-secondary {
            color: #fff;
        }
    }

    small {
        color: rgba(255, 255, 255, 0.9);
    }
}

.render-mode-content {
    padding: 1.5rem;
}

@media (max-width: 768px) {
    .mermaid-generator-container {
        margin: 1rem;
        padding: 1rem;
    }

    .mode-selection {
        margin: 0 0.5rem;
        padding: 1rem;

        .flex {
            flex-direction: column;
            gap: 0.75rem;
        }

        .mode-button {
            min-width: auto;
            width: 100%;
        }
    }

    .config-section .grid {
        .col-6 {
            width: 100%;
            margin-bottom: 1rem;
        }

        .flex {
            flex-direction: column;
            align-items: flex-start;

            label {
                margin-bottom: 0.5rem;
                margin-right: 0;
            }
        }
    }

    .render-mode-content {
        padding: 0.75rem;
    }
}

.generate-action {
    .p-button {
        background: linear-gradient(135deg, #ff3670, #e91e63);
        border: none;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
            background: linear-gradient(135deg, #e91e63, #d81b60);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(255, 54, 112, 0.4);
        }

        &:disabled {
            background: #ccc;
            transform: none;
            box-shadow: none;
        }
    }

    .p-button-outlined {
        background: transparent;
        border: 2px solid #ff3670;
        color: #ff3670;

        &:hover {
            background: #ff3670;
            color: white;
        }
    }
}
</style>
