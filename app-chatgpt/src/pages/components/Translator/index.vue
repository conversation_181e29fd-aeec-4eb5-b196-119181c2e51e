<template>
    <div class="translator-container">
        <div class="translator-header">
            <h3 class="flex align-items-center">
                <i class="pi pi-language mr-2"></i>
                智能翻译工具
            </h3>
            <p class="text-sm text-color-secondary">支持多语言互译，基于deepseek大语言模型</p>
        </div>

        <div class="translator-content">
            <!-- 平台和模型选择 -->
            <div class="platform-selector mb-3">
                <div class="grid">
                    <!-- 不要删除，保留备用 -->
                    <!-- <div class="col-6">
                        <label class="block text-sm font-medium mb-2">AI平台</label>
                        <Dropdown
                            v-model="selectedPlatform"
                            :options="platformOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择AI平台"
                            class="w-full"
                            @change="onPlatformChange"
                        />
                    </div> -->
                    <div class="col-6 flex align-items-center">
                        <label class="block text-base font-medium flex-shrink-0 mr-4"> 模型选择： </label>
                        <Dropdown
                            v-model="selectedModel"
                            :options="modelOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择模型"
                            class="w-full"
                            @change="onModelChange"
                        />
                    </div>
                </div>
            </div>

            <!-- 语言选择 -->
            <div class="language-selector mb-4 flex align-items-center">
                <label class="block text-base font-medium flex-shrink-0 mr-4"> 语言选择： </label>
                <div class="flex align-items-center gap-3">
                    <Dropdown
                        v-model="sourceLanguage"
                        :options="languages"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="源语言"
                        class="flex-1"
                    />

                    <Button
                        icon="pi pi-arrow-right-arrow-left"
                        @click="swapLanguages"
                        outlined
                        rounded
                        v-tooltip.top="'交换语言'"
                    />

                    <Dropdown
                        v-model="targetLanguage"
                        :options="languages"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="目标语言"
                        class="flex-1"
                    />
                </div>
            </div>

            <!-- 翻译风格和语调控制 -->
            <div class="translation-options mb-4">
                <div class="grid">
                    <div class="col-6 flex align-items-center">
                        <label class="block text-base font-medium flex-shrink-0 mr-4"> 翻译风格： </label>
                        <Dropdown
                            v-model="translationStyle"
                            :options="styleOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择风格"
                            class="w-full"
                        />
                    </div>
                    <div class="col-6 flex align-items-center">
                        <label class="block text-base font-medium flex-shrink-0 mr-4"> 翻译语调： </label>
                        <Dropdown
                            v-model="translationTone"
                            :options="toneOptions"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="选择语调"
                            class="w-full"
                        />
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-section mb-3">
                <div class="flex align-items-center justify-content-between mb-2">
                    <label class="font-semibold">输入文本</label>
                </div>
                <Textarea v-model="inputText" :placeholder="getPlaceholder()" rows="8" class="w-full" />
            </div>

            <!-- 翻译按钮 -->
            <div class="translate-action mb-3">
                <div class="flex gap-2">
                    <Button
                        @click="translateText"
                        :loading="isTranslating && !streamingResult"
                        :disabled="!canTranslate"
                        icon="pi pi-send"
                        :label="isTranslating ? '翻译中...' : '开始翻译'"
                        class="flex-1"
                    />
                    <Button
                        v-if="isTranslating && streamingResult"
                        @click="cancelTranslation"
                        icon="pi pi-times"
                        severity="secondary"
                        outlined
                        v-tooltip.top="'完成翻译'"
                    />
                </div>
            </div>

            <!-- 结果区域 -->
            <div class="result-section" v-if="translatedText || isTranslating || streamingResult">
                <div class="flex align-items-center justify-content-between mb-2">
                    <label class="font-semibold">翻译结果</label>
                    <Button
                        v-if="(translatedText || streamingResult) && !isTranslating"
                        @click="copyResult"
                        severity="contrast"
                        size="small"
                        label="复制"
                        v-tooltip.top="'复制翻译结果'"
                    />
                </div>
                <div class="result-content p-3 border-1 surface-border border-round">
                    <div v-if="isTranslating && !streamingResult" class="flex align-items-center">
                        <i class="pi pi-spinner pi-spin mr-2"></i>
                        <span>正在翻译中...</span>
                    </div>
                    <div v-else class="translated-text" :class="{ streaming: isTranslating }">
                        {{ streamingResult || translatedText }}
                        <span v-if="isTranslating" class="cursor-blink"></span>
                    </div>
                </div>
            </div>

            <!-- 错误提示 -->
            <Message v-if="errorMessage" severity="error" :closable="true" @close="errorMessage = ''" class="mt-3">
                {{ errorMessage }}
            </Message>
        </div>
    </div>
</template>

<script>
import { TranslatorService } from '@/services/langchain/index.js';
import { getAllPlatforms, getPlatformModels, getPlatformInfo, getAPIConfig, isFreeModel } from './platforms.js';

export default {
    name: 'Translator',
    data() {
        return {
            // 平台和模型相关
            selectedPlatform: 'company', // 默认选择硅基流动
            selectedModel: 'deepseek-v3',
            platformOptions: [],
            modelOptions: [],

            // 语言相关
            sourceLanguage: 'auto',
            targetLanguage: 'en',
            languages: [
                { label: '自动检测', value: 'auto' },
                { label: '中文', value: 'zh' },
                { label: 'English', value: 'en' },
                { label: '日本語', value: 'ja' },
                { label: '한국어', value: 'ko' },
                { label: 'Français', value: 'fr' },
                { label: 'Deutsch', value: 'de' },
                { label: 'Español', value: 'es' },
                { label: 'Русский', value: 'ru' },
            ],

            // 翻译风格和语调
            translationStyle: 'formal',
            translationTone: 'neutral',
            styleOptions: [
                { label: '正式', value: 'formal' },
                { label: '口语化', value: 'casual' },
            ],
            toneOptions: [
                { label: '中性', value: 'neutral' },
                { label: '学术', value: 'academic' },
                { label: '营销', value: 'marketing' },
                { label: '友好', value: 'friendly' },
                { label: '专业', value: 'professional' },
            ],

            // 翻译相关
            inputText: '',
            translatedText: '',
            streamingResult: '', // 新增：用于存储流式返回的部分结果
            isTranslating: false,
            errorMessage: '',
            cancelFlag: false, // 新增：用于标记是否取消翻译

            // UI状态
            showConfigHelp: false,

            // 翻译服务
            translatorService: null,
        };
    },
    computed: {
        currentPlatformInfo() {
            return getPlatformInfo(this.selectedPlatform);
        },

        isCurrentModelFree() {
            return isFreeModel(this.selectedPlatform, this.selectedModel);
        },

        apiConfigStatus() {
            const config = getAPIConfig(this.selectedPlatform);

            if (!config.apiKey) {
                return {
                    type: 'error',
                    icon: 'pi pi-exclamation-triangle',
                    message: `请配置 ${config.platform} 的API密钥`,
                };
            }

            return {
                type: 'success',
                icon: 'pi pi-check-circle',
                message: `${config.platform} API 已配置`,
            };
        },

        canTranslate() {
            const config = getAPIConfig(this.selectedPlatform);
            return !this.isTranslating && this.inputText.trim() && config.apiKey && this.translatorService?.isReady();
        },
    },

    mounted() {
        this.initializePlatformOptions();
        this.initializeTranslatorService();
    },

    methods: {
        // 初始化平台选项
        initializePlatformOptions() {
            this.platformOptions = getAllPlatforms();
            this.onPlatformChange();
        },

        // 平台改变时的处理
        onPlatformChange() {
            this.modelOptions = getPlatformModels(this.selectedPlatform);
            if (this.modelOptions.length > 0) {
                this.selectedModel = this.modelOptions[0].value;
            }
            this.initializeTranslatorService();
        },

        // 模型改变时的处理
        onModelChange() {
            this.initializeTranslatorService();
        },

        // 初始化翻译服务
        async initializeTranslatorService() {
            try {
                const config = getAPIConfig(this.selectedPlatform);

                if (!config.apiKey) {
                    console.warn(`${config.platform} API密钥未配置`);
                    this.translatorService = null;
                    return;
                }

                // 创建翻译服务实例
                this.translatorService = new TranslatorService();

                // 初始化服务
                await this.translatorService.initialize({
                    model: this.selectedModel,
                    apiKey: config.apiKey,
                    baseURL: config.baseURL,
                    temperature: 0.1,
                });

                console.log(`已初始化 ${config.platform} 翻译服务，模型: ${this.selectedModel}`);
            } catch (error) {
                console.error('翻译服务初始化失败:', error);
                this.errorMessage = `AI翻译服务初始化失败: ${error.message}`;
                this.translatorService = null;
            }
        },

        // 执行翻译
        async translateText() {
            if (!this.translatorService?.isReady()) {
                this.errorMessage = 'AI翻译服务未正确初始化，请检查API配置';
                return;
            }

            // 验证翻译参数
            const translationParams = {
                text: this.inputText,
                sourceLanguage: this.sourceLanguage,
                targetLanguage: this.targetLanguage,
                style: this.translationStyle,
                tone: this.translationTone,
            };

            const validation = this.translatorService.validateTranslationParams(translationParams);
            if (!validation.isValid) {
                this.errorMessage = validation.errors[0];
                return;
            }

            this.isTranslating = true;
            this.errorMessage = '';
            this.translatedText = '';
            this.streamingResult = '';
            this.cancelFlag = false;

            try {
                // 执行翻译
                const result = await this.translatorService.translate(
                    translationParams,
                    // 流式进度回调
                    (chunk) => {
                        this.streamingResult += chunk;
                    },
                    // 取消检查函数
                    () => this.cancelFlag
                );

                // 翻译完成后，将最终结果保存
                this.translatedText = result;
                this.streamingResult = '';
            } catch (error) {
                console.error('翻译失败:', error);
                this.errorMessage = error.message;
            } finally {
                this.isTranslating = false;
                this.cancelFlag = false;
            }
        },

        // 取消翻译
        cancelTranslation() {
            this.cancelFlag = true;
            this.translatedText = this.streamingResult;
            this.isTranslating = false;
        },

        // 交换语言
        swapLanguages() {
            if (this.sourceLanguage === 'auto') {
                this.sourceLanguage = 'zh';
            }

            const temp = this.sourceLanguage;
            this.sourceLanguage = this.targetLanguage;
            this.targetLanguage = temp;

            // 如果有翻译结果，可以将结果作为新的输入
            if (this.translatedText) {
                this.inputText = this.translatedText;
                this.translatedText = '';
            }
        },

        // 复制结果到剪贴板
        async copyResult() {
            const textToCopy = this.streamingResult || this.translatedText;
            if (!textToCopy) return;

            try {
                await navigator.clipboard.writeText(textToCopy);
                this.$toast?.add({
                    severity: 'success',
                    summary: '已复制到剪贴板',
                    detail: '',
                    life: 2000,
                });
            } catch (error) {
                console.error('复制失败:', error);
                this.errorMessage = '复制失败，请手动复制';
            }
        },

        // 获取占位符文本
        getPlaceholder() {
            const sourceLang = this.languages.find((lang) => lang.value === this.sourceLanguage)?.label || '文本';
            return `请输入要翻译的${sourceLang}文本...`;
        }
    },
};
</script>

<style lang="scss" scoped>
.translator-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1.5rem 1.5rem;
}

.translator-header {
    text-align: center;
    margin-bottom: 2rem;

    h3 {
        margin: 0 0 0.5rem 0;
        color: #2563eb;
        justify-content: center;
    }
}

.platform-selector {
    .grid {
        gap: 1rem;
    }
}

.language-selector {
    .p-dropdown {
        min-width: 120px;
    }
}

.translation-options {
    .grid {
        gap: 1rem;
    }

    .p-dropdown {
        min-width: 100px;
    }
}

.result-content {
    min-height: 60px;
    background-color: #f8f9fa;

    .translated-text {
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;

        &.streaming {
            .cursor-blink {
                display: inline-block;
                width: 3px;
                height: 1.2em;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8, #2563eb);
                vertical-align: middle;
                margin-left: 2px;
                border-radius: 2px;
                box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
                animation: modernBlink 1.2s ease-in-out infinite;
            }
        }
    }
}

@keyframes modernBlink {
    0% {
        opacity: 1;
        transform: scaleY(1);
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    }
    50% {
        opacity: 0.3;
        transform: scaleY(0.8);
        box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
    }
    100% {
        opacity: 1;
        transform: scaleY(1);
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.translate-action {
    text-align: center;
}

.config-help-content {
    pre {
        font-size: 0.875rem;
        overflow-x: auto;
    }

    ul {
        list-style: none;

        li {
            margin-bottom: 1rem;
            line-height: 1.6;

            a {
                color: #2563eb;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .translator-container {
        margin: 1rem;
        padding: 1rem;
    }

    .language-selector .flex {
        flex-direction: column;
        gap: 1rem;

        .p-dropdown {
            width: 100%;
        }
    }

    .platform-selector .grid {
        .col-6 {
            width: 100%;
        }
    }

    .translation-options .grid {
        .col-6 {
            width: 100%;
            margin-bottom: 1rem;
        }

        .flex {
            flex-direction: column;
            align-items: flex-start;

            label {
                margin-bottom: 0.5rem;
                margin-right: 0;
            }
        }
    }
}
</style>
