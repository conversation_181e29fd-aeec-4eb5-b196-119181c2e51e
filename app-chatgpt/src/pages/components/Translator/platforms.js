// 支持的AI平台配置
export const AI_PLATFORMS = {
    // siliconflow: {
    //     name: '硅基流动',
    //     baseURL: 'https://api.siliconflow.cn/v1',
    //     models: [
    //         { value: 'Qwen/Qwen2.5-72B-Instruct', label: 'Qwen2.5-72B' },
    //         { value: 'Pro/deepseek-ai/DeepSeek-R1', label: 'DeepSeek R1 Pro' },
    //         { value: 'deepseek-ai/DeepSeek-R1', label: 'DeepSeek R1' },
    //     ],
    //     envKey: 'VUE_APP_SILICONFLOW_API_KEY',
    //     envBaseURL: 'VUE_APP_SILICONFLOW_BASE_URL',
    //     description: '提供免费的开源大模型API，兼容OpenAI格式',
    // },
    company: {
        name: 'AIDEV',
        baseURL: '/langchain-ai',
        models: [
            { value: 'deepseek-v3', label: 'deepseek-v3' },
            { value: 'deepseek-r1', label: 'deepseek-r1' },
        ],
        envKey: 'VUE_APP_COMPANY_API_KEY',
        envBaseURL: 'VUE_APP_COMPANY_BASE_URL',
        description: '公司内部代理的AI模型，通过本地代理访问',
        defaultApiKey: 'company-internal-proxy', // 公司内部代理不需要真实的API密钥
    },
};

// 获取平台信息
export const getPlatformInfo = (platformKey) => {
    return AI_PLATFORMS[platformKey] || AI_PLATFORMS.openai;
};

// 获取所有平台选项
export const getAllPlatforms = () => {
    return Object.keys(AI_PLATFORMS).map((key) => ({
        value: key,
        label: AI_PLATFORMS[key].name,
        description: AI_PLATFORMS[key].description || '',
    }));
};

// 获取平台的模型列表
export const getPlatformModels = (platformKey) => {
    const platform = AI_PLATFORMS[platformKey];
    return platform ? platform.models : [];
};

// 检查是否为免费模型
export const isFreeModel = (platformKey, modelValue) => {
    const platform = AI_PLATFORMS[platformKey];
    return platform?.freeModels?.includes(modelValue) || false;
};

// 获取API配置
export const getAPIConfig = (platformKey) => {
    const platform = getPlatformInfo(platformKey);
    const apiKey = process.env[platform.envKey] || platform.defaultApiKey;
    let baseURL = process.env[platform.envBaseURL] || platform.baseURL;

    // 处理相对路径，转换为完整URL
    if (baseURL && baseURL.startsWith('/')) {
        // 获取当前页面的origin（协议 + 域名 + 端口）
        const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
        baseURL = origin + baseURL;
    }

    return {
        apiKey,
        baseURL,
        platform: platform.name,
    };
};
