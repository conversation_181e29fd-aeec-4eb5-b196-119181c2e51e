<!-- 折叠会话列表 -->
<template>
    <div class="btn-chatlist-toggle" :class="{ 'hide-chatlist': isHidden }">
        <img
            :src="isHidden ? 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-chatgpt/contract-right-line.png' : 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-chatgpt/expand-left-line2.png' "
            :alt="isHidden ? '展开会话列表' : '收起会话列表'"
            v-tooltip="{ value: isHidden ? '展开会话列表' : '收起会话列表', showDelay: 1000, hideDelay: 300 }"
            @click="toggleChatList"
        />
    </div>
</template>

<script>
import { mapActions } from 'pinia';
import useAppStore from '@/app/stores/app';

export default {
    name: 'ButtonChatToggle',
    data() {
        return {
            isHidden: false,
        };
    },
    methods: {
        ...mapActions(useAppStore, ['reportUserEvent']),
        toggleChatList() {
            this.isHidden = !this.isHidden;
            document.querySelector('.home-wrap .chat-content').classList.toggle('hide-chatlist');
            document.querySelector('.home-wrap .chat-list').classList.toggle('hide-chatlist');

            this.reportUserEvent('click_appChatgpt_chatToogle');
        },
    },
};
</script>

<style lang="sass">
.btn-chatlist-toggle
  position: absolute
  left: 300px
  top: 34px
  cursor: pointer
  z-index: 10000
  transition: left 0.1s ease
  img
    width: 30px
    height: 30px

.btn-chatlist-toggle.hide-chatlist
  left: 20px

.home-wrap
  .chat-list.hide-chatlist
    left: -350px
    transition: left 0.3s ease
  .chat-content.hide-chatlist
    margin-left: 16px
    transition: margin-left 0.1s ease
</style>
