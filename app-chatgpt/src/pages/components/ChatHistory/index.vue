<template>
    <section class="m-chat-content">
        <div class="m-chat-content-inner">
            <div class="conversation">
                <section class="item" v-for="message in messages" :key="message.id">
                    <div class="user-query">
                        <i class="pi pi-user pic"></i>
                        <div class="content">
                            <p class="info">
                                {{ formatDate(message.questionTime) }}
                            </p>
                            <div class="content__detail">
                                {{ message.questionContent }}
                            </div>
                        </div>
                    </div>
                    <div class="model-response">
                        <div class="pic">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-navbar/ai_dev_logo_small.png"
                                alt=""
                            />
                        </div>
                        <div class="content">
                            <p class="info">
                                <span>{{ formatDate(message.questionTime) }}</span>
                            </p>
                            <div
                                class="markdown-body"
                                :id="message.id"
                                v-html="renderMarkdown(message.answerContent || '')"
                            ></div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </section>
</template>

<script>
import { formatDate } from '@/app/utils/index';
import { md } from '@/app/utils/markdown';

export default {
    name: 'ChatHistory',
    props: {
        messages: {
            type: Array,
            required: true,
        },
    },
    methods: {
        formatDate,
        renderMarkdown(markdownContent) {
            return md.render(markdownContent);
        },
    },
};
</script>

<style lang="scss">
.m-chat-content {
    .m-chat-content-inner {
        position: relative;
        display: flex;
        flex-direction: column;
        margin: 20px auto;
        padding: 0 16px 20px;
        background-color: #f4f7fc;
        border-radius: 24px;

        .conversation {
            position: relative;
            flex: 1;
            overflow-y: auto;
            scroll-behavior: smooth;

            section.item {
                max-width: 1200px;
                margin: 0 auto;

                .user-query {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    padding: 12px 0 12px 12px;

                    .pic {
                        font-size: 1.4rem;
                        color: #6366f1;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 32px;
                        height: 32px;
                        min-width: 32px;
                        background-color: #ededff;
                        border-radius: 50%;
                    }

                    .content {
                        flex: 1;
                        line-height: 28px;
                        white-space: pre-wrap;
                        word-wrap: break-word;
                        overflow-x: auto;

                        p.info {
                            height: 32px;
                            font-size: 12px;
                            opacity: 0.5;
                            margin: 0;
                            display: flex;
                            align-items: center;
                        }

                        .content__detail {
                            font-size: 14px;
                            color: #2c2c36;
                            line-height: 1.4;
                            letter-spacing: 0.4px !important;
                        }
                    }

                    .button-gutter {
                        width: 45px;

                        .material-icons {
                            cursor: pointer;
                            font-size: 20px;
                        }
                    }
                }

                .model-response {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    background-color: #fff;
                    border-radius: 16px;
                    padding: 12px;

                    .pic {
                        width: 32px;
                        height: 32px;
                        margin: 0 10px 0 0;

                        img {
                            display: inline-block;
                            width: 100%;
                        }
                    }

                    .content {
                        flex: 1;
                        line-height: 28px;
                        overflow-x: auto;

                        p.info {
                            display: flex;
                            align-items: center;
                            height: 32px;
                            font-size: 12px;
                            opacity: 0.5;
                            margin: 0;
                        }
                    }
                }
            }
        }
    }
}
</style>
