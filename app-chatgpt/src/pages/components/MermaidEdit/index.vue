<template>
    <div class="mermaid-renderer-container">
        <!-- 添加小提示区域 -->
        <div class="mermaid-tip" v-if="iframeUrl">
            <span class="font-medium m-0">如果需要更大编辑空间，可以</span>
            <button class="tip-button font-medium" @click="openInMermaidLive">在官网中编辑</button>
        </div>

        <iframe
            v-if="iframeUrl"
            :src="iframeUrl"
            :style="{ height: height + 'px' }"
            frameborder="0"
            allowfullscreen
            class="mermaid-iframe"
        ></iframe>
    </div>
</template>

<script>
import * as pako from 'pako';

export default {
    name: 'MermaidRenderer',
    props: {
        // 传入的 mermaid 代码
        mermaidCode: {
            type: String,
            required: true,
        },
        // iframe 高度
        height: {
            type: Number,
            default: 600,
        },
    },
    computed: {
        iframeUrl() {
            if (!this.mermaidCode) return 'https://mermaid.live/edit';

            try {
                console.log(this.mermaidCode, 'mermaidCode');

                // 构造正确的JSON对象
                const mermaidData = {
                    code: this.mermaidCode,
                    mermaid: {
                        theme: 'default',
                    },
                    autoSync: true,
                    updateDiagram: false,
                    editorMode: 'code',
                };

                // 将JSON对象转换为字符串
                const jsonString = JSON.stringify(mermaidData);

                // 使用 TextEncoder 将字符串转换为字节数组
                const encoder = new TextEncoder();
                const bytes = encoder.encode(jsonString);

                // 使用 pako 进行 deflate 压缩
                const compressed = pako.deflate(bytes);

                // 转换为 base64
                const base64 = this.bytesToBase64(compressed);

                // 进行URL安全的字符替换
                const urlSafe = base64.replace(/\+/g, '-').replace(/\//g, '_');

                const finalUrl = `https://mermaid.live/edit#pako:${urlSafe}`;
                console.log('Generated URL:', finalUrl);

                return finalUrl;
            } catch (error) {
                console.error('URL编码失败:', error);
                return 'https://mermaid.live/edit';
            }
        },
    },
    methods: {
        // 现代的 base64 编码方法
        bytesToBase64(bytes) {
            const binString = String.fromCodePoint(...bytes);
            return btoa(binString);
        },
        // 在新标签页中打开官网编辑器
        openInMermaidLive() {
            if (this.iframeUrl) {
                window.open(this.iframeUrl, '_blank');
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.mermaid-renderer-container {
    width: 100%;
    height: 100%;
}

.mermaid-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: var(--surface-50);
    border: 1px solid var(--surface-border);
    border-radius: 6px 6px 0 0;
    font-size: 12px;
    color: var(--text-color-secondary);
}

.tip-text {
    margin: 0;
}

.tip-button {
    background: none;
    border: none;
    color: #2196f3;
    cursor: pointer;
    text-decoration: underline;
    font-size: 12px;
    padding: 0;

    &:hover {
        color: #42a5f5;
        text-decoration: none;
    }
}

.mermaid-iframe {
    width: 100%;
    height: 100%;
    display: block;
    border: 1px solid var(--surface-border);
    border-radius: 0 0 6px 6px;
    border-top: none; /* 移除顶部边框，因为tip已经有了 */
}
</style>
