<template>
    <div class="search-history">
        <div class="search-history__input-wrapper">
            <InputGroup class="search-history__input-group">
                <InputText
                    ref="input"
                    placeholder="搜索历史记录"
                    v-model="inputValue"
                    @update:modelValue="debounceSearchMessage"
                    class="search-history__input"
                />
            </InputGroup>
            <Listbox
                v-show="listBox.visible"
                v-model="listBox.value"
                :options="listBox.messages"
                optionLabel="questionContent"
                listStyle="max-height:400px"
                class="search-history__list"
                @focus="handleListBoxFocus"
            >
                <template #option="slotProps">
                    <section class="search-history__list-item">
                        <div
                            class="search-history__list-content"
                            v-tooltip.right="{
                                value: slotProps.option.questionContent,
                            }"
                            title=""
                            @click="bindMessageSelect(slotProps.option)"
                        >
                            {{ slotProps.option.questionContent }}
                        </div>
                    </section>
                </template>
            </Listbox>
        </div>
        <Sidebar
            header="历史消息"
            class="search-history__sidebar"
            style="width: 40%"
            position="right"
            v-model:visible="sidebar.visible"
            blockScroll
        >
            <template #closeicon>
                <Button
                    icon="pi pi-times"
                    class="p-button-text search-history__close-btn"
                    @click="sidebar.visible = false"
                />
            </template>
            <ChatHistory :messages="sidebar.data" />
        </Sidebar>
    </div>
</template>

<script>
import { mapState, mapActions } from 'pinia';
import useChatStore from '@/app/stores/chat';
import ChatHistory from '@/pages/components/ChatHistory/index.vue';
import { debounce } from 'lodash';

export default {
    components: {
        ChatHistory,
    },
    data() {
        return {
            inputValue: '',
            sidebar: {
                visible: false,
                data: [],
            },
            listBox: {
                value: '',
                visible: false,
                messages: [],
            },
        };
    },
    computed: {
        ...mapState(useChatStore, ['searchMessages']),
    },
    methods: {
        ...mapActions(useChatStore, ['getSearchMessages']),

        resetListBox() {
            this.listBox.messages = [];
            this.listBox.visible = false;
        },

        async bindSearchMessage() {
            if (!this.inputValue) {
                this.resetListBox();
                return;
            }

            this.listBox.visible = true;

            const params = {
                keyword: this.inputValue,
                pageIndex: 1,
                pageSize: 999,
            };

            try {
                const res = await this.getSearchMessages(params);
                this.listBox.messages = res.list;
            } catch (error) {
                this.$toast.add({
                    severity: 'error',
                    summary: '请求失败',
                    detail: error.message,
                    life: 3000,
                });
            }
        },

        debounceSearchMessage: debounce(function () {
            this.bindSearchMessage();
        }, 500),

        bindMessageSelect(event) {
            this.listBox.visible = false;
            this.sidebar.visible = true;
            this.sidebar.data[0] = event;
        },

        bindInputFocus() {
            if (this.inputValue) {
                this.bindSearchMessage();
            }
        },

        bindInputBlur() {
            setTimeout(() => {
                this.resetListBox();
            }, 200);
        },

        handleListBoxFocus(e) {
            console.log(e, 'handleListBoxFocus123');
        },
    },

    mounted() {
        this.$refs.input.$el.addEventListener('blur', this.bindInputBlur);
        this.$refs.input.$el.addEventListener('focus', this.bindInputFocus);
    },

    beforeUnmount() {
        this.$refs.input.$el.removeEventListener('blur', this.handleBlur);
        this.$refs.input.$el.removeEventListener('focus', this.bindInputFocus);
    },
};
</script>

<style lang="scss" scoped>
.search-history {
    width: 100%;
    &__input-wrapper {
        width: 100%;
        position: relative;
    }

    &__input-group {
        width: 100%;
    }

    &__list {
        position: absolute;
        top: 35px;
        z-index: 10;
        width: 300px;
    }

    &__list-item {
        display: flex;
    }

    &__list-content {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.875rem;
    }

    &__sidebar {
        width: 50%;
    }
}
</style>
