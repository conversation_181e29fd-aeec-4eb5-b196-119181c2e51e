.chat-content {
    height: 100%;
    overflow: hidden;
    border-radius: 18px;
    margin-left: 330px;
    flex: 1;
    z-index: 9;
    :deep(.p-button .pi) {
        font-size: 1.3rem !important;
    }
    .p-progressbar {
        position: absolute;
        left: 0;
        top: -1px;
        width: 100%;
        z-index: 99;
    }
    .chat-content-inner:before {
        content: '';
        position: absolute;
        width: 100%;
        border-top: 2px solid #fff;
        z-index: 1;
    }
    .chat-content-inner {
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 0 16px;
        margin: 0 auto;
        background-color: #f9fafd;
        .conversation-inner {
            flex: 1;
            position: relative;
            overflow-y: auto;
            width: 100%;
            margin: 0 auto;
            section.item-checked {
                background-color: transparent;
            }
            section.item {
                display: flex;
                justify-content: center;
                max-width: 1200px;
                margin: 0 auto 10px;
                padding: 4px 0;
                .item-left {
                    cursor: pointer;
                    width: 8px;
                    margin: 0 0 0 auto;
                    flex: 0 0 auto;
                    border-radius: 0 4px 4px 0;
                    transition: all 0.2s ease;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover::after {
                        content: '点击选择上下文';
                        position: absolute;
                        left: 16px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: rgba(99, 102, 241, 0.9);
                        color: white;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        white-space: nowrap;
                        z-index: 10;
                        opacity: 0.9;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }
                }
                .item-context-selected {
                    border-left: 4px solid rgba(99, 102, 241, 0.5);
                    background-color: rgba(99, 102, 241, 0.05);
                }
                .item-hover-class {
                    border-left: 4px solid rgba(189, 191, 195, 0.3);
                    background-color: rgba(189, 191, 195, 0.05);
                }
                .item-hover-class-selected {
                    border-left: 4px solid rgba(99, 102, 241, 0.7);
                    background-color: rgba(99, 102, 241, 0.1);
                }
                .item-left-hover-class {
                    background-color: rgba(189, 191, 195, 0.2);
                }
                .item-left-hover-class-selected {
                    background-color: rgba(99, 102, 241, 0.2);
                }
                .item-noBorder {
                    border-left: 0 !important;
                    background: transparent !important;
                }
                .item-right {
                    max-width: calc(100% - 8px);
                    margin: 0 auto 0 0;
                    flex: 1 1 auto;
                    .user-query {
                        position: relative;
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        padding: 14px 30px 14px 12px;
                        border-radius: 12px;
                        transition: background-color 0.2s ease;

                        .pic {
                            color: #fff;
                            margin-right: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 32px;
                            height: 32px;
                            background-color: #6366f1;
                            border-radius: 50%;
                        }

                        .content {
                            flex: 1;
                            line-height: 24px;
                            white-space: pre-wrap;
                            word-wrap: break-word;
                            overflow-x: auto;
                            margin-top: 2px;
                            p.info {
                                font-size: 11px;
                                opacity: 0.6;
                                margin: 0;
                            }
                            .content__detail {
                                font-size: 14px;
                                color: #2c2c36;
                                line-height: 1.8;
                                letter-spacing: 0.8px !important;
                            }
                        }
                        .button-gutter {
                            padding-right: 0.5rem;
                            img {
                                width: 20px;
                                height: 20px;
                                cursor: pointer;
                                transition: transform 0.2s ease;
                                &:hover {
                                    transform: scale(1.1);
                                }
                            }
                        }
                        .tool-insert {
                            position: absolute;
                            display: none;
                            left: 10px;
                            bottom: -30px;
                            cursor: pointer;
                            background: #fff;
                            z-index: 99;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }
                        .tool-insert:hover {
                            display: flex;
                        }
                    }
                    .user-query:hover {
                        .tool-insert {
                            display: flex;
                        }
                    }
                    .model-response {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        background-color: #fff;
                        border-radius: 6px;
                        padding: 16px 30px 10px 12px;

                        .pic {
                            margin-right: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }

                        .content {
                            flex: 1;
                            line-height: 24px;
                            overflow-x: auto;
                            margin-top: 5px;
                            p.info {
                                font-size: 11px;
                                opacity: 0.6;
                                margin: 0;
                                .role {
                                    margin-right: 10px;
                                }
                            }
                            .answer-tools {
                                display: flex;
                                align-items: center;
                                padding: 10px 0 0;
                                border-top: 1px solid #f0f0f0;
                                transition:
                                    opacity 0.2s ease,
                                    transform 0.2s ease;
                                transform: translateY(0);
                                img {
                                    width: 18px;
                                    height: 18px;
                                    cursor: pointer;
                                    transition: transform 0.2s ease;
                                    &:hover {
                                        transform: scale(1.08);
                                    }
                                }
                            }
                            .suggestion {
                                border-top: 1px dashed #ddd;
                                margin-top: 20px;
                                padding: 10px 0;
                                h6 {
                                    font-size: 14px;
                                    margin: 0;
                                }
                                ol {
                                    margin: 0;
                                    padding: 0;
                                    list-style: none;
                                    .p-button {
                                        font-size: 13px;
                                    }
                                }
                            }
                        }
                    }

                    .model-response--checked {
                        border: 2px solid #6366f1;
                    }
                    .model-response--pointer {
                        cursor: pointer;
                    }
                }
            }
            section.item-highlight {
                background-color: #e0e0fa;
            }
        }
    }
    .user-input {
        position: relative;
        width: 90%;
        max-width: 1200px;
        color: #444746;
        padding: 12px 0px 12px;
        margin: 0 auto;
        z-index: 9;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            left: -40px;
            right: -40px;
            bottom: 0;
            height: 100%;
            background: linear-gradient(to bottom, rgba(249, 250, 253, 0), rgba(249, 250, 253, 0.9) 20%, #f9fafd);
            z-index: -1;
            pointer-events: none;
        }

        .user-input-chat {
            position: relative;
            .action-buttons {
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                padding: 0.75rem 0;
                margin-bottom: 0.5rem;
                border-radius: 10px;
                position: relative;

                ul {
                    display: flex;
                    flex-wrap: nowrap;
                    align-items: center;
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    width: 100%;
                    gap: 0.75rem;

                    li {
                        margin-bottom: 0.25rem;
                        margin-right: 0.25rem;
                        white-space: nowrap;

                        &:last-child {
                            margin-left: auto;
                        }

                        .splitbutton-badge-wrapper {
                            position: relative;

                            .custom-badge {
                                position: absolute;
                                top: -8px;
                                right: -8px;
                                min-width: 1.2rem;
                                height: 1.2rem;
                                background: #6366f1;
                                color: white;
                                border-radius: 50%;
                                font-size: 0.75rem;
                                font-weight: 700;
                                box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
                            }
                        }
                    }
                }
            }

            :deep(.p-menu.p-menu-overlay) {
                background: #ffffff;
                border: 1px solid #e5e7eb;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                border-radius: 6px;

                .auto-context {
                    display: flex;
                    align-items: center;
                    padding: 8px 12px;

                    label {
                        cursor: pointer;
                        margin-left: 8px;
                        font-size: 14px;
                        color: #26244ce0;
                    }
                }
            }

            form.form {
                margin-bottom: 16px;
                .form-inner {
                    position: relative;
                    background: #fff;
                    border: 1px solid #e5e7eb;
                    border-radius: 18px;
                    overflow: hidden;
                    z-index: 9;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
                    transition: all 0.3s ease;

                    &:hover,
                    &:focus-within {
                        box-shadow: 0 8px 30px rgba(99, 102, 241, 0.15);
                        border-color: rgba(99, 102, 241, 0.4);
                        transform: translateY(-2px);
                    }

                    .header {
                        display: block;
                        .close-icon {
                            position: absolute;
                            right: 20px;
                            top: 50%;
                            transform: translateY(-50%);
                            cursor: pointer;
                            font-size: 1.2rem;
                            color: #606060;
                            &:hover {
                                color: #333;
                            }
                        }
                        .prompts,
                        .history {
                            border-bottom: 1px solid #eee;
                        }
                        .history {
                            position: relative;
                            z-index: 9;
                            .history-empty {
                                padding: 10px 0;
                                text-align: center;
                                p {
                                    margin: 10px 0 0 0;
                                    font-size: 14px;
                                    letter-spacing: 1px;
                                    opacity: 0.6;
                                }
                            }
                            .ul__history {
                                margin: 0;
                                padding: 0;
                                height: 200px;
                                overflow-y: auto;
                                li {
                                    width: 100%;
                                    display: flex;
                                    justify-content: flex-start;
                                    align-items: center;
                                    cursor: pointer;
                                    padding: 10px;
                                    border-bottom: 1px solid #eee;
                                    .pi {
                                        color: #6366f1;
                                        margin-right: 8px;
                                    }
                                    span {
                                        flex: 1;
                                        font-size: 14px;
                                        white-space: nowrap;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }
                                }
                                li:hover {
                                    background-color: #f9fafb;
                                }
                            }
                        }
                    }
                    .main {
                        position: relative;
                        z-index: 9;
                        .input-area {
                            flex: 1;
                            padding: 18px 64px 18px 24px;
                            position: relative;

                            &::before {
                                content: '输入您的问题...';
                                position: absolute;
                                left: 24px;
                                top: 18px;
                                font-size: 14px;
                                color: #9ca3af;
                                pointer-events: none;
                                opacity: 0;
                                transition: opacity 0.2s ease;
                            }

                            &:has(textarea:placeholder-shown)::before {
                                opacity: 0;
                            }

                            textarea {
                                display: block;
                                width: 100%;
                                color: #3c4043;
                                min-height: 56px;
                                height: 56px;
                                line-height: 24px;
                                max-height: 320px;
                                padding: 0;
                                border: none;
                                outline: none;
                                resize: none;
                                font-size: 14px;
                                font-family:
                                    system-ui,
                                    -apple-system,
                                    BlinkMacSystemFont,
                                    'Segoe UI',
                                    Roboto,
                                    sans-serif;
                                transition: all 0.2s ease;

                                &::placeholder {
                                    color: #9ca3af;
                                    opacity: 1;
                                }

                                &.textarea-focused {
                                    color: #111827;
                                }
                            }

                            &:focus-within::after {
                                content: '';
                                position: absolute;
                                left: 0;
                                top: 12px;
                                bottom: 12px;
                                width: 3px;
                                background-color: #6366f1;
                                border-radius: 3px;
                                animation: fadeIn 0.3s ease forwards;
                            }

                            @keyframes fadeIn {
                                from {
                                    opacity: 0;
                                    transform: translateX(-5px);
                                }
                                to {
                                    opacity: 1;
                                    transform: translateX(0);
                                }
                            }
                        }
                        .sending {
                            position: absolute;
                            right: 20px;
                            bottom: 18px;
                            display: flex;
                            flex: 1;
                            justify-content: flex-end;
                            align-items: center;
                            z-index: 99;
                            :deep(.p-button) {
                                width: 38px;
                                height: 38px;
                                padding: 0;
                                box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
                                background-color: #6366f1;
                                border-color: #6366f1;
                                color: #fff;

                                .p-button-icon {
                                    font-size: 1.3rem;
                                }

                                &:hover {
                                    transform: scale(1.05);
                                    transition: transform 0.2s ease;
                                    background-color: #4f46e5;
                                    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
                                }
                            }
                        }
                        .full-screen {
                            .full-screen-edit {
                                position: absolute;
                                right: 20px;
                                top: 18px;
                                z-index: 99;

                                &:hover {
                                    color: #6366f1;
                                    transform: scale(1.05);
                                    transition: all 0.2s ease;
                                }
                            }
                        }
                    }
                }
            }
            .disclaimer {
                font-size: 11px;
                text-align: center;
                color: #6b7280;
                margin-top: 10px;
                p {
                    margin: 0;
                    a {
                        color: #6366f1;
                        text-decoration: underline;
                        text-underline-offset: 2px;

                        &:hover {
                            opacity: 0.8;
                        }
                    }
                }
            }
            .backToBottom-btn {
                position: absolute;
                bottom: 190px;
                left: 50%;
                transform: translateX(-50%);
                cursor: pointer;
                z-index: 8;
                width: 28px;
                height: 28px;

                :deep(.pi) {
                    font-size: 1rem !important;
                }
            }
        }
        .user-input-share {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 120px;
            .share-btn {
                text-align: center;
            }
        }
    }
}
// Sidebar - 全屏组件
.input-area-fullScreen {
    .p-sidebar-header {
        padding: 16px 24px;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
        }

        button {
            transition: all 0.2s ease;

            &:hover {
                background-color: #f3f4f6 !important;
                color: #6366f1 !important;
                transform: scale(1.05);
            }
        }
    }

    .p-sidebar-content {
        background-color: #fff;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: calc(100% - 60px);

        .p-inputtextarea {
            display: block;
            width: 80%;
            min-height: calc(100% - 60px);
            margin: 30px auto;
            padding: 24px;
            font-size: 16px;
            line-height: 1.6;
            border-radius: 12px;
            border-color: #e5e7eb;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            font-size: 1rem;
            letter-spacing: 0.4px;
            line-height: 1.4;
            overflow-y: auto !important;

            &:focus {
                border-color: #6366f1;
                outline: none;
            }
        }
    }

    :deep(.p-sidebar) {
        background-color: #fff;
    }
}
:global(.context-setting) {
    background: #fff;
}
:global(.context-setting .p-menuitem-content) {
    background: #fff;
}
.fullScreenEdit-enter-active {
    transition: opacity 0.5s ease;
}
.fullScreenEdit-leave-active {
    transition: opacity 0.5s ease;
}
.fullScreenEdit-enter-from,
.fullScreenEdit-leave-to {
    opacity: 0;
}

.context-selector {
    cursor: pointer;
    width: 32px;
    margin: 0 0 0 auto;
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    opacity: 0;
    margin-right: 5px;

    .selector-inner {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        i {
            font-size: 16px;
            color: #9ca3af;
            transition: all 0.2s ease;
        }

        &:hover {
            background-color: #e5e7eb;
            transform: scale(1.05);

            i {
                color: #6366f1;
            }
        }

        &.selector-selected {
            background-color: rgba(99, 102, 241, 0.15);

            i {
                color: #6366f1;
            }

            &:hover {
                background-color: rgba(99, 102, 241, 0.25);
            }
        }
    }
}

section.item:hover .context-selector {
    opacity: 1;
}

.context-selector.selector-selected,
section.item .context-selector .selector-inner.selector-selected {
    opacity: 1;
}

/* 删除旧的样式 */
.item-left {
    display: none;
}
.item-context-selected,
.item-hover-class,
.item-hover-class-selected,
.item-left-hover-class,
.item-left-hover-class-selected,
.item-noBorder {
    display: none;
}

/* 新增上下文选择按钮样式 */
.context-action-button {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    cursor: pointer;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    line-height: 1.4;

    i {
        font-size: 12px;
        margin-right: 5px;
        color: #6b7280;
    }

    span {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
    }

    &:hover {
        background-color: #e5e7eb;

        i,
        span {
            color: #4b5563;
        }
    }

    &.context-selected {
        background-color: rgba(99, 102, 241, 0.1);
        border-color: rgba(99, 102, 241, 0.3);

        i,
        span {
            color: #6366f1;
        }

        &:hover {
            background-color: rgba(99, 102, 241, 0.15);
        }
    }
}

/* 删除不再需要的旧样式 */
.context-selector,
.item-left {
    display: none;
}

/* 添加上下文高亮样式 */
.item-in-context {
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background: var(--primary-color, #6366f1);
        border-radius: 0 2px 2px 0;
        animation: pulse-border 2s infinite;
    }
}

@keyframes pulse-border {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

/* 增强上下文按钮样式 */
.context-highlight {
    background-color: rgba(99, 102, 241, 0.15) !important;
    border: 1px solid var(--primary-color, #6366f1) !important;
    font-weight: 600 !important;

    i,
    span {
        color: var(--primary-color, #6366f1) !important;
    }
}

// 添加上下文列表样式
.context-list-container {
    max-height: 400px;
    overflow-y: auto;

    .context-list-item {
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
            background: #f1f5f9;
        }

        .context-index {
            background: var(--primary-color, #6366f1);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .context-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 350px;
            font-size: 0.9rem;
        }
    }
}

// 增强上下文数量徽章的可点击性
.custom-badge.cursor-pointer {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        transform: scale(1.1);
        background-color: var(--primary-600, #4f46e5) !important;
    }
}
