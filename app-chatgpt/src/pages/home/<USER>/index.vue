<template>
    <section class="chat-list">
        <Button
            type="button"
            label="新建会话"
            icon="pi pi-plus-circle"
            :loading="loading"
            @click="editSessionDialog('create')"
            class="chat-list__create-btn w-5"
        />
        <div class="chat-list__search flex justify-content-start mt-4 mb-2">
            <search-history />
        </div>
        <ScrollPanel
            :pt="{
                root: 'chat-list__scroll',
                bary: {
                    class: 'surface-300 opacity-100 border-noround outline-none',
                },
            }"
        >
            <TabView v-model:activeIndex="tabViewIndex" @tab-change="handleTabChange" class="chat-list__tabs">
                <TabPanel header="进行中会话">
                    <div
                        v-for="(session, index) in sessions"
                        :key="session.id"
                        :class="{
                            'chat-list__session--active': activeIndex === index,
                        }"
                        class="chat-list__session"
                    >
                        <div class="chat-list__session-item" @click.prevent="getChatMessagesBySelect(session, index)">
                            <i class="chat-list__session-icon fa-regular fa-message fa-sm mr-2"></i>
                            <span
                                class="chat-list__session-text"
                                :style="{
                                    'padding-right':
                                        session.sessionType === 1 || session.sessionType === 2 ? '50px' : '0',
                                }"
                            >
                                {{ session.sessionName }}
                                <Badge
                                    v-if="session.sessionType === 1"
                                    value="idea"
                                    severity="warning"
                                    size="small"
                                    class="chat-list__session-badge"
                                ></Badge>
                                <Badge
                                    v-if="session.sessionType === 2"
                                    value="vscode"
                                    severity="info"
                                    size="small"
                                    class="chat-list__session-badge"
                                ></Badge>
                            </span>
                        </div>
                        <div
                            class="chat-list__session-more"
                            @click.stop="(event) => selectSectionMore(event, session, index)"
                        >
                            <i class="pi pi-pencil"></i>
                        </div>
                    </div>
                </TabPanel>
                <TabPanel header="归档会话">
                    <div
                        v-for="(session, index) in sessionsArchived"
                        :key="session.id"
                        :class="{
                            'chat-list__session--active': activeIndexArchived === index,
                        }"
                        class="chat-list__session"
                    >
                        <div
                            class="chat-list__session-item"
                            @click.prevent="getChatMessagesBySelect(session, index, 'archive')"
                        >
                            <i class="chat-list__session-icon fa-regular fa-message fa-sm mr-2"></i>
                            <span
                                class="chat-list__session-text"
                                :style="{
                                    'padding-right':
                                        session.sessionType === 1 || session.sessionType === 2 ? '50px' : '0',
                                }"
                            >
                                {{ session.sessionName }}
                                <Badge
                                    v-if="session.sessionType === 1"
                                    value="idea"
                                    severity="warning"
                                    size="small"
                                    class="chat-list__session-badge"
                                ></Badge>
                                <Badge
                                    v-if="session.sessionType === 2"
                                    value="vscode"
                                    severity="info"
                                    size="small"
                                    class="chat-list__session-badge"
                                ></Badge>
                            </span>
                        </div>
                    </div>
                </TabPanel>
            </TabView>
        </ScrollPanel>
        <Button
            icon="pi pi-cog"
            label="会话设置"
            class="chat-list__settings-btn pd-2 mt-3 mb-3 w-5"
            @click="openSettingDialog"
            rounded
        ></Button>

        <!-- Dropdown : 会话 - 更多操作 -->
        <DropdownAuto
            :visibility="dropdown.visibility"
            :style="dropdown.style"
            @edit-session="editSessionDialog('edit', dropdown.index)"
            @delete-session="dialog.confirm.deleteSession.visibility = true"
            @close="dropdown.visibility = false"
            class="chat-list__dropdown"
        >
        </DropdownAuto>

        <!-- 弹窗 : 会话 - 创建&编辑  -->
        <teleport to="body">
            <section class="modal chat-list__modal fadein" v-show="dialog.session.visibility">
                <div class="modal-dialog modal-dialog-centered" role="document" style="width: 550px">
                    <div class="chat-list__modal-header modal-header">
                        <h5 class="chat-list__modal-title modal-title">
                            {{ dialog.session.title }}
                        </h5>
                        <i
                            class="chat-list__modal-close fa-regular fa-circle-xmark fa-lg"
                            @click.prevent="closeSessionDialog"
                        ></i>
                    </div>
                    <!-- 表单组件开始 -->
                    <form @submit.prevent="sumbitSessionDialog" class="chat-list__form">
                        <div class="chat-list__form-body modal-body">
                            <input name="type" type="hidden" v-model="dialog.session.data.type" />
                            <input name="id" type="hidden" v-model="dialog.session.data.id" />
                            <div
                                class="chat-list__form-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__form-label w-4"> 聊天名称 </label>
                                <div class="chat-list__form-input flex-1">
                                    <InputText
                                        class=""
                                        id="name"
                                        name="name"
                                        type="text"
                                        placeholder="请输入聊天名称"
                                        v-model="dialog.session.data.name"
                                    />
                                </div>
                            </div>
                            <div
                                class="chat-list__form-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__form-label w-4">
                                    角色设定
                                    <a
                                        @click="reportUserEvent('clickfaq')"
                                        href="/app-product-gallery/faq"
                                        target="_blank"
                                        class="chat-list__form-help"
                                    >
                                        <i
                                            class="pi pi-question-circle"
                                            v-tooltip.top="'模型初始说明，例如你是一名web专家'"
                                        ></i>
                                    </a>
                                </label>
                                <div class="chat-list__form-input flex-1">
                                    <Textarea
                                        :pt="{
                                            root: {
                                                style: {
                                                    'max-height': '300px',
                                                    'overflow-y': 'auto',
                                                },
                                            },
                                        }"
                                        v-model="dialog.session.data.systemMessage"
                                        autoResize
                                        rows="5"
                                        cols="30"
                                    />
                                </div>
                            </div>
                            <div
                                class="chat-list__form-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__form-label w-4">
                                    模型
                                    <a
                                        @click="reportUserEvent('clickfaq')"
                                        href="/app-product-gallery/faq"
                                        target="_blank"
                                        class="chat-list__form-help"
                                    >
                                        <i
                                            class="pi pi-question-circle"
                                            v-tooltip.top="
                                                '不同的模型，拥有不同的语言及逻辑推理能力，可点击修改会话的按钮来修改模型'
                                            "
                                        ></i>
                                    </a>
                                </label>
                                <div class="chat-list__form-input flex-1">
                                    <Dropdown
                                        v-model="dialog.session.data.model"
                                        :options="availableModels"
                                        placeholder="请选择模型..."
                                    />
                                </div>
                            </div>
                            <div
                                class="chat-list__form-group form-group flex align-items-start justify-content-between mb-4"
                            >
                                <label class="chat-list__form-label w-4">
                                    温度
                                    <a
                                        @click="reportUserEvent('clickfaq')"
                                        href="/app-product-gallery/faq"
                                        target="_blank"
                                        class="chat-list__form-help"
                                    >
                                        <i
                                            class="pi pi-question-circle"
                                            v-tooltip.top="
                                                '温度越低，生成策略越保守，反之生成策略越多样化，可点击修改会话的按钮来修改温度'
                                            "
                                        ></i>
                                    </a>
                                </label>
                                <div
                                    class="chat-list__form-input flex-1 flex flex-wrap align-items-center justify-content-between"
                                >
                                    <Slider
                                        v-model="dialog.session.data.temperature"
                                        class="w-10"
                                        :min="0"
                                        :max="1"
                                        :step="0.01"
                                        :pt="{
                                            range: {
                                                style: {
                                                    background: temperatureConfig(dialog.session.data.temperature)
                                                        .color,
                                                },
                                            },
                                        }"
                                    />
                                    <span
                                        class="chat-list__temperature-value ml-3 font-bold"
                                        :style="{
                                            color: temperatureConfig(dialog.session.data.temperature).color,
                                        }"
                                        >{{ dialog.session.data.temperature }}</span
                                    >
                                    <MeterGroup
                                        :value="meterGroupValue"
                                        :max="100"
                                        class="chat-list__meter-group w-10 mt-3"
                                    >
                                        <template #label>
                                            <div
                                                class="chat-list__meter-labels flex w-full justify-content-between align-items-center"
                                            >
                                                <span
                                                    v-for="item in meterGroupValue"
                                                    :key="item.label"
                                                    :style="{
                                                        color: item.color,
                                                    }"
                                                    class="chat-list__meter-label"
                                                >
                                                    {{ item.label }}
                                                </span>
                                            </div>
                                        </template>
                                    </MeterGroup>
                                </div>
                            </div>
                        </div>
                        <div class="chat-list__form-footer modal-footer">
                            <Button
                                label="取消"
                                severity="secondary"
                                @click.prevent="closeSessionDialog"
                                class="chat-list__form-btn mr-3"
                                size="small"
                            />
                            <Button
                                label="确认"
                                @click.prevent="sumbitSessionDialog"
                                class="chat-list__form-btn"
                                size="small"
                            />
                        </div>
                    </form>
                    <!-- 表单组件结束 -->
                </div>
                <div class="modal-backdrop"></div>
            </section>
        </teleport>

        <!-- 弹窗 : 二次确认  -->
        <teleport to="body">
            <section class="modal chat-list__confirm-modal fadein" v-show="dialog.confirm.deleteSession.visibility">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="chat-list__confirm-header modal-header">
                        <h5 class="chat-list__confirm-title modal-title">
                            {{ dialog.confirm.deleteSession.title }}
                        </h5>
                        <i
                            class="chat-list__confirm-close fa-regular fa-circle-xmark fa-lg"
                            @click.prevent="dialog.confirm.deleteSession.visibility = false"
                        ></i>
                    </div>
                    <div class="chat-list__confirm-body modal-body">
                        {{ dialog.confirm.deleteSession.content }}
                    </div>
                    <div class="chat-list__confirm-footer modal-footer">
                        <Button
                            label="取消"
                            severity="secondary"
                            @click.prevent="dialog.confirm.deleteSession.visibility = false"
                            class="chat-list__confirm-btn mr-3"
                        />
                        <Button
                            label="确认"
                            @click.prevent="deleteSessionDialog"
                            type="submit"
                            :loading="loading"
                            class="chat-list__confirm-btn"
                        />
                    </div>
                </div>
                <div class="modal-backdrop"></div>
            </section>
        </teleport>

        <!-- 弹窗 : 会话设置 -->
        <teleport to="body">
            <section class="modal chat-list__settings-modal fadein" v-show="dialog.setting.visibility">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="chat-list__settings-header modal-header">
                        <h5 class="chat-list__settings-title modal-title">
                            {{ dialog.setting.title }}
                        </h5>
                        <i
                            class="chat-list__settings-close fa-regular fa-circle-xmark fa-lg"
                            @click.prevent="dialog.setting.visibility = false"
                        ></i>
                    </div>
                    <div class="chat-list__settings-body modal-body">
                        <form class="chat-list__settings-form form-app-chatgpt-setting">
                            <div
                                class="chat-list__settings-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__settings-label w-2">发送消息</label>
                                <div class="chat-list__settings-input flex-1">
                                    <Dropdown
                                        placeholder="请选择..."
                                        v-model="appConfig.shortcuts.sendChatMessage"
                                        :options="dialog.setting.form.shortcuts.options"
                                        optionLabel="label"
                                        optionValue="value"
                                        @change="sumbitSettingDialog"
                                    />
                                </div>
                            </div>
                            <div
                                class="chat-list__settings-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__settings-label w-2">开启推荐</label>
                                <div class="chat-list__settings-input flex-1">
                                    <Dropdown
                                        placeholder="请选择..."
                                        v-model="appConfig.enableSuggest"
                                        :options="dialog.setting.form.enableSuggest.options"
                                        optionLabel="label"
                                        optionValue="value"
                                        @change="sumbitSettingDialog"
                                    />
                                </div>
                            </div>
                            <div
                                class="chat-list__settings-group form-group flex align-items-center justify-content-between mb-4"
                            >
                                <label class="chat-list__settings-label w-2" for="ideAuthCode">IDE认证码</label>
                                <div class="chat-list__settings-input flex-1">
                                    <InputText id="ideAuthCode" disabled v-model="dialog.setting.data.ideAuthCode" />
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-backdrop"></div>
            </section>
        </teleport>
    </section>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia';
import useChatStore from '@/app/stores/chat';
import useChatvmStore from '@/app/stores/chatvm';
import useAppStore from '@/app/stores/app';

import DropdownAuto from '@/app/components/dropdown/index.vue';
import SearchHistory from '../components/SearchHistory.vue';
import { scrollToBottom } from '@/app/utils/index';

export default {
    name: 'PageHomeList',
    components: {
        DropdownAuto,
        SearchHistory,
    },
    data() {
        return {
            items: [
                {
                    label: 'Add',
                    icon: 'pi pi-pencil',
                    command: () => {
                        this.$toast.add({
                            severity: 'info',
                            summary: 'Add',
                            detail: 'Data Added',
                        });
                    },
                },
                {
                    label: 'Update',
                    icon: 'pi pi-refresh',
                    command: () => {
                        this.$toast.add({
                            severity: 'success',
                            summary: 'Update',
                            detail: 'Data Updated',
                        });
                    },
                },
                {
                    label: 'Delete',
                    icon: 'pi pi-trash',
                    command: () => {
                        this.$toast.add({
                            severity: 'error',
                            summary: 'Delete',
                            detail: 'Data Deleted',
                        });
                    },
                },
                {
                    label: 'Upload',
                    icon: 'pi pi-upload',
                    command: () => {
                        this.$router.push('/fileupload');
                    },
                },
                {
                    label: 'Vue Website',
                    icon: 'pi pi-external-link',
                    command: () => {
                        window.location.href = 'https://vuejs.org/';
                    },
                },
            ],
            loading: false,
            meterGroupValue: [
                { label: '精确(0 - 0.3)', color: '#007bff', value: 30 },
                { label: '平衡(0.3 - 0.7)', color: '#28a745', value: 40 },
                { label: '创意(0.7 - 1)', color: '#dc3545', value: 30 },
            ],
            activeIndex: 0,
            activeIndexArchived: 0,
            tabViewIndex: 0,
            dropdown: {
                index: -1,
                visibility: false,
                style: {
                    position: 'fixed',
                    top: '0px',
                    left: '0px',
                    transform: 'translate(20px, -10px)',
                    'z-index': 999,
                },
            },
            dialog: {
                session: {
                    title: '新建',
                    visibility: false,
                    data: {
                        type: 'create',
                        id: null,
                        name: '',
                        model: '',
                        temperature: 0.5,
                    },
                },
                setting: {
                    title: '会话设置',
                    visibility: false,
                    form: {
                        shortcuts: {
                            options: [
                                {
                                    label: `Enter发送, Shift+Enter换行`,
                                    value: '1',
                                },
                                {
                                    label: `Enter换行, Crtl+Enter发送`,
                                    value: '2',
                                },
                                {
                                    label: `Enter发送, Crtl+Enter换行`,
                                    value: '3',
                                },
                            ],
                        },
                        enableSuggest: {
                            options: [
                                { label: '关闭', value: '0' },
                                { label: '开启', value: '1' },
                            ],
                        },
                    },
                    data: {
                        ideAuthCode: '',
                    },
                },
                confirm: {
                    deleteSession: {
                        title: '废弃会话',
                        content: '废弃后，将只能在"归档会话"中查看.',
                        visibility: false,
                    },
                },
            },
            speedDialItems: [
                {
                    label: 'Add',
                    icon: 'pi pi-pencil',
                    command: () => {
                        this.$toast.add({
                            severity: 'info',
                            summary: 'Add',
                            detail: 'Data Added',
                        });
                    },
                },
                {
                    label: 'Update',
                    icon: 'pi pi-refresh',
                    command: () => {
                        this.$toast.add({
                            severity: 'success',
                            summary: 'Update',
                            detail: 'Data Updated',
                        });
                    },
                },
                {
                    label: 'Delete',
                    icon: 'pi pi-trash',
                    command: () => {
                        this.$toast.add({
                            severity: 'error',
                            summary: 'Delete',
                            detail: 'Data Deleted',
                        });
                    },
                },
            ],
        };
    },
    computed: {
        ...mapWritableState(useAppStore, ['appConfig']),
        ...mapWritableState(useChatStore, [
            'messages',
            'sessions',
            'session',
            'sessionsArchived',
            'availableModels',
            'settingPopover',
            'sessionCurrentIndex',
        ]),
        ...mapWritableState(useChatvmStore, ['chatList', 'chatForm']),
    },
    methods: {
        ...mapActions(useAppStore, ['reportUserEvent']),
        ...mapActions(useChatStore, [
            'getAvailableModels',
            'getSessions',
            'getSessionsArchived',
            'createSession',
            'updateSession',
            'deleteSession',
            'getMessages',
            'getMessagesArchived',
            'getIdeAuthCode',
        ]),
        ...mapActions(useChatvmStore, ['stopSendMessage']),
        temperatureConfig(temperature) {
            // 按照温度进行颜色区分，跟设置页一致
            if (temperature >= 0 && temperature < 0.3) {
                return {
                    color: '#007bff',
                };
            } else if (temperature >= 0.3 && temperature < 0.7) {
                return {
                    color: '#28a745',
                };
            } else {
                return {
                    color: '#dc3545',
                };
            }
        },

        // 修改保存方法，根据类型分别保存
        saveLastVisitedSessionId(id, type = 'active') {
            const key = type === 'archive' ? 'lastVisitedArchivedSessionId' : 'lastVisitedActiveSessionId';
            localStorage.setItem(key, id);
        },

        // 修改获取方法，根据类型分别获取
        getLastVisitedSessionId(type = 'active') {
            const key = type === 'archive' ? 'lastVisitedArchivedSessionId' : 'lastVisitedActiveSessionId';
            return localStorage.getItem(key);
        },

        async getChatMessagesBySelect(values, index, type) {
            this.sessionCurrentIndex = index;
            let data;
            this.chatForm.loadedAllMessages = false;
            this.chatForm.loading = true;
            this.chatForm.lastScrollTop = 0;
            this.stopSendMessage();

            this.chatForm.data.messages.pageIndex = 1;
            this.chatForm.data.messages.chatSessionId = values.id;
            this.session = values;
            if (type === 'archive') {
                data = await this.getMessagesArchived(this.chatForm.data.messages);
                this.activeIndexArchived = index;
                this.chatList.selectArchivedItem = true;
                this.saveLastVisitedSessionId(values.id, 'archive'); // 保存归档会话ID
            } else {
                data = await this.getMessages(this.chatForm.data.messages);
                this.activeIndex = index;
                this.chatList.selectArchivedItem = false;
                this.saveLastVisitedSessionId(values.id, 'active'); // 保存进行中会话ID
            }

            this.chatForm.loading = false;
            this.messages = data.list;
            this.$router.push({ query: { type, id: values.id } });
            this.$nextTick(() => {
                scrollToBottom('.chat-content-inner .conversation-inner');
            });
            if (values.modelName === 'gpt-4-32k' && type !== 'archive') {
                this.editSessionDialog('edit', index);
            }
        },

        handleTabChange(e) {
            const type = e.index === 0 ? 'active' : 'archive';
            const data = e.index === 0 ? this.sessions : this.sessionsArchived;

            // 获取上次访问的会话ID
            const lastVisitedId = this.getLastVisitedSessionId(type);
            let index = 0;

            if (lastVisitedId) {
                const idNum = parseInt(lastVisitedId, 10);
                const foundIndex = data.findIndex((item) => item.id === idNum);
                if (foundIndex !== -1) {
                    index = foundIndex;
                    // 直接设置正确的activeIndex
                    if (type === 'archive') {
                        this.activeIndexArchived = foundIndex;
                    } else {
                        this.activeIndex = foundIndex;
                    }
                }
            }

            this.getChatMessagesBySelect(data[index], index, type === 'active' ? undefined : 'archive');
        },

        selectSectionMore(event, values, index) {
            if (this.session.id === values.id) {
                this.dropdown.visibility = !this.dropdown.visibility;
            } else {
                this.dropdown.visibility = true;
            }
            this.dropdown.index = index;
            this.dropdown.style.left = `${event.clientX}px`;
            this.dropdown.style.top = `${event.clientY}px`;
        },
        editSessionDialog(type, index) {
            let options;
            const data = this.sessions[index];
            if (type === 'create') {
                options = {
                    title: '新建',
                    visibility: true,
                    data: {
                        type: 'create',
                        id: null,
                        name: '',
                        systemMessage: '',
                        model: this.availableModels[0],
                        temperature: 0.5,
                    },
                };
            } else {
                options = {
                    title: '编辑',
                    visibility: true,
                    data: {
                        type: 'edit',
                        id: data.id,
                        name: data.sessionName,
                        systemMessage: data.systemMessage,
                        model: data.modelName,
                        temperature: data.temperature,
                    },
                };
            }
            this.dialog.session = options;
        },
        async sumbitSessionDialog() {
            const values = this.dialog.session.data;
            const data = {
                id: values.id,
                modelName: values.model,
                sessionName: values.name,
                sessionType: 0,
                systemMessage: values.systemMessage,
                temperature: values.temperature,
            };
            if (!values.model) {
                this.$toast.add({
                    severity: 'warn',
                    summary: '请选择模型',
                    life: 3000,
                });
                return;
            }
            this.loading = true;
            if (values.type === 'create') {
                try {
                    await this.createSession(data);
                } catch (err) {
                    this.$toast.add({
                        severity: 'error',
                        summary: '错误',
                        detail: err.message,
                        life: 3000,
                    });
                    return;
                }
                this.init(true);
            } else {
                try {
                    await this.updateSession(data);
                } catch (err) {
                    this.$toast.add({
                        severity: 'error',
                        summary: '错误',
                        detail: err.message,
                        life: 3000,
                    });
                    return;
                }
                // Todo : 稍后修复, 当修改别的会话选项时, 不更新session
                this.session = data;
                this.init();
            }

            this.closeSessionDialog();
        },

        closeSessionDialog() {
            this.dialog.session.visibility = false;
            this.settingPopover = false;
            this.loading = false;
        },

        async deleteSessionDialog() {
            const _sessionIndex = this.dropdown.index;
            const _session = this.sessions[_sessionIndex];
            try {
                await this.deleteSession(_session.id);
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
            this.dialog.confirm.deleteSession.visibility = false;
            this.init(true);
        },
        async openSettingDialog() {
            this.dialog.setting.visibility = true;
            this.dialog.setting.data.enableSuggest = this.appConfig.enableSuggest;
            const data = await this.getIdeAuthCode();
            this.dialog.setting.data.ideAuthCode = data.code;
        },
        async sumbitSettingDialog() {
            this.$toast.add({
                severity: 'success',
                summary: '成功',
                detail: '修改全局配置成功!',
                life: 3000,
            });
        },

        async init(autoSelect) {
            await this.getSessions();
            await this.getSessionsArchived();

            if (autoSelect) {
                if (!this.sessions.length) return;
                await this.getChatMessagesBySelect(this.sessions[0], 0);
                return;
            }

            const { query } = this.$route;
            const { type, id } = query;
            let itemIndex = 0;
            let list;
            let targetId = id;

            // 根据type类型获取对应的lastVisitedId
            if (!targetId) {
                const lastVisitedId = this.getLastVisitedSessionId(type === 'archive' ? 'archive' : 'active');
                if (lastVisitedId) {
                    targetId = lastVisitedId;
                }
            }

            // 先设置正确的tabViewIndex，避免闪动
            this.tabViewIndex = type === 'archive' ? 1 : 0;

            // 确定要使用的列表和索引
            if (type === 'archive') {
                if (!this.sessionsArchived.length) return;
                list = this.sessionsArchived;
                if (targetId) {
                    const idNum = parseInt(targetId, 10);
                    const foundIndex = list.findIndex((item) => item.id === idNum);
                    if (foundIndex !== -1) {
                        itemIndex = foundIndex;
                        this.activeIndexArchived = foundIndex; // 直接设置正确的activeIndex
                    }
                }
            } else {
                if (!this.sessions.length) return;
                list = this.sessions;
                if (targetId) {
                    const idNum = parseInt(targetId, 10);
                    const foundIndex = list.findIndex((item) => item.id === idNum);
                    if (foundIndex !== -1) {
                        itemIndex = foundIndex;
                        this.activeIndex = foundIndex; // 直接设置正确的activeIndex
                    }
                }
            }

            // 直接设置当前会话，避免闪动
            this.session = list[itemIndex];

            // 获取消息内容
            await this.getChatMessagesBySelect(list[itemIndex], itemIndex, type);
        },
    },
    watch: {
        // 如果需要监听store中的特定状态
        settingPopover: {
            handler(newVal) {
                if (newVal) {
                    this.editSessionDialog('edit', this.activeIndex);
                }
            },
            immediate: true,
        },
    },
    async mounted() {
        await this.init();
        await this.getAvailableModels();
    },
};
</script>
<style lang="scss" scoped>
.chat-list {
    position: absolute;
    left: 32px;
    top: 32px;
    display: flex;
    width: 300px;
    height: calc(100% - 32px);
    flex-direction: column;
    justify-content: space-between;
    z-index: 10;
    background-color: #fff;
    font-size: 0.875rem;

    &__create-btn {
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        }
    }

    &__settings-btn {
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        }
    }

    &__scroll {
        flex: 1;
        overflow-y: auto;
        margin: 0.5rem 0;
    }

    &__tabs {
        :deep(.p-tabview-nav) {
            font-size: 1rem;
            border-bottom: 1px solid #e9e9ff;
        }

        :deep(.p-tabview-nav-link) {
            padding: 0.5rem 1rem;
            color: #6b7280;
            transition: all 0.2s ease;

            &:hover {
                color: #4f46e5;
                background-color: rgba(79, 70, 229, 0.05);
                border-color: transparent;
            }
        }

        :deep(.p-highlight .p-tabview-nav-link) {
            color: #4f46e5;
        }

        :deep(.p-tabview-panels) {
            padding: 1rem 0 0 0;
        }
    }

    &__session {
        margin: 0 0 0.8rem 0;
        position: relative;
        z-index: 9;
        transition: all 0.2s ease;

        &-item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border: 1px solid rgba(79, 70, 229, 0.1);
            border-radius: 10px;
            padding: 8px 36px 8px 14px;
            transition: all 0.2s ease;
            font-size: 1rem;
            background-color: rgba(250, 250, 255, 0.4);
        }

        &-icon {
            color: #4f46e5;
            font-size: 0.8rem;
            opacity: 0.7;
        }

        &-text {
            position: relative;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 20px;
            line-height: 20px;
            color: #26244ce0;
        }

        &-badge {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 99;
            font-size: 0.7rem;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 1.2rem;
        }

        &-more {
            position: absolute;
            display: flex;
            visibility: hidden;
            justify-content: center;
            align-items: center;
            top: 6px;
            right: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            z-index: 99;
            color: #4f46e5;
            transition: all 0.15s ease;

            &:hover {
                background-color: rgba(79, 70, 229, 0.15);
                color: #4f46e5;
            }
        }

        &:hover {
            cursor: pointer;

            .chat-list__session-item {
                background-color: rgba(244, 244, 255, 0.8);
                border-color: rgba(79, 70, 229, 0.3);
                box-shadow: 0 1px 3px rgba(79, 70, 229, 0.05);
            }

            .chat-list__session-more {
                visibility: visible;
            }
        }

        &--active {
            .chat-list__session-item {
                background-color: rgba(79, 70, 229, 0.08);
                border-color: rgba(79, 70, 229, 0.5);
                box-shadow: 0 2px 4px rgba(79, 70, 229, 0.15);
            }

            .chat-list__session-more {
                visibility: visible;
                background-color: rgba(79, 70, 229, 0.15);
            }
        }
    }

    &__form,
    &__form-group,
    &__confirm-body,
    &__settings-body {
        font-size: 1rem;
    }

    &__form-btn,
    &__confirm-btn {
        font-size: 1rem;
        border-radius: 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    &__temperature-value {
        font-size: 1rem;
    }

    &__meter-label {
        font-size: 1rem;
    }

    &__modal-header,
    &__confirm-header,
    &__settings-header {
        padding-bottom: 0.75rem;
    }

    &__modal-title,
    &__confirm-title,
    &__settings-title {
        font-size: 1rem;
        font-weight: 600;
    }

    &__search {
        margin: 0.7rem 0 0 0;

        :deep(.p-inputtext) {
            font-size: 1rem;
            border-radius: 8px;
            border-color: #e0e0e0;
            transition: all 0.2s ease;

            &:focus {
                border-color: #4f46e5;
                box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2);
            }
        }
    }

    // 优化表单样式
    &__form {
        &-group {
            margin-bottom: 1rem;
        }

        &-label {
            font-size: 1rem;
            color: #26244ce0;
        }

        &-input {
            :deep(.p-inputtext),
            :deep(.p-dropdown) {
                font-size: 1rem;
                border-radius: 6px;
                transition: all 0.2s ease;

                &:focus,
                &.p-focus {
                    border-color: #4f46e5;
                    box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2);
                }
            }

            :deep(.p-dropdown-panel) {
                font-size: 1rem;
            }
        }

        &-help {
            opacity: 0.6;
            transition: opacity 0.2s ease;

            &:hover {
                opacity: 1;
            }
        }
    }

    // 优化弹窗样式
    &__modal,
    &__confirm-modal,
    &__settings-modal {
        :deep(.modal-dialog) {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(79, 70, 229, 0.15);
        }
    }

    // 优化温度控制器
    &__meter-group {
        margin-top: 0.5rem;

        :deep(.p-meter) {
            height: 4px;
            border-radius: 2px;
        }
    }

    &__meter-labels {
        margin-top: 0.3rem;
    }

    &__temperature-value {
        font-size: 1rem;
        font-weight: 500;
    }

    // 优化按钮
    :deep(.p-button) {
        border-radius: 8px;
        transition: all 0.2s ease;

        &:not(.p-button-text):not(.p-button-link) {
            border: none;
            box-shadow: 0 1px 3px rgba(79, 70, 229, 0.15);

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba(79, 70, 229, 0.25);
            }

            &:active {
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba(79, 70, 229, 0.15);
            }
        }

        .p-button-icon {
            font-size: 0.875rem;
        }

        .p-button-label {
            font-weight: 500;
        }
    }

    // 修改滑块样式
    :deep(.p-slider) {
        .p-slider-handle {
            border-radius: 50%;
            border: 2px solid #4f46e5;
            background: #fff;
            width: 14px;
            height: 14px;
            margin-top: -6px;
            margin-left: -7px;
            transition: all 0.2s ease;

            &:hover {
                transform: scale(1.1);
            }
        }
    }
}
</style>

<style lang="scss">
.chat-list__scroll {
    .p-scrollpanel-bar-y {
        display: none;
        width: 2px !important;
        opacity: 0.7;
        background-color: rgba(79, 70, 229, 0.3) !important;
    }
}
</style>
