<template>
    <Transition name="slide-fade">
        <div v-if="visible" class="reply-complete-notification" @click="handleClick">
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="pi pi-check-circle"></i>
                </div>
                <div class="notification-text">
                    <div class="notification-title">{{ title }}</div>
                    <div class="notification-subtitle">{{ subtitle }}</div>
                </div>
                <div class="notification-close" @click.stop="handleClose">
                    <i class="pi pi-times"></i>
                </div>
            </div>
        </div>
    </Transition>
</template>

<script>
export default {
    name: 'Notification',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: 'AI 回答完成',
        },
        subtitle: {
            type: String,
            default: '点击查看',
        },
        autoClose: {
            type: Boolean,
            default: true,
        },
        closeDelay: {
            type: Number,
            default: 5000,
        },
    },
    emits: ['update:visible', 'click', 'close'],
    data() {
        return {
            autoCloseTimer: null,
        };
    },
    watch: {
        visible(newVal) {
            if (newVal && this.autoClose) {
                this.startAutoClose();
            } else {
                this.clearAutoClose();
            }
        },
    },
    methods: {
        handleClick() {
            this.$emit('click');
            this.close();
        },
        handleClose() {
            this.$emit('close');
            this.close();
        },
        close() {
            this.$emit('update:visible', false);
        },
        startAutoClose() {
            this.clearAutoClose();
            this.autoCloseTimer = setTimeout(() => {
                this.close();
            }, this.closeDelay);
        },
        clearAutoClose() {
            if (this.autoCloseTimer) {
                clearTimeout(this.autoCloseTimer);
                this.autoCloseTimer = null;
            }
        },
    },
    beforeUnmount() {
        this.clearAutoClose();
    },
};
</script>

<style lang="scss" scoped>
.reply-complete-notification {
    position: fixed;
    top: 100px;
    right: 30px;
    z-index: 9999;
    max-width: 320px;
    background: #ffffff;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0,1);

    /* 红色未读提示小点 */
    &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        width: 10px;
        height: 10px;
        background: linear-gradient(135deg, #ef4444, #dc2626);
        border-radius: 50%;
        box-shadow:
            0 0 0 2px rgba(255, 255, 255, 0.9),
            0 2px 6px rgba(239, 68, 68, 0.3);
        z-index: 1;
        animation: pulse-red 2s infinite;
    }

    .notification-content {
        display: flex;
        align-items: center;
        padding: 12px 14px;
        color: #1f2937;
    }

    .notification-icon {
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #22c55e, #16a34a);
        border-radius: 50%;
        box-shadow:
            0 4px 12px rgba(34, 197, 94, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);

        i {
            font-size: 1.1rem;
            color: #ffffff;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
    }

    .notification-text {
        flex: 1;

        .notification-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #111827;
            letter-spacing: -0.01em;
        }

        .notification-subtitle {
            font-size: 11px;
            color: #6b7280;
            opacity: 0.85;
            font-weight: 500;
        }
    }

    .notification-close {
        margin-left: 12px;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
        background: rgba(34, 197, 94, 0.05);
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            opacity: 1;
            background: rgba(34, 197, 94, 0.1);
            transform: scale(1.1);
        }

        i {
            font-size: 0.9rem;
            color: #6b7280;
        }
    }
}

/* 红色脉动动画 */
@keyframes pulse-red {
    0% {
        box-shadow:
            0 0 0 2px rgba(255, 255, 255, 0.9),
            0 0 0 5px rgba(239, 68, 68, 0.2);
    }
    50% {
        box-shadow:
            0 0 0 2px rgba(255, 255, 255, 0.9),
            0 0 0 10px rgba(239, 68, 68, 0.1);
    }
    100% {
        box-shadow:
            0 0 0 2px rgba(255, 255, 255, 0.9),
            0 0 0 5px rgba(239, 68, 68, 0.2);
    }
}

/* 动画效果 */
.slide-fade-enter-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
}

.slide-fade-leave-to {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .reply-complete-notification {
        right: 20px;
        top: 70px;
        max-width: 280px;

        .notification-content {
            padding: 12px 14px;
        }

        .notification-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;

            i {
                font-size: 1rem;
            }
        }

        .notification-text {
            .notification-title {
                font-size: 13px;
            }

            .notification-subtitle {
                font-size: 10px;
            }
        }
    }
}
</style>
