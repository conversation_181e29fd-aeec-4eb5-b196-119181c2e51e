<template>
    <div class="plugins">
        <TabView
            v-model:active-index="activeIndex"
            :pt="{
                panelContainer: { class: 'plugins__panel-container' },
            }"
        >
            <TabPanel
                v-for="(tab, index) in tabs"
                :key="tab.title"
                :header="tab.title"
            >
                <div v-if="tab.data.length" class="plugins__content">
                    <div
                        v-for="prompt in tab.data"
                        :key="prompt.id"
                        class="plugins__item fadein animation-duration-500"
                        @click="selectPrompt(prompt)"
                        @mouseover="prompt.isEditingIconVisible = true"
                        @mouseout="prompt.isEditingIconVisible = false"
                    >
                        <Button
                            class="plugins__item-btn plugins__item-btn--edit"
                            type="button"
                            label="编辑"
                            @click.stop="clickEditBtn(prompt)"
                            autofocus
                            :class="{
                                'plugins__item-btn--hidden':
                                    !prompt.isEditingIconVisible || index !== 1,
                            }"
                            size="small"
                        ></Button>
                        <Button
                            class="plugins__item-btn plugins__item-btn--use"
                            type="button"
                            label="使用"
                            @click.stop="selectPrompt(prompt)"
                            autofocus
                            :class="{
                                'plugins__item-btn--hidden':
                                    !prompt.isEditingIconVisible || index !== 0,
                            }"
                            size="small"
                        ></Button>
                        <div class="plugins__item-content">
                            <span class="plugins__item-title text-sm">{{
                                prompt.title
                            }}</span>
                            |
                            <span class="plugins__item-desc text-sm">{{
                                prompt.content
                            }}</span>
                        </div>
                    </div>
                </div>

                <EmptyTabContent
                    :dataLength="tab.data.length"
                    class="m-3"
                    v-else
                >
                    <template v-if="index === 0" #default>
                        <div class="plugins__empty">
                            暂无使用记录，快去
                            <span
                                class="plugins__empty-link"
                                @click="$emit('openPromptShop')"
                            >
                                指令商店
                            </span>
                            看看吧
                        </div>
                    </template>
                    <template v-if="index === 1" #create>
                        <Button
                            label="创建指令"
                            icon="pi pi-plus"
                            text
                            @click="createPrompts"
                        />
                    </template>
                    <template v-if="index === 2" #shop>
                        <div class="plugins__empty plugins__empty--shop">
                            暂不支持收藏指令，请去
                            <span
                                @click="$emit('openPromptShop')"
                                class="plugins__empty-link"
                                >指令商店</span
                            >
                            选择要使用的指令吧~
                        </div>
                    </template>
                </EmptyTabContent>
            </TabPanel>
        </TabView>
        <div class="plugins__actions">
            <Button
                label="创建指令"
                icon="pi pi-plus"
                @click="createPrompts"
                size="small"
                class="plugins__actions-btn"
            />
            <Button
                label="指令商店"
                icon="pi pi-shop"
                class="plugins__actions-btn plugins__actions-btn--shop"
                size="small"
                @click="$emit('openPromptShop')"
            />
        </div>
        <Dialog
            v-model:visible="dialog.visible"
            modal
            :style="{ width: '600px' }"
            @hide="resetForm"
            dismissableMask
            class="plugins__dialog"
        >
            <template #header>
                <div class="plugins__dialog-header">
                    <div>{{ dialog.title }}</div>
                </div>
            </template>
            <div class="plugins__dialog-form">
                <label for="title" class="plugins__dialog-label"
                    >指令名称</label
                >
                <InputText
                    id="title"
                    class="plugins__dialog-input"
                    placeholder="请填写指令名称"
                    v-model="form.title"
                    autocomplete="off"
                />
            </div>
            <div class="plugins__dialog-form">
                <label for="formContent" class="plugins__dialog-label"
                    >指令内容</label
                >
                <Textarea
                    v-model="form.content"
                    rows="5"
                    cols="30"
                    autoResize
                    placeholder="请填写指令内容"
                    class="plugins__dialog-textarea"
                />
            </div>
            <template #footer>
                <Button
                    v-if="dialog.type === 'update'"
                    class="plugins__dialog-btn plugins__dialog-btn--delete"
                    label="删除指令"
                    icon="pi pi-trash"
                    size="small"
                    @click="showDeletePop($event)"
                    severity="danger"
                />
                <Button
                    type="button"
                    label="取消"
                    severity="secondary"
                    @click="dialog.visible = false"
                    autofocus
                    class="plugins__dialog-btn plugins__dialog-btn--cancel"
                    size="small"
                ></Button>
                <Button
                    type="button"
                    label="保存"
                    @click="confirmCreate"
                    autofocus
                    size="small"
                    class="plugins__dialog-btn plugins__dialog-btn--save"
                    :disabled="!form.title || !form.content"
                ></Button>
            </template>
        </Dialog>
    </div>
</template>

<script>
import EmptyTabContent from './emptyTabContent.vue';
import { mapActions, mapWritableState } from 'pinia';
import useChatStore from '@/app/stores/chat';
import { ChatHistory } from '@/app/utils/index';
const promptsHistory = new ChatHistory('app-chatgpt-prompts-history');
export default {
    components: {
        EmptyTabContent,
    },
    data() {
        return {
            activeIndex: 0,
            tabs: [
                { title: '最近使用', data: [] },
                { title: '我创建的', data: [] },
                { title: '我收藏的', data: [] },
            ],
            dialog: { title: '创建指令', type: 'create', visible: false },
            form: {
                content: '',
                title: '',
            },
            currentPromptId: '',
        };
    },
    computed: {
        ...mapWritableState(useChatStore, ['']),
    },
    mounted() {
        this.init();
        this.initPromptHistory();
    },
    methods: {
        ...mapActions(useChatStore, [
            'getPrompts',
            'createPrompt',
            'deletePrompt',
            'updatePrompt',
        ]),
        async init() {
            try {
                const data = await this.getPrompts();
                this.tabs[1].data = [...data];
            } catch (error) {
                this.$toast.add({
                    severity: 'error',
                    summary: '请求失败',
                    detail: error.message,
                    life: 3000,
                });
            }
        },

        initPromptHistory() {
            promptsHistory.loadHistory();
            this.tabs[0].data = [...promptsHistory.historyList];
        },

        createPrompts() {
            this.dialog = {
                title: '创建指令',
                type: 'create',
                visible: 'true',
            };
        },

        // 选择指令
        selectPrompt(prompt) {
            // 将选中的指令传递给父组件，由父组件处理，如插入到输入框中
            this.$emit('selectPrompt', prompt);

            // 更新最近使用的指令
            this.updatePromptHistory(prompt);
        },

        updatePromptHistory(prompt) {
            promptsHistory.loadHistory();
            let historyList = [...promptsHistory.historyList];
            const index = historyList.findIndex(
                (item) => item.id === prompt.id
            );
            if (index !== -1) {
                historyList.splice(index, 1);
            }
            historyList.unshift(prompt);
            this.tabs[0].data = historyList;
            promptsHistory.setHistoryList(historyList);
        },

        // 确认创建/更新指令
        async confirmCreate() {
            try {
                if (this.dialog.type === 'update') {
                    await this.updatePrompt(
                        { id: this.currentPromptId },
                        this.form
                    );
                } else {
                    await this.createPrompt(this.form);
                }
                await this.init();
                this.dialog.visible = false;
                this.resetForm();
                this.$toast.add({
                    severity: 'success',
                    summary: '成功',
                    detail: `${this.dialog.title}成功`,
                    life: 3000,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
        },

        // 修改指令
        clickEditBtn({ id, content, title }) {
            this.dialog = {
                title: '编辑我的指令',
                type: 'update',
                visible: true,
            };
            this.form.title = title;
            this.form.content = content;
            this.currentPromptId = id;
        },

        // 删除指令
        showDeletePop(event) {
            this.$confirm.require({
                target: event.currentTarget,
                message: '确定删除当前指令吗？',
                icon: 'pi pi-exclamation-triangle',
                rejectClass: 'p-button-secondary p-button-outlined p-button-sm',
                acceptClass: 'p-button-sm p-button-danger',
                rejectLabel: '取消',
                acceptLabel: '确认',
                accept: this.confirmDelete,
            });
        },

        // 调用删除接口并更新列表
        async confirmDelete() {
            try {
                await this.deletePrompt({ id: this.currentPromptId });
                // 重新获取
                await this.init();
                this.dialog.visible = false;
                this.$toast.add({
                    severity: 'success',
                    summary: '删除成功',
                    detail: `${this.dialog.title}成功`,
                    life: 3000,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '删除错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
        },

        // 重置form表单状态
        resetForm() {
            this.form.content = '';
            this.form.title = '';
        },
    },
};
</script>
<style lang="scss" scoped>
:deep(.plugins__panel-container) {
    padding: 0;
}
:deep(.p-tabview-nav, .p-tabmenu-nav) {
    font-size: 0.875rem !important;
}

.plugins {
    position: relative;

    &__content {
        height: 200px;
        overflow-y: scroll;
    }

    &__item {
        width: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        position: relative;
        padding: 10px;
        padding-left: 75px;
        border-bottom: 1px solid #eee;
        font-size: 14px;

        &:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        &-content {
            max-width: 95%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        &-title {
            font-weight: bold;
        }

        &-desc {
            opacity: 0.6;
        }

        &-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);

            &--hidden {
                display: none !important;
            }
        }
    }

    &__empty {
        padding: 8px;

        &-link {
            color: #6366f1;
            font-weight: bold;
            cursor: pointer;
        }

        &--shop {
            text-align: center;
            padding: 10px 0;
            font-size: 14px;
        }
    }

    &__actions {
        right: 0;
        top: 5px;
        position: absolute;
        display: flex;
        align-items: center;

        &-btn {
            justify-content: center;
            display: flex;
            align-items: center;

            &--shop {
                margin: 0 10px;
            }
        }
    }

    &__dialog {
        &-header {
            font-weight: bold;
        }

        &-form {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        &-label {
            font-weight: 600;
            width: 6rem;
        }

        &-input,
        &-textarea {
            flex: 1;
        }

        &-btn {
            &--delete {
                margin-right: 12px;
            }

            &--cancel {
                margin-right: 12px;
            }
        }
    }
}
</style>
