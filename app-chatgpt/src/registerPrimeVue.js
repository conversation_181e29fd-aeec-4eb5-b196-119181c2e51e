import Button from 'primevue/button';
import Textarea from 'primevue/textarea';
import Badge from 'primevue/badge';
import Tab<PERSON>ie<PERSON> from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import Checkbox from 'primevue/checkbox';
import Dropdown from 'primevue/dropdown';
import InputText from 'primevue/inputtext';
import Dialog from 'primevue/dialog';
import ConfirmDialog from 'primevue/confirmdialog';
import Toast from 'primevue/toast';
import Card from 'primevue/card';
import Calendar from 'primevue/calendar';
import AutoComplete from 'primevue/autocomplete';
import Fieldset from 'primevue/fieldset';
import ProgressBar from 'primevue/progressbar';
import ConfirmPopup from 'primevue/confirmpopup';
import ToggleButton from 'primevue/togglebutton';
import Message from 'primevue/message';
import Sidebar from 'primevue/sidebar';
import SplitButton from 'primevue/splitbutton';
import RadioButton from 'primevue/radiobutton';
import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';
import Tag from 'primevue/tag';
import Chip from 'primevue/chip';
import Float<PERSON>abel from 'primevue/floatlabel';
import ScrollPanel from 'primevue/scrollpanel';
import SpeedDial from 'primevue/speeddial';
import Avatar from 'primevue/avatar';
import Panel from 'primevue/panel';

// Table
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ColumnGroup from 'primevue/columngroup'; // optional
import Row from 'primevue/row'; // optional

import OverlayPanel from 'primevue/overlaypanel';
import MeterGroup from 'primevue/metergroup';
import Slider from 'primevue/slider';
import Listbox from 'primevue/listbox';
import TabMenu from 'primevue/tabmenu';
import Chart from 'primevue/chart';
import ProgressSpinner from 'primevue/progressspinner';

const components = {
    OverlayPanel,
    FloatLabel,
    Button,
    Textarea,
    Badge,
    TabView,
    TabPanel,
    Checkbox,
    Dropdown,
    InputText,
    Dialog,
    ConfirmDialog,
    Toast,
    Card,
    Calendar,
    DataTable,
    Column,
    ColumnGroup,
    Row,
    AutoComplete,
    Fieldset,
    ProgressBar,
    ConfirmPopup,
    ToggleButton,
    Message,
    Sidebar,
    SplitButton,
    RadioButton,
    InputGroup,
    InputGroupAddon,
    Tag,
    Chip,
    ScrollPanel,
    SpeedDial,
    MeterGroup,
    Slider,
    Listbox,
    TabMenu,
    Avatar,
    Chart,
    Panel,
    ProgressSpinner
};

import Tooltip from 'primevue/tooltip';
const directives = {
    Tooltip,
};

export default function (app) {
    // 循环注册所有组件
    for (const [name, component] of Object.entries(components)) {
        app.component(name, component);
    }
    // 循环注册所有指令
    for (const [name, directive] of Object.entries(directives)) {
        app.directive(name, directive);
    }
}
