import dayjs from 'dayjs';

import copy from 'copy-to-clipboard';
import markdownToTxt from 'markdown-to-txt';

/**
 * 通过url判断当前环境 (本地 测试 线上)
 * @param {String} url
 */
export const detectEnv = function (url) {
    let result = '';
    const regIsLocalEnv = /localhost/;
    const regIsTestEnv = /\.mulan\./;
    const regIsOnlineEnv = /aidev.*\.corpautohome\.com/; // 预发和线上,都统一识别为线上

    if (url.search(regIsLocalEnv) > -1) {
        result = 'local';
    }
    if (url.search(regIsTestEnv) > -1) {
        result = 'test';
    }
    if (url.search(regIsOnlineEnv) > -1) {
        result = 'online';
    }
    return result;
};

/**
 * 节流函数
 *  在指定的时间间隔内只执行一次事件处理函数。即使在这个时间间隔内多次触发事件，也只执行一次
 * @param {*} fn
 * @param {*} delay
 */
export const throttle = function (fn, delay) {
    let prev = Date.now();
    return function () {
        const now = Date.now();
        if (now - prev >= delay) {
            fn.apply(this, arguments);
            prev = now;
        }
    };
};

// Copy to clipboard
export const copyToClipboard = function (input, type) {
    let result;
    // markdown to text
    if (type === 'text') {
        result = markdownToTxt(input);
    } else {
        result = input;
    }
    copy(result);
    return result;
};

export const noLoginRequired = function () {
    return location.search.indexOf('authCode') > -1 ? true : false;
};
export function scrollToBottom(selector, behavior = 'auto') {
    const element = document.querySelector(selector);
    if (element) {
        element.scrollTo({
            top: element.scrollHeight,
            behavior: behavior,
        });
    }
}

export const formatDate = function (dateString, format) {
    return dayjs(dateString).format(format || 'YYYY-MM-DD HH:mm');
};

export const updateLocalStorage = function (storageName, data) {
    const _storageStr = localStorage.getItem(storageName);
    const _storage = JSON.parse(_storageStr) || {};
    const _newStorage = Object.assign({}, _storage, data);
    localStorage.setItem(storageName, JSON.stringify(_newStorage));
};

export const getLocalStorage = function (storageName, name) {
    let result;
    const _storageStr = localStorage.getItem(storageName);
    const _storage = JSON.parse(_storageStr) || {};
    result = _storage[name];
    return result;
};

export const checkShortcuts = function (config, event) {
    let result = {
        history: {
            status: false,
            key: '',
        },
        magicSlash: false,
        enter: false,
        newline: false,
    };

    // 历史记录
    if ((event.key === 'ArrowUp' || event.key === 'ArrowDown') && event.ctrlKey && event.shiftKey) {
        result.history.status = true;
        result.history.key = event.key;
        return result;
    }

    // 简化一下写法。
    const isEnter = event.key === 'Enter';
    const configActions = {
        1: { enter: !event.shiftKey, newline: event.shiftKey },
        2: { enter: event.ctrlKey, newline: !event.ctrlKey },
        3: { enter: !event.ctrlKey, newline: event.ctrlKey },
        default: { enter: !event.shiftKey, newline: event.shiftKey },
    };

    const action = configActions[config] || configActions['default'];
    if (isEnter) {
        result.enter = action.enter;
        result.newline = action.newline;
    }

    return result;
};

export const diffAndCopy = function (target, source) {
    Object.keys(source).forEach((key) => {
        if (source[key] !== null && typeof source[key] === 'object') {
            // 如果属性是对象，则递归调用
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = Array.isArray(source[key]) ? [] : {};
            }
            diffAndCopy(target[key], source[key]);
        } else if (target[key] !== source[key]) {
            // 只有当属性值不相等时才复制
            target[key] = source[key];
        }
    });
};

/**
 * ChatHistory - 用户查看输入的历史记录
 */
export const ChatHistory = class {
    constructor(storageKey) {
        this.storageKey = storageKey || 'app-chatgpt-history';
        this.historyList = []; // 存储历史记录的数组
        this.index = -1;
        this.MAX_HISTORY_LENGTH = 20; // 最多存储 20 条历史记录
    }
    // Setter
    // Method

    // 从 localStorage 中加载历史记录
    loadHistory() {
        const historyJSON = localStorage.getItem(this.storageKey);
        if (historyJSON) {
            try {
                this.historyList = JSON.parse(historyJSON);
            } catch (error) {
                console.error('解析历史记录时出错:', error);
                // 可以设置默认值或处理错误
                this.historyList = [];
            }
        } else {
            // 如果找不到历史记录，可以初始化一个空数组或其他默认值
            this.historyList = [];
        }
    }

    // 将文本添加到历史记录
    addToHistory(item) {
        if (item === '') return;
        this.historyList.push(item);
        if (this.historyList.length > this.MAX_HISTORY_LENGTH) {
            this.historyList.shift();
        }
        this.saveHistory();
    }
    /**
     * handleHistory
     *   direction - 向上箭头为"-1", 向下箭头为"1",
     */
    handleHistory(direction) {
        if (this.historyList.length === 0) return;
        this.index = this.index === -1 ? this.historyList.length : this.index;

        if (this.index + direction > this.historyList.length) {
            return;
        } else if (this.index + direction < 0) {
            this.index = 0;
        } else {
            this.index = this.index + direction;
        }
        return this.historyList[this.index];
    }

    // 将历史记录保存到 localStorage
    saveHistory() {
        localStorage.setItem(this.storageKey, JSON.stringify(this.historyList));
    }

    // 直接全量替换历史记录
    setHistoryList(historyList) {
        localStorage.setItem(this.storageKey, JSON.stringify(historyList));
    }
};
