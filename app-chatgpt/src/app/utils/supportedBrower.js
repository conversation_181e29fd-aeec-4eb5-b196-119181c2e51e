var _ua = navigator.userAgent;
var _chromeVersion = _ua.match(/chrome\/([\d.]+)/i);
var _firefoxVersion = _ua.match(/firefox\/([\d.]+)/i);
var _safariVersion = _ua.match(/version\/([\d.]+).*safari/i);
var _edgeVersion = _ua.match(/edg\/([\d.]+)/i);
var _ieVersion = _ua.match(/msie\s([\d.]+)/i) || _ua.match(/trident.*rv:11./i);

// 假设layer特性在Chrome 99以下版本不支持
var _isLowChromeVersion = _chromeVersion && parseInt(_chromeVersion[1].split('.')[0]) < 99;

// 假设layer特性在Firefox 96以下版本不支持
var _isLowFirefoxVersion = _firefoxVersion && parseInt(_firefoxVersion[1].split('.')[0]) < 97;

// 假设layer特性在Safari 15.4以下版本不支持
var _isLowSafariVersion = _safariVersion && parseFloat(_safariVersion[1]) < 15.4;

// 假设layer特性在Edge 98以下版本不支持
var _isLowEdgeVersion = _edgeVersion && parseInt(_edgeVersion[1].split('.')[0]) < 99;

// 假设layer特性在所有IE版本不支持
var _isIE = _ieVersion !== null;

var _result = _isLowChromeVersion || _isLowFirefoxVersion || _isLowSafariVersion || _isLowEdgeVersion || _isIE;

export default _result;