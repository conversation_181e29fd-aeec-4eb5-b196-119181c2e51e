/**
 * markdown-it : md文档渲染组件
 */
import markdownit from 'markdown-it';
import hljs from 'highlight.js'; // https://highlightjs.org
import 'highlight.js/styles/github.css'; // 引入 highlight.js 的样式文件
import '@/app/assets/css/highlight-default.min.css'; // 本地引入default样式，避免从CDN加载
import '@/app/assets/css/markdownit.css';

// 禁用highlight.js自动加载CDN样式
hljs.configure({
    noAutodetect: true,
    cssSelector: 'none', // 防止自动加载样式
});

export const md = new markdownit({
    highlight: function (str, lang) {
        let code;
        if (lang && hljs.getLanguage(lang)) {
            try {
                code =
                    '<pre><code class="hljs">' +
                    hljs.highlight(str, {
                        language: lang,
                        ignoreIllegals: true,
                    }).value +
                    "</code><div class='copy-button text-sm font-medium bg-slate-100 text-white'>点击复制代码</div></pre>";
            } catch (err) {
                console.log(err);
            }
        } else {
            code = '<pre><code class="hljs">' + md.utils.escapeHtml(str) + '</code></pre>';
        }

        return code;
    },
});

/**
 * 用于修复GPT4-turbo, markdown格式嵌套错误
 * 见issue : https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/218
 */
export const fixMarkdownCodeBlocks = function (markdownString) {
    let result = markdownString;
    const regex = /```markdown[\s\S]*?(?:```[\s\S]*?```[\s\S]*?)*```/gm;
    if (regex.test(markdownString)) {
        result = markdownString.replace(/^```markdown/, '````markdown').replace(/```$/, '````');
    }
    return result;
};

/**
 * 处理 Markdown 中的图片加载失败情况
 * @param {string} html - 经过 markdown-it 渲染后的 HTML 字符串
 * @returns {string} - 处理后的 HTML 字符串
 */
export function handleMarkdownImages(html) {
    // 创建一个临时的 div 来解析 HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 为所有图片添加错误处理
    const images = tempDiv.getElementsByTagName('img');
    for (let img of images) {
        // 保存原始图片链接，用于提示
        const originalSrc = img.getAttribute('src');

        // 创建包装容器
        const wrapper = document.createElement('div');
        wrapper.style.position = 'relative';
        wrapper.style.display = 'inline-block';
        wrapper.style.textAlign = 'center';

        // 创建错误提示文本元素
        const errorText = document.createElement('div');
        errorText.style.display = 'none'; // 默认隐藏
        errorText.style.color = '#ff4d4f';
        errorText.style.fontSize = '12px';
        errorText.style.marginTop = '4px';
        errorText.style.wordBreak = 'break-all';
        errorText.style.maxWidth = '200px';
        errorText.textContent = '图片加载失败';

        // 将图片和错误提示文本添加到包装容器中
        img.parentNode.insertBefore(wrapper, img);
        wrapper.appendChild(img);
        wrapper.appendChild(errorText);

        // 添加错误处理
        img.setAttribute(
            'onerror',
            `
            this.src='https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-chatgpt/img_404.png';
            this.style.width = '50px';
            this.style.height = '50px';
            this.nextElementSibling.style.display = 'block';
            this.nextElementSibling.textContent = '图片加载失败: ${originalSrc}';
        `
        );
    }

    return tempDiv.innerHTML;
}
