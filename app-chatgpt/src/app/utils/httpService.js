import { noLoginRequired } from "@/app/utils/index"

// 定义一个自定义错误对象
class DevchatRequestFaild extends Error {
  constructor(message) {
    super(message);
    this.name = "DevchatRequestFaild";
  }
}
class DealerRequestFaild extends Error {
  constructor(message) {
    super(message);
    this.name = "DealerRequestFaild";
  }
}

async function get(url, params = null, headers = {}) {
  const options = {
    url,
    method: "GET",
    params,
    headers,
  };
  return request(options);
}

async function post(url, body = {}, params = null, headers = {}) {
  const options = {
    url,
    method: "POST",
    body,
    params,
    headers,
  };
  if (noLoginRequired()) {
    throw new Error('请求发送失败,请稍后再试');
  }
  return request(options);
}

async function put(url, body = {}, headers = {}) {
  const options = {
    url,
    method: "PUT",
    body,
    headers,
  };
  return request(options);
}

async function deleteRequest(url, body = {}, headers = {}) {
  const options = {
    url,
    method: "DELETE",
    body,
    headers,
  };
  return request(options);
}

async function request(options) {
  let _url = options.url;
  const _options = {
    method: options.method || "GET",
    headers: {
      // "Content-Type": "application/json",
      ...options.headers,
    },
  };
  if (options.params) {
    _url += "?" + new URLSearchParams(options.params).toString();
  }
  if (options.body) {
    // _options.body = JSON.stringify(options.body);

    // 检查是否为 FormData 对象，如果是，则不设置 Content-Type 并直接使用 FormData 作为请求体
    if (options.body instanceof FormData) {
      _options.body = options.body;
    } else {
      _options.body = JSON.stringify(options.body);
      _options.headers["Content-Type"] = "application/json";
    }   
  }
  try {
    const response = await fetch(_url, _options);
    // const data = await response.json();

    // 根据响应的 Content-Type 处理响应数据
    const contentType = response.headers.get("Content-Type");
    let data;
    if (contentType && contentType.includes("application/json")) {
      data = await response.json();
    } else {
      data = await response.blob(); // 处理二进制数据
    }

    if (response.ok) {
      if (data.returncode == 403) {
        location.reload();
      }
      // 非经销商标准协议
      if (data.status && data.status !== 'Success') {
        throw new DevchatRequestFaild(data.message);
      }
      // 经销商标准协议
      if (data.returncode && (data.returncode != 0)) {
        throw new DealerRequestFaild(data.message);
      }
    }
    // TODO : 稍后完善, 错误整理
    else {
      throw new Error(data.message);
    }
    return data;
  } catch (err) {
    console.error("request error:", err.message);
    throw err;
  }
}

export default {
  get,
  post,
  put,
  delete: deleteRequest, // 注意：delete 是保留字，需要别名导出
};
