/**
 * 更新 localStorage
 * @param {*} storageName
 * @param {*} data
 */
const updateLocalStorage = function (storageName, data) {
    const _storageStr = localStorage.getItem(storageName);
    let _newStorage;
    if (_storageStr === null) {
        _newStorage = data;
    } else {
        const _storage = JSON.parse(_storageStr) || {};
        _newStorage = Object.assign({}, _storage, data);
    }
    localStorage.setItem(storageName, JSON.stringify(_newStorage));
};

/**
 * 获取 localStorage
 * @param {*} storageName
 * @param {*} name
 */
const getLocalStorage = function (storageName, name) {
    let result = null;
    const _storageStr = localStorage.getItem(storageName);
    if (_storageStr === null) {
        return result;
    }
    // localStorage 存在时
    const _storage = JSON.parse(_storageStr);
    if (Array.isArray(_storage)) {
        result = _storage;
    } else {
        result = _storage[name];
    }
    return result;
};

export default {
    getLocalStorage,
    updateLocalStorage,
};
