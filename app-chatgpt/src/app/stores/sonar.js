import { defineStore } from 'pinia';
import httpService from '@/app/utils/httpService';

export default defineStore('sonar', {
    state: () => ({
        scanRecords: [], // 扫描记录列表
        totalRecords: 0, // 总记录数
        loading: false, // 加载状态
        sonarDetail: null, // Sonar详情数据
        issuesList: [], // 代码增量问题列表
        issuesTotal: 0, // 问题总数
        issuesLoading: false, // 问题列表加载状态
        gitUrls: [], // Git地址列表
        accounts: [], // 账户名列表
    }),
    actions: {
        /**
         * 获取Sonar扫描记录
         * @param {Object} params 查询参数
         * @param {string} params.startTime 起始时间 YYYY-MM-DD HH:mm:ss
         * @param {string} params.endTime 结束时间 YYYY-MM-DD HH:mm:ss
         * @param {string} [params.gitUrl] Git仓库地址（可选）
         * @param {string} [params.accountName] 账户名（可选）
         * @param {number} params.pageIndex 当前页码
         * @param {number} params.pageSize 每页条数
         * @returns {Promise<Object>} 扫描记录数据
         */
        async getSonarMessage(params) {
            this.loading = true;
            try {
                // 确保所有必需的参数都已提供
                const requiredParams = {
                    startTime: params.startTime,
                    endTime: params.endTime,
                    pageIndex: params.pageIndex || 1,
                    pageSize: params.pageSize || 10,
                };

                // 添加可选参数
                if (params.gitUrl) {
                    console.log(params.gitUrl, 'gitUrl');

                    requiredParams.gitUrl = params.gitUrl;
                }

                console.log(params, 'params');

                if (params.accountName) {
                    requiredParams.accountName = params.accountName;
                }

                const url = '/sonar-api/IncrCodeIssue/scans';
                const response = await httpService.post(url, requiredParams);

                // 将响应数据保存到state
                if (response && response.result) {
                    this.scanRecords = response.result.list || [];
                    this.totalRecords = response.result.rowcount || 0;
                }

                return response;
            } catch (err) {
                console.error('获取Sonar扫描记录失败:', err.message);
                // 直接抛出错误，让调用方处理
                throw err;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取Sonar详情数据
         * @param {number} projectExecutionId 扫描ID
         * @returns {Promise<Object>} 详情数据
         */
        async getSonarDetail(projectExecutionId) {
            this.loading = true;
            try {
                const url = `/sonar-api/IncrCodeIssue/statistics?projectExecutionId=${projectExecutionId}`;
                const response = await httpService.get(url);

                // 将响应数据保存到state
                if (response && response.result) {
                    this.sonarDetail = response.result;
                }

                return response;
            } catch (err) {
                console.error('获取Sonar详情失败:', err.message);
                // 直接抛出错误，让调用方处理
                throw err;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取代码增量问题列表
         * @param {Object} params 查询参数
         * @param {number} params.projectExecutionId 扫描ID
         * @param {number} params.pageIndex 当前页码
         * @param {number} params.pageSize 每页条数
         * @returns {Promise<Object>} 问题列表数据
         */
        async getSonarIssues(params) {
            this.issuesLoading = true;
            try {
                const url = '/sonar-api/IncrCodeIssue/issues';
                const response = await httpService.get(url, params);

                // 将响应数据保存到state
                if (response && response.result) {
                    this.issuesList = response.result.list || [];
                    this.issuesTotal = response.result.rowcount || 0;
                }

                return response;
            } catch (err) {
                console.error('获取代码增量问题列表失败:', err.message);
                // 直接抛出错误，让调用方处理
                throw err;
            } finally {
                this.issuesLoading = false;
            }
        },

        /**
         * 获取Git地址列表
         * @param {Object} params 查询参数
         * @param {string} params.words 搜索关键词
         * @returns {Promise<Array>} Git地址列表
         */
        async getGitUrls(params) {
            try {
                const url = '/api-cr/app/gitUrls';
                const response = await httpService.get(url, params);
                if (response && response.result) {
                    this.gitUrls = response.result;
                }
                return response.result;
            } catch (err) {
                console.error('获取Git地址列表失败:', err.message);
                throw err;
            }
        },

        /**
         * 获取账户名列表
         * @param {Object} params 查询参数
         * @param {string} params.words 搜索关键词
         * @returns {Promise<Array>} 账户名列表
         */
        async getAccounts(params) {
            try {
                const url = '/api-cr/oa/searchEmployees';
                const response = await httpService.get(url, params);
                if (response && response.result) {
                    this.accounts = response.result;
                }
                return response.result;
            } catch (err) {
                console.error('获取账户名列表失败:', err.message);
                throw err;
            }
        },
    },
});
