import { defineStore } from 'pinia';
import httpService from '@/app/utils/httpService';

export default defineStore('chat', {
  state: () => ({
    searchMessages: {
      // rowcount: 254,
      // pagecount: 26,
      // pageindex: 1,
      // list: [
      //   {
      //     id: 15495,
      //     sessionId: 546,
      //     sessionName: 'test2',
      //     questionRole: 'zhangjun1102',
      //     questionContent: 'java oom需要重启进程吗',
      //     questionRoleType: 1,
      //     questionTime: '2024-03-25T16:34:20.000+0800',
      //     createdStime: '2024-03-25T16:34:19.000+0800',
      //     modifiedStime: '2024-03-25T16:34:19.000+0800',
      //     isDel: 0,
      //     contextIds: null,
      //     answerRole: 'gpt-4-turbo',
      //     answerContent: '在Java中，当',
      //     answerRoleType: 2,
      //     answerTime: '2024-03-25T16:34:21.000+0800',
      //     messageStatus: 5,
      //     temperature: 0,
      //     messageType: 0,
      //     associatedQuestions: null,
      //     knowledgeId: 0,
      //     archived: 0,
      //     contextSelected: false,
      //   },
      // ],
    },
    availableModels: [
      // "gpt-4-turbo",
      // "gpt-4",
      // "gpt-3.5-turbo",
      // "dalle2"
    ],
    ideAuthCode: '',
    session: {},
    sessions: [
      // {
      //   id: 399,
      //   createdAccount: "biankai",
      //   sessionName: "test1",
      //   systemMessage: null,
      //   createdStime: "2023-07-28T15:35:43.000+0800",
      //   modelName: "gpt-4",
      //   temperature: 0.8,
      //   sessionType: 0,
      // },
    ],
    sessionsArchived: [],
    message: {},
    messages: [
      // {
      //   id: 6874,
      //   sessionId: 399,
      //   questionRole: "biankai",
      //   questionContent: "返回10字的文案 from client",
      //   questionRoleType: 1,
      //   questionTime: "2023-08-03T11:22:30.000+0800",
      //   createdStime: "2023-08-03T11:22:30.000+0800",
      //   modifiedStime: "2023-08-03T11:22:30.000+0800",
      //   isDel: 0,
      //   contextIds: "",
      //   answerRole: "gpt-4",
      //   answerContent:
      //     "亲爱的客户，感谢您的支持，我们将竭诚为您提供优质服务。**来自客户**：这是我们的十个字文案。亲爱的客户，我们致力于为您提供最优质的服务体验。亲爱的客户，欢迎使用我们的产品，感谢您的支持！**客户寄语**：感谢支持，诚挚合作，共创辉煌！亲爱的客户，您的支持是我们前进的动力。谢谢！亲爱的客户，您的满意是我们最大的追求！亲爱的客户，非常感谢您选择我们的产品，期待为您提供优质服务。**来自客户：** 优质服务，技术保障，值得信赖！**来自客户**：这次合作非常满意，期待下次再度合作！客户说：请务必提高效率，质量第一，共创美好未来！亲爱的客户，我们为您提供最专业的技术支持和优质服务。亲爱的客户，我们竭诚为您提供优质的服务体验。您好，这是一份客户提供的精彩文案，请查收！尊敬的客户，感谢您选择我们的产品与服务。亲爱的客户，我们提供优质服务，感谢您的支持！亲爱的客户，我们郑重承诺为您提供优质服务和高性价比产品。`from client` 指示可能有误，如果您需要10字的文案，请提供更详细的信息。亲爱的用户，我们已收到您的需求，将尽快为您提供满意方案。",
      //   answerRoleType: 2,
      //   answerTime: "2023-08-03T11:24:09.000+0800",
      //   messageStatus: 5,
      //   temperature: 0.8,
      //   messageType: 0,
      // },
    ],
    remarkSessions: {
      returncode: 0,
      message: '',
      result: {
        rowcount: 9,
        pagecount: 1,
        pageindex: 1,
        list: [
          // {
          //   "chatSessionId": 325,
          //   "sessionName": "uu",
          //   "sessionMarkType": 3,
          //   "sessionMarkName": "工作助手",
          //   "sessionMarkTagName": "工作助手",
          //   "createdAccount": "biankai",
          //   "createdStime": "2023-08-16 21:55:53",
          //   "sessionType": 0
          // }
        ],
      },
    },
    promptsList: [],
    settingPopover: false,
    sessionCurrentIndex: -1,
    projectInfo: {
      projectName: '',
      gitUrl: '',
      branchName: '',
      availableModels: []
    },
    scanInfo: {
      current: {
        commitId: '',
        triggerUser: '',
        completedTime: ''
      },
      previous: {
        commitId: '',
        triggerUser: '',
        completedTime: ''
      }
    },
    metricsComparison: {
      bugs: { current: 0, previous: 0, trend: 0 },
      vulnerabilities: { current: 0, previous: 0, trend: 0 },
      codeSmells: { current: 0, previous: 0, trend: 0 },
      coverage: { current: 0, previous: 0, trend: 0 }
    },
    issuesList: {
      total: 0,
      pageSize: 10,
      pageIndex: 1,
      records: []
    }
  }),
  getters: {
    getMessageIds(state) {
      let result = [];
      let messages = state.messages.slice();
      const len = messages.length;

      return function (num) {
        if (num >= 0 && num < len) {
          messages = messages.slice(len - num);
        }
        messages.forEach((value) => {
          result.push(value.id);
        });
        return result;
      };
    },
  },
  actions: {
    async getSearchMessages(params) {
      const url = '/api/searchMessage';
      try {
        const data = await httpService.get(url, params);
        this.searchMessage = data.result;
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async getAvailableModels() {
      const url = '/api/availableModels';
      try {
        const data = await httpService.get(url);
        this.availableModels = data.result;
        return data.result;
      } catch (err) {
        console.error(err.message);
        this.availableModels = [
          'gpt-4-turbo',
          'gpt-4',
          'gpt-3.5-turbo',
          'dalle2',
        ];
        throw err;
      }
    },
    async getIdeAuthCode() {
      const url = '/api/ideAuthCode';
      try {
        const data = await httpService.get(url);
        this.ideAuthCode = data.data.code;
        return data.data;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async getSessions() {
      const url = '/api/chatUsedSessions';
      try {
        const data = await httpService.get(url);
        this.sessions = data.data;
        return data;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async getSessionsArchived() {
      const url = '/api/chatSessions';
      try {
        const data = await httpService.get(url);
        this.sessionsArchived = data.data;
        return data;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async createSession(body) {
      const url = '/api/createSession';
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async updateSession(body) {
      const url = '/api/updateSession';
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async deleteSession(params) {
      const url = '/api/noUseSession';
      try {
        const data = await httpService.post(url, params);
        this.session = {};
        this.messages = [];
        return data;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async getMessages(params) {
      const url = '/api/paginatedUsingMessages';
      try {
        const data = await httpService.get(url, params);
        data.result.list.reverse();
        return data.result;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async getMessagesArchived(params) {
      const url = '/api/paginatedArchivedMessages';
      try {
        const data = await httpService.get(url, params);
        data.result.list.reverse();
        return data.result;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async createMessage(body) {
      const url = '/api/createMessage';
      let res;
      try {
        res = await httpService.post(url, body);
        return res;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async updateArchivedMessage(body) {
      const url = '/api/archiveMessages';
      let res;
      try {
        res = await httpService.post(url, body);
        return res;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async updateMessageContextSelected(body) {
      const url = '/api/updateMessageContextSelected';
      let res;
      try {
        res = await httpService.post(url, body);
        this.session.contextIds = res.result;
        this.messages.forEach(async (item) => {
          item.contextSelected = res.result.includes(item.id);
        });
        return res;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
    async clearMessageContextSelected(body) {
      const url = '/api/clearMessageContextSelected';
      let res;
      try {
        res = await httpService.post(url, body);
        this.session.contextIds = res.result;
        this.messages.forEach(async (item) => {
          item.contextSelected = res.result.includes(item.id);
        });
        return res;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 获取提示词列表
    async getPrompts() {
      const url = '/api/getPrompts';
      try {
        const data = await httpService.get(url);
        this.promptsList = data.result;
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 创建提示词
    async createPrompt(body) {
      const url = '/api/createPrompt';
      try {
        const data = await httpService.post(url, body);
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 删除提示词
    async deletePrompt(params) {
      const url = `/api/deletePrompt/${params.id}`;
      try {
        const data = await httpService.delete(url);
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 更新提示词
    async updatePrompt(params, body) {
      const url = `/api/updatePrompt/${params.id}`;
      try {
        const data = await httpService.put(url, body);
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 上传图片
    async uploadImage(body) {
      const url = '/file-api/uploadImg';
      try {
        const data = await httpService.post(url, body);
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

     // 获取图片链接
     async getUploadedImage(img) {
      try {
        const data = await httpService.get(`/file-api/img/${img}`);
        return data;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },

    // 获取项目信息
    async getProjectInfo() {
      try {
        const { data } = await httpService.get('/api/sonar/project/info');
        if (data.code === 200) {
          this.projectInfo = data.data.projectInfo;
          this.availableModels = data.data.projectInfo.availableModels;
        }
        return data;
      } catch (err) {
        console.error('获取项目信息失败:', err.message);
        throw err;
      }
    },

    // 获取扫描信息
    async getScanInfo() {
      try {
        const { data } = await httpService.get('/api/sonar/scan/comparison');
        if (data.code === 200) {
          this.scanInfo = data.data.scanInfo;
          this.metricsComparison = data.data.metricsComparison;
        }
        return data;
      } catch (err) {
        console.error('获取扫描信息失败:', err.message);
        throw err;
      }
    },

    // 获取问题列表
    async getIssuesList(params = {}) {
      try {
        const { data } = await httpService.get('/api/sonar/issues/list', {
          params: {
            pageSize: params.pageSize || 10,
            pageIndex: params.pageIndex || 1,
            type: params.type,
            severity: params.severity,
            status: params.status
          }
        });
        if (data.code === 200) {
          this.issuesList = data.data;
        }
        return data;
      } catch (err) {
        console.error('获取问题列表失败:', err.message);
        throw err;
      }
    }
  },
});
