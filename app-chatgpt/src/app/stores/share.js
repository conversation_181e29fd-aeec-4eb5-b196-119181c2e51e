import { defineStore } from 'pinia';
import httpService from '@/app/utils/httpService';

export default defineStore('share', {
  state: () => ({
    message: {
      shareAccount: '',
      shareEmployeeName: '',
      createdStime: '',
      messages: [],
    },
  }),
  actions: {
    async getShareMessage(params) {
      const url = '/api/getShareMessage';
      try {
        const data = await httpService.get(url, params);
        this.message = data.result;
        return data;
      } catch (err) {
        console.error('Error fetching data:', err.message);
      }
    },
    async createShareMessage(body) {
      const url = '/api/createShareMessage';
      try {
        const data = await httpService.post(url, body);
        return data.result;
      } catch (err) {
        console.error(err.message);
        throw err;
      }
    },
  },
});
