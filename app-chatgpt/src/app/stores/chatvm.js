import { defineStore } from 'pinia';
import useAppStore from './app';

export default defineStore('chatvm', {
    state: () => ({
        chatList: {
            selectArchivedItem: false,
        },
        chatForm: {
            /**
             * 聊天区域 - 滚动事件相关
             */
            lastScrollTop: 0, // 存储上一次的滚动位置
            loadedAllMessages: false, // 是否全部加载完成(无最新消息)

            loading: false,
            // 解决用户在中文输入法状态下, 输入回车, 信息就发送了. (而此时可能还有要输入的内容)
            isCompositionEnd: true,
            data: {
                message: '',
                // 获取会话消息列表 - 请求参数
                // chatContent滚动加载和 chatList点击会话, 会用到
                messages: {
                    chatSessionId: 0,
                    pageIndex: 1,
                    pageSize: 8,
                },
            },
            schema: {
                message: 'required',
            },
            textarea: {},
            submit: {
                disabled: false,
            },
            eventSource: {
                close: function () {},
            },
            stop: {
                visibility: false,
            },
            conversationDecorationBar: false,
            user_uploaded_image: '',
        },
    }),
    actions: {
        stopSendMessage() {
            const appStore = useAppStore();
            this.chatForm.eventSource.close();
            this.chatForm.stop.visibility = false;
            this.chatForm.submit.disabled = false;
            appStore.reportUserEvent('click_appChatgpt_stopMessage');
        },
    },
});
