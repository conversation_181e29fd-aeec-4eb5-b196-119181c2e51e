import { defineStore } from 'pinia';
import httpService from '@/app/utils/httpService';

export default defineStore('codereview', {
    state: () => ({
        apps: [
            // {
            //   "appId": 252,
            //   "appName": "autojob"
            // },
        ],
        gitUrls: [
            // 原始格式
            // "https://git.corpautohome.com/GP_RD_OPS_PAAS/job_exe.git",
            // 加工后 格式
            // {
            //   label : "", value:""
            // }
        ],
        depts: [
            // {
            //   "deptId": "********",
            //   "deptName": "i车商技术团队"
            // }
        ],
        employees: [
            // {
            //   "deptId": "********",
            //   "deptName": "法务部",
            //   "hrCode": "1244",
            //   "employeeName": "周珊珊",
            //   "accountName": "zhoushanshan"
            // }
        ],
        codeCommitPage: {
            list: [],
            // "rowcount": 0,
            // "pagecount": 0,
            // "pageindex": 1,
            // "list": [
            //   {
            //     "createdAt": "2023-11-06T20:30:43.000+0800",
            //     "deptName": "数据架构团队",
            //     "appNames": "gpt-api",
            //     "gitUrl": "https://git.corpautohome.com/dealer-arch/gpt-api.git",
            //     "commitEmployeeName": "张俊",
            //     "commitSha": "de6ad01fe55ccb4c158a680fb7d8369b9d19afe2"
            //   }
            // ]
        },
        codeCommitDetail: {
            // "createdAt": "2023-11-06T20:30:43.000+0800",
            // "deptName": "数据架构团队",
            // "appNames": "gpt-api",
            // "gitUrl": "https://git.corpautohome.com/dealer-arch/gpt-api.git",
            // "commitEmployeeName": "张俊",
            // "commitSha": "de6ad01fe55ccb4c158a680fb7d8369b9d19afe2",
            // "diffCount": 4,
            // "reviewCount": 3,
            // "commitLink": "https://git.corpautohome.com/dealer-arch/gpt-api/commit/de6ad01fe55ccb4c158a680fb7d8369b9d19afe2",
            // "diffs": [{
            //   "gptOptimizationSuggestion": "在代码的diff结果中，发现了一个可能的逻辑错误。在原始代码中，两次检查了同一个集合`enableServerUrls`是否为空，而在新的代码中，第二次检查的是`disableServerUrls`。这可能是一个改进，因为在原始代码中，如果`disableServerUrls`为空，那么不会执行`updateStatusByServerUrls`方法，可能会导致一些预期之外的行为。\n\n以下是改进后的代码示例：\n\n```java\nif (CollectionUtils.isNotEmpty(enableServerUrls)) {\n    result += this.updateStatusByServerUrls(serverEnv, serverType, enableServerUrls, IsDelConst.FALSE_BYTE);\n}\nif (CollectionUtils.isNotEmpty(disableServerUrls)) {\n    result += this.updateStatusByServerUrls(serverEnv, serverType, disableServerUrls, IsDelConst.TRUE_BYTE);\n}\nreturn result;\n```\n\n这样，无论`enableServerUrls`和`disableServerUrls`哪个为空，都不会影响另一个的处理。",
            //   "acceptedType": 0,
            //   "acceptedReasonContent": null,
            //   "acceptedAccount": null,
            //   "diff": "--- a/source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\n+++ b/source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\n@@ -81,7 +81,7 @@ public class CargptDealerGptServerInfoDao implements ICargptDealerGptServerInfoD\n         if (CollectionUtils.isNotEmpty(enableServerUrls)) {\n             result += this.updateStatusByServerUrls(serverEnv, serverType, enableServerUrls, IsDelConst.FALSE_BYTE);\n         }\n-        if (CollectionUtils.isNotEmpty(enableServerUrls)) {\n+        if (CollectionUtils.isNotEmpty(disableServerUrls)) {\n             result += this.updateStatusByServerUrls(serverEnv, serverType, disableServerUrls, IsDelConst.TRUE_BYTE);\n         }\n         return result;\n",
            //   "codeCommitDiffId": 16868,
            //   "diffLink": "https://git.corpautohome.com/dlr_gpt/unit-gpt-api/commit/dab1de8e2f1ae405683882e0a30a741a808203df#a96370219cdbf1fdea90bec119adda530d77af9e",
            //   "fileLink": "https://git.corpautohome.com/dlr_gpt/unit-gpt-api/blob/dab1de8e2f1ae405683882e0a30a741a808203df/source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java",
            //   "filePath": "source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java",
            //   "commitLink": null,
            //   "reviewStatus": 2,
            //   "suggestionType": null,
            //   "promptSysMsg": "你是一个软件架构设计专家，精通Java开发，你需要根据开发人员本次提交版本和历史版本的diff结果查找缺陷并给出改进建议。你给的改进建议是陈述性的输出，将直接关联到Git版本管理系统，在称呼上和第一人称和第二人称的对话方式有一定的区别。请认真阅读给你的输入内容按照要求返回内容。",
            //   "prompt": "你是一个软件架构设计专家，精通Java开发，你需要根据开发人员本次提交版本和历史版本的diff结果查找缺陷并给出改进建议。你的改进建议时需要严格遵守以下要求：\n1、只对比较明显和严重的代码问题给出改进建议，否则不要给出改进建议，请务必遵守！\n2、对于很简洁或者无明显问题的代码片段，不要给出改进建议。\n3、在没有明确证据表明会抛出异常时，不要给出异常处理方面的改进建议。\n4、由于给你的只是代码片段，在你无法知道代码片段中依赖的具体内容时，请假设依赖内容是正确合理的。\n5、在无法知道代码的变更具体原因时，假设代码的变更意图是合理的。\n6、给出的建议中尽量包含问题代码和改进后的示例代码。\n7、请减少复述性输出。\n8、你给的建议是陈述性的输出，将直接关联到Git版本管理系统，在称呼上和第一人称和第二人称的对话方式有一定的区别。\n你的输出是一个结构化的数据： suggestion_type|suggestion_description，其中suggestion_type是一个枚举类型，只可为\"have_suggestion\",\"no_suggestion\"两种。当你认为代码没有改进建议时，suggestion_type的值为no_suggestion；当你认为代码有改进建议时，suggestion_type的值为have_suggestion，并且将改进建议用markdown格式输出放到suggestion_description字段。\n你的输出应该是以下几种：\n示例一无改进建议：no_suggestion|\n示例二有改进建议：have_suggestion|markdown格式的改进建议详情\n从该行往下的所有内容为git diff接口返回的diff结果：\n {\"old_path\":\"source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\",\"new_path\":\"source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\",\"a_mode\":\"100644\",\"b_mode\":\"100644\",\"new_file\":false,\"renamed_file\":false,\"deleted_file\":false,\"diff\":\"--- a/source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\\n+++ b/source/unit-gpt-api-provider/src/main/java/com/autohome/unit/gpt/api/provider/context/cargpt/dao/mysql/dao/impl/CargptDealerGptServerInfoDao.java\\n@@ -81,7 +81,7 @@ public class CargptDealerGptServerInfoDao implements ICargptDealerGptServerInfoD\\n         if (CollectionUtils.isNotEmpty(enableServerUrls)) {\\n             result += this.updateStatusByServerUrls(serverEnv, serverType, enableServerUrls, IsDelConst.FALSE_BYTE);\\n         }\\n-        if (CollectionUtils.isNotEmpty(enableServerUrls)) {\\n+        if (CollectionUtils.isNotEmpty(disableServerUrls)) {\\n             result += this.updateStatusByServerUrls(serverEnv, serverType, disableServerUrls, IsDelConst.TRUE_BYTE);\\n         }\\n         return result;\\n\"}",
            //   "acceptedReasonList": []
            // }]
        },
        acceptedReasonList: [
            //   {
            //     "acceptedReasonId": 21,
            //     "acceptedReasonName": "错误建议",
            //     "active" : false
            //   }
        ],
        weeklyReport: {
            weeklyDepartments: [],
            weeklySuggestions: [],
            weeklyEmployees: [],
        },

        weeklyReportYear: [],
        weeklyReportWeek: [],

        evaluationSeveritys: {
            优秀: 'info',
            合理: 'success',
            无营养: 'warning',
            错误: 'danger',
        },
    }),
    getters: {},
    actions: {
        async initApp() {},
        async getApps(params) {
            const url = '/api-cr/app/apps';
            try {
                const data = await httpService.get(url, params);
                this.apps = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getGitUrls(params) {
            const url = '/api-cr/app/gitUrls';
            try {
                const data = await httpService.get(url, params);
                this.gitUrls = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getDeptList() {
            const url = '/api-cr/oa/deptList';
            try {
                const data = await httpService.get(url);
                this.depts = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getEmployees(params) {
            const url = '/api-cr/oa/searchEmployees';
            try {
                const data = await httpService.get(url, params);
                this.employees = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getCodeCommitPage(body) {
            let data;
            const url = '/api-cr/codeCommit/page';
            try {
                data = await httpService.post(url, body);
            } catch (err) {
                console.log(err);
                throw err;
            }
            this.codeCommitPage = data.result;
            return data.result;
        },
        async getCodeCommitDetail(params) {
            let data;
            const url = '/api-cr/codeCommit/detail';
            try {
                data = await httpService.get(url, params);
            } catch (err) {
                console.log(err);
                throw err;
            }
            data.result.diffs.map((item) => {
                const prompt = item.prompt;
                const reg = /\{"old_path"/;
                let startIndex = prompt.search(reg);
                if (startIndex == -1) {
                    startIndex = 0;
                }
                item.promptToClipboard = prompt.slice(
                    startIndex,
                    prompt.length
                );
                return item;
            });
            data.result.diffs.forEach((diff) => {
                diff.suggestions.forEach((suggestion) => {
                    switch (suggestion.evaluationType) {
                        case 0:
                            suggestion.evaluationButton = '待评价';
                            suggestion.evaluationButtonSeverity = '';
                            break;
                        case 1:
                            suggestion.evaluationButton = '优 秀';
                            suggestion.evaluationButtonSeverity = 'info';
                            break;
                        case 2:
                            suggestion.evaluationButton = '合 理';
                            suggestion.evaluationButtonSeverity = 'success';
                            break;
                        case 3:
                            suggestion.evaluationButton = '无营养';
                            suggestion.evaluationButtonSeverity = 'warning';
                            break;
                        case 4:
                            suggestion.evaluationButton = '错 误';
                            suggestion.evaluationButtonSeverity = 'danger';
                            break;
                        default:
                            suggestion.evaluationButton = '待评价';
                            suggestion.evaluationButtonSeverity = '';
                            break;
                    }
                    suggestion.evaluationRadio = suggestion.evaluationType + '';
                    suggestion.evaluationText = suggestion.evaluationContent;
                });
            });
            this.codeCommitDetail = data.result;
            return data.result;
        },
        async updateAcceptSuggestion(body) {
            const url = '/api-cr/codeCommit/acceptedSuggestion';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },
        async evaluate(body) {
            const url = '/api-cr/codeCommit/evaluate';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },
        async getAcceptedReasonList() {
            let data;
            const url = '/api-cr/codeCommit/acceptedReasonList';
            try {
                data = await httpService.get(url);
            } catch (err) {
                console.log(err);
                throw err;
            }
            this.acceptedReasonList = data.result;
            return data.result;
        },
        async updateAcceptedReason(body) {
            const url = '/api-cr/codeCommit/acceptedReason';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getWeeklyReport(params) {
            const url = '/api-cr/codeCommit/weeklyReport';
            try {
                const data = await httpService.get(url, params);
                this.weeklyReport = data.result;
                this.weeklyReport.weeklyDepartments.forEach((department) => {
                    department.commitPushCountSuggestedUnreadRate = this.rate(
                        department.commitPushCountSuggestedUnread,
                        department.commitPushCountSuggested
                    );
                    department.commitPushCountSuggestedReadRate = this.rate(
                        department.commitPushCountSuggestedRead,
                        department.commitPushCountSuggested
                    );

                    department.suggestionCountReadUnevaluatedRate = this.rate(
                        department.suggestionCountReadUnevaluated,
                        department.suggestionCountRead
                    );
                });
                this.weeklyReport.weeklySuggestions.forEach((suggestion) => {
                    suggestion.suggestionCountUnreadRate = this.rate(
                        suggestion.suggestionCountUnread,
                        suggestion.suggestionCount
                    );
                    suggestion.suggestionCountReadRate = this.rate(
                        suggestion.suggestionCountRead,
                        suggestion.suggestionCount
                    );

                    suggestion.suggestionCountReadUnevaluatedRate = this.rate(
                        suggestion.suggestionCountReadUnevaluated,
                        suggestion.suggestionCountRead
                    );
                });

                this.weeklyReport.weeklyEmployees.forEach((employee) => {
                    employee.commitPushCountSuggestedUnreadRate = this.rate(
                        employee.commitPushCountSuggestedUnread,
                        employee.commitPushCountSuggested
                    );

                    employee.commitPushCountSuggestedReadRate = this.rate(
                        employee.commitPushCountSuggestedRead,
                        employee.commitPushCountSuggested
                    );

                    employee.suggestionCountReadUnevaluatedRate = this.rate(
                        employee.suggestionCountReadUnevaluated,
                        employee.suggestionCountRead
                    );
                });

                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getWeeklyReportYear() {
            const url = '/api-cr/codeCommit/weeklyReportYear';
            try {
                const data = await httpService.get(url);
                this.weeklyReportYear = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        async getWeeklyReportWeek(params) {
            const url = '/api-cr/codeCommit/weeklyReportWeek';
            try {
                const data = await httpService.get(url, params);
                this.weeklyReportWeek = data.result;
                return data.result;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },
        rate(part, totle) {
            return totle == 0 ? -1 : part / totle;
        },
    },
});
