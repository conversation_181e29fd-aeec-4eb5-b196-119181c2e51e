import { defineStore } from 'pinia';
import { watch } from 'vue';
import httpService from '@/app/utils/httpService';
import { noLoginRequired, detectEnv } from '@/app/utils/index';

export default defineStore('app', {
    state: () => ({
        username: '',
        appConfig: {
            version: '1.1.0',
            contextNum: '0', // -1代表全部
            autoContext: true,
            // 是否开启推荐
            enableSuggest: '0', // 1 开启推荐
            shortcuts: {
                sendChatMessage: '1',
                lineWrap: 'shift_enter',
                history: {
                    prev: 'shift_up',
                    next: 'shift_down',
                },
                magic: '/',
            },
        },
        env: '',
        // 免登
        noLoginRequired: noLoginRequired(),
    }),
    getters: {},
    actions: {
        async initApp() {
            const data = await this.getUsername();
            this.username = data.username;
            this.env = detectEnv(location.host); // 设置 env

            // 判断localStorage 是否存在 :
            //    Y -> localStorage to appConfig
            //    N -> appConfig to localStorage
            const appConfigStr = localStorage.getItem('app-chatgpt');
            if (appConfigStr) {
                // 兼容性处理 : 之前版本 appConfig 中 contextNum, enableSuggest,sendChatMessage 用的是Number类型
                // 当localStorage中没有 "version :1.1.0"时, 主动清理下localStorage
                if (!JSON.parse(appConfigStr).version) {
                    localStorage.removeItem('app-chatgpt');
                    localStorage.setItem('app-chatgpt', JSON.stringify(this.appConfig));
                } else {
                    this.setAppConfig(JSON.parse(appConfigStr));
                }
            } else {
                localStorage.setItem('app-chatgpt', JSON.stringify(this.appConfig));
            }
            this.setupLocalStorage();
        },

        setupLocalStorage() {
            // 监听 : 当 appConfig变化时, 自动更新 localStorage. 好处是不需要在同时维护localStorage
            watch(
                () => this.appConfig,
                (newValue) => {
                    localStorage.setItem('app-chatgpt', JSON.stringify(newValue));
                },
                // options
                { deep: true }
            );
        },

        setAppConfig(data) {
            const _data = Object.assign({}, this.appConfig, data);
            localStorage.setItem('app-chatgpt', JSON.stringify(_data));
            this.appConfig = _data;
        },

        /**
         * 获取用户名
         * @returns {Promise}
         */
        async getUsername() {
            const url = '/sso/username';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error('Error fetching data:', err.message);
            }
        },

        /**
         * 埋点上报
         * @param {String} name
         */
        async reportUserEvent(name) {
            const env = detectEnv(location.host);
            if (env === 'local' || env === 'online') {
                try {
                    await fetch('/stat/employeeEvent', {
                        method: 'Post',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            accountName: this.username,
                            eventName: name,
                        }),
                    });
                } catch (error) {
                    // 静默处理埋点上报错误，不影响主业务流程
                    console.warn('埋点上报失败:', error.message);
                }
            }
        },
    },
});
