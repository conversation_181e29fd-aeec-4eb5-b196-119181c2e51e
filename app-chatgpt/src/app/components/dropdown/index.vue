<template>
  <div class="dropdown-menu" :class="{ show: visibility }" style="
      position: absolute;
      will-change: transform;
      top: 0px;
      left: 0px;
      transform: translate3d(133px, 0px, 0px);
    " :style="style">
    <Button label="编辑"  class="w-full" @click="editSession" size="small"/>
    <Button label="删除"  severity="danger" @click="deleteSession" size="small"/>
  </div>
</template>

<script>
export default {
  name: "Dropdown",
  props: {
    visibility: {
      type: Boolean,
      required: true,
    },
    style: {
      type: Object,
      required: true,
    },
  },
  emits: ["edit-session", "delete-session", "close"],
  methods: {
    editSession() {
      this.$emit("edit-session");
      this.$emit("close");
    },
    deleteSession() {
      this.$emit("delete-session");
      this.$emit("close");
    },
    closeDropdown(event) {
      var target = event.target; // 获取被点击的元素
      if (target.classList.contains('material-icons') && target.closest('.more')) {
        return;
      } else {
        this.$emit("close");
      }
    }
  },
  watch: {
    visibility(newVal) {
      if (newVal === true) {
        document.body.addEventListener('click', this.closeDropdown)
      } else {
        document.body.removeEventListener('click', this.closeDropdown)
      }
    }
  }
};
</script>

<style lang="scss">
@import "./index.scss";
</style>
