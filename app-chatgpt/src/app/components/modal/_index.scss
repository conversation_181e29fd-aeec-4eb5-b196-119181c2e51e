.modal {
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  z-index: 1000;
  .modal-dialog {
    position: relative;
    width: auto;
    background-color: #fff;
    border: 0;
    box-shadow: 0 3px 10px rgba(62, 85, 120, 0.045);
    border-radius: 4px;
    z-index: 1002;
    transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
    .modal-header {
      display: flex;
      justify-content: space-between;
      // align-items: flex-start;
      align-items: center;
      padding: 25px 25px 20px;
      border-top-left-radius: 0.3rem;
      border-top-right-radius: 0.3rem;
      .modal-title {
        flex: 1;
        color: #000;
        font-size: 16px;
        line-height: 1.5;
        margin: 0;
      }
      .close {
        cursor: pointer;
        opacity: 0.5;
        background-color: transparent;
        border: 0;
        padding: 0;
        margin: 0;
        -webkit-transition: all 0.2s ease-in-out;
        -moz-transition: all 0.2s ease-in-out;
        -o-transition: all 0.2s ease-in-out;
        transition: all 0.2s ease-in-out;
        i {
          font-size: 18px;
          color: #7d7d83;
          text-shadow: none;
        }
      }
      .close:hover {
        opacity: 0.9;
      }
    }
    .modal-body {
      padding: 0 25px 20px;
      .form-group .p-dropdown,  .form-group .p-inputtext {
        width: 100%;
      }
    }
    .modal-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 20px 25px 25px;

      button:last-child {
        margin-left: 10px;
      }
    }
  }
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.5;
    transition: opacity 0.15s linear;
    z-index: 1001;
  }
}

.hide {
  display: none;
}

.modal {
  .modal-dialog {
    width: 500px;
  }
  .modal-xl {
    width: 90%;
  }
}

// app-chatgpt 页面 - 全局设置 - 表单
.form-app-chatgpt-setting {
  .form-group {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // margin: 2rem 1rem;
    label {
      // display: block;
      // width: 6em;
    }
    .form-group-content {
      // flex: 1;
    }
  }
}
