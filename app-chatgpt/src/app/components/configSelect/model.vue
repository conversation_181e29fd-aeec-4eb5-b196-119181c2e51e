<template>
  <div class="model__select setting-block">
    <el-tooltip
      effect="dark"
      content="不同的模型，拥有不同的语言及逻辑推理能力，可点击修改会话的按钮来修改模型"
      placement="top-start"
    >
      <img class="setting-tipIcon" src="@/app/assets/tip.svg" alt="tip" @click.stop="go_to_model_explain_page">
    </el-tooltip>
    <span class="demonstration">模型选择</span>
    <el-select
      v-model="localModelName"
      placeholder="请选择模型"
      size="large"
      style="width: 240px"
      @change="emitChange"
    >
    <template #label="{ label }">
      <img
        :src="getImageSrc(label)"
        alt=""
        style="width: 20px; height: 20px; margin-right: 5px; vertical-align: middle;"
      />
      {{ label }}
    </template>
      <el-option
        v-for="item in availableModels"
        :key="item"
        :label="item"
        :value="item"
      >
      <template v-slot:default>
        <img
          :src="getImageSrc(item)"
          alt=""
          style="width: 20px; height: 20px; margin-right: 5px; vertical-align: middle;"
        />
        {{ item }}
      </template>
      </el-option>
    </el-select>
  </div>
  </template>
  
  <script>
  import { mapWritableState, mapActions } from "pinia";
  import useChatStore from "@/app/stores/chat";
  import useAppStore from "@/app/stores/app";
  
  export default {
    name: "ModelDropdown",
    props: {
      modelName: {
        type: String,
        required: true
      },
    },
    data() {
      return {
        localModelName: this.modelName
      }
    },
    watch: {
      modelName(newVal) {
        this.localModelName = newVal;
      }
    },
    computed: {
      ...mapWritableState(useChatStore, ["availableModels"]),
    },
    methods: {
      ...mapActions(useAppStore, ['reportUserEvent']),
      getImageSrc(item) {
        if (item.toLowerCase().includes('gpt')) {
          return require('@/app/assets/logo-chatGpt.svg');
        } else if (item.toLowerCase().includes('qwen')) {
          return require('@/app/assets/logo-qianWen.svg');
        } else if (item.toLowerCase().includes('文心一言')) {
          return require('@/app/assets/logo-wenxin.svg');
        }
        return '';
      },
      emitChange() {
        this.$emit('update:modelName', this.localModelName);
        this.$emit('change');
      },
  
      go_to_model_explain_page() {
        this.reportUserEvent('clickfaq');
        window.open('/app-product-gallery/faq', '_blank');
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .model__select {
    .demonstration + .el-select {
      flex: 0 0 75%;
    }
  }
  
  .setting-block {
    max-width: 500px;
    display: flex;
    align-items: center;
    .setting-tipIcon {
      width: 16px;
      height: 16px;
      margin-right: 5px;
      cursor: pointer;
    }
    .demonstration {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      line-height: 44px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 0;
    }
  }
  </style>
  