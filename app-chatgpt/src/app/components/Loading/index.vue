<template>
    <div class="loading-container">
        <div class="loading-content">
            <ProgressSpinner strokeWidth="3" class="loading-spinner" />
            <span class="loading-text">{{ text }}</span>
        </div>
    </div>
</template>

<script setup>
import { defineProps } from 'vue';
import ProgressSpinner from 'primevue/progressspinner';

defineProps({
    text: {
        type: String,
        default: '加载中...',
    },
});
</script>

<style lang="scss" scoped>
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    width: 100%;

    .loading-content {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .loading-spinner {
        width: 20px;
        height: 20px;
        animation: pulse 2s infinite ease-in-out;

        :deep(.p-progress-spinner-circle) {
            stroke: #6366f1 !important;
            stroke-width: 3px;
        }
    }

    .loading-text {
        color: #6366f1;
        font-size: 14px;
        font-weight: 500;
        opacity: 0.9;
        animation: fadeInOut 1.5s infinite ease-in-out;
    }
}

@keyframes pulse {
    0% {
        transform: scale(0.97);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.03);
        opacity: 1;
    }
    100% {
        transform: scale(0.97);
        opacity: 0.8;
    }
}

@keyframes fadeInOut {
    0% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
    }
}
</style>
