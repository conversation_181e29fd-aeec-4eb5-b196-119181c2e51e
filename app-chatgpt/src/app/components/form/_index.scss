.form-group {
  margin-bottom: 10px;
  label {
    display: block;
    font-size: 14px;
    // margin-bottom: 5px;
  }
  .form-control {
    display: block;
    width: 100%;
    height: auto;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: #838d91;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  .form-control:hover {
    border-color: #dbdbdb;
  }
  .form-control:focus {
    border-color: #75b7ff;
    box-shadow: none;
    color: #384c6d;
    box-shadow: 0 0 0 0.2rem rgba(3, 122, 251, 0.25);
  }

  .custom-select {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 1.75rem 0.375rem 0.75rem;
    // font-size: 1.4rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e")
      no-repeat right 0.75rem center/8px 10px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  .custom-control-label::before,
  .custom-file-label,
  .custom-select {
    transition: background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  .custom-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
  .custom-range {
    padding: 0;
  }
}
