// 问题级别常量
export const SEVERITY = {
    BLOCKER: 'BLOCKER',
    CRITICAL: 'CRITICAL',
    MAJOR: 'MAJOR',
    MINOR: 'MINOR',
    INFO: 'INFO',
};

// 问题级别显示名称
export const SEVERITY_NAMES = {
    [SEVERITY.BLOCKER]: '阻断问题',
    [SEVERITY.CRITICAL]: '严重问题',
    [SEVERITY.MAJOR]: '主要问题',
    [SEVERITY.MINOR]: '次要问题',
    [SEVERITY.INFO]: '提示问题',
};

// 问题类型常量
export const ISSUE_TYPE = {
    RELIABILITY: 2,
    VULNERABILITY: 3,
    CODE_SMELL: 1,
    OWASP: 4,
};

// 问题类型显示名称
export const ISSUE_TYPE_NAMES = {
    [ISSUE_TYPE.RELIABILITY]: 'Bugs',
    [ISSUE_TYPE.VULNERABILITY]: '漏洞',
    [ISSUE_TYPE.CODE_SMELL]: '代码坏味道',
    [ISSUE_TYPE.OWASP]: 'OWASP漏洞',
};

// 获取问题级别标签样式
export function getSeverityTagStyle(severity) {
    switch (severity) {
        case SEVERITY.BLOCKER:
            return 'blocker';
        case SEVERITY.CRITICAL:
            return 'critical';
        case SEVERITY.MAJOR:
            return 'major';
        case SEVERITY.MINOR:
            return 'minor';
        case SEVERITY.INFO:
            return 'info';
        default:
            return '';
    }
}

// 获取问题类型标签样式
export function getIssueTypeTagStyle(issueType) {
    const type = Number(issueType);
    switch (type) {
        case ISSUE_TYPE.RELIABILITY:
            return 'danger';
        case ISSUE_TYPE.VULNERABILITY:
            return 'warning';
        case ISSUE_TYPE.CODE_SMELL:
            return 'info';
        case ISSUE_TYPE.OWASP:
            return 'primary';
        default:
            return 'secondary';
    }
}

// 获取问题级别显示名称
export function getSeverityName(severity) {
    return SEVERITY_NAMES[severity] || '未知级别';
}

// 获取问题类型显示名称
export function getIssueTypeName(issueType) {
    const type = Number(issueType);
    return ISSUE_TYPE_NAMES[type] || '未知问题';
}
