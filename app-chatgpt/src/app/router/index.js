import { createRouter, createWebHistory } from 'vue-router';

import Home from '@/pages/home/<USER>';
import Codereview from '@/pages/codereview/Index.vue';
import CodereviewDetail from '@/pages/codereview/Detail.vue';
import CodereviewMy from '@/pages/codereview/My.vue';
import CodereviewReport from '@/pages/codereview/Report.vue';
import shareDetail from '@/pages/share/detail.vue';
import Sonar from '@/pages/sonar/index.vue';
import SonarDetail from '@/pages/sonar/detail.vue';
const routes = [
  {
    path: '/',
    name: 'home',
    component: Home,
  },
  {
    path: '/codereview',
    name: 'codereview',
    component: Codereview,
  },
  {
    path: '/codereview/report',
    name: 'codereview-report',
    component: CodereviewReport,
  },
  {
    path: '/codereview/:id',
    name: 'codereview-detail',
    component: CodereviewDetail,
  },
  {
    path: '/my-review',
    name: 'my-review',
    component: CodereviewMy,
  },

  {
    path: '/share/:id',
    name: 'share-detail',
    component: shareDetail,
  },
  {
    path: '/sonar',
    name: 'sonar',
    component: Sonar,
  },
  {
    path: '/sonar-detail',
    name: 'sonar-detail',
    component: SonarDetail,
  },
];

const router = createRouter({
  history: createWebHistory('/app-chatgpt/'),
  routes,
});

export default router;
