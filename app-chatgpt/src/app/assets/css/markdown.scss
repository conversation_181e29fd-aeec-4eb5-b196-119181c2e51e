// Markdown 样式统一管理
.markdown-body {
    // 复制按钮样式
    .copy-button {
        position: absolute;
        right: 10px;
        top: 10px;
        padding: 4px 10px;
        color: #fff;
        background-color: #6366f1;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.875rem;
        display: none;
    }

    // 状态相关样式
    .status-pending,
    .status-submitted,
    .status-failed {
        display: flex;
        flex-direction: column;
        align-items: baseline;
        color: #999;

        .error-actions {
            width: 100%;

            .action-buttons {
                .button-with-badge {
                    position: relative;
                    display: inline-block;

                    .recommend-badge {
                        z-index: 1;
                        position: absolute;
                        top: -8px;
                        right: -20px;
                        background: linear-gradient(45deg, #ff6b6b, #feca57);
                        color: white;
                        font-size: 0.7rem;
                        padding: 2px 4px;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        gap: 2px;
                        transform: scale(0.85);
                        transform-origin: bottom left;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        animation: badge-pop 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);

                        i {
                            font-size: 0.7rem;
                            color: #fff;
                        }
                    }
                }

                .help-button {
                    &:hover {
                        background-color: transparent !important;

                        a {
                            color: var(--primary-color) !important;
                        }
                    }

                    a {
                        display: inline-flex;
                        align-items: center;
                        padding: 0.5rem 0.75rem;
                        border-radius: 6px;
                        transition: all 0.2s ease;

                        &:hover {
                            background-color: var(--surface-hover);
                        }

                        i {
                            font-size: 1rem;
                            color: var(--primary-color);
                        }
                    }
                }
            }

            .admin-contact {
                padding: 0.5rem 0.75rem;
                background-color: var(--surface-ground);
                border-radius: 8px;
                border: 1px solid var(--surface-border);

                .admin-tag {
                    transition: all 0.2s ease;
                    cursor: pointer;

                    &:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
    }

    .status-unknown {
        color: #856404;
        background-color: #fff3cd;
        padding: 0.75rem 1.25rem;
        border-radius: 0.25rem;
    }

    .status-failed {
        .pi-exclamation-circle {
            color: #ff4d4f;
        }

        .subtitle {
            padding: 10px;
            border-radius: 10px;
            background-color: #fef2f2;
            color: #b91c1c;
        }
    }

    .status-pending {
        .pi-spinner {
            color: #1890ff;
        }

        .subtitle {
            padding: 10px;
            border-radius: 10px;
            background-color: #f0f7ff;
            color: #1d4ed8;
        }
    }
}

// 管理员联系样式
.admin-contact {
    .admin-list {
        .admin-item {
            text-decoration: none;
            color: var(--primary-color);
            font-size: 0.875rem;
            background-color: var(--surface-ground);
            border: 1px solid var(--surface-border);

            &:hover {
                background-color: var(--surface-hover);
                border-color: var(--primary-color);
            }

            i {
                color: var(--primary-color);
            }
        }
    }
}

// 动画
@keyframes badge-pop {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(0.85);
        opacity: 1;
    }
}
