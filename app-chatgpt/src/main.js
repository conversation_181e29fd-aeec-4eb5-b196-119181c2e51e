import './main-global.scss';
import './main.scss';
import 'github-markdown-css/github-markdown.css';
import '@fortawesome/fontawesome-free/css/all.css';
import 'primeicons/primeicons.css'

import { h, createApp } from 'vue';
import singleSpaVue from 'single-spa-vue';
import { createPinia } from 'pinia';

import App from './App.vue';
import router from '@/app/router';
import VeeValidatePlugin from '@/app/includes/validation';

import 'primevue/resources/themes/aura-light-indigo/theme.css';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import registerPrimeVue from './registerPrimeVue';

import { zhCN } from '@/app/utils/locales/zh.js';

// 支持应用独立运行、部署，不依赖于基座应用, 独立运行时，直接挂载应用
if (!window.singleSpaNavigate) {
    console.log('独立运行');

    const app = createApp(App);

    app.use(router);
    app.use(createPinia());
    app.use(VeeValidatePlugin);
    app.use(PrimeVue, {
        ripple: true,
        locale: zhCN,
    });

    registerPrimeVue(app);
    app.use(ToastService);
    app.use(ConfirmationService);

    app.mount('#app');
}


const vueLifecycles = singleSpaVue({
    createApp,
    appOptions: {
        render() {
            return h(App, {});
        },
    },
    handleInstance: (app) => {
        app.use(router);
        app.use(createPinia());
        app.use(VeeValidatePlugin);
        app.use(PrimeVue, {
            ripple: true,
            locale: zhCN,
        });

        registerPrimeVue(app);
        app.use(ToastService);
        app.use(ConfirmationService);
    },
});

export const bootstrap = vueLifecycles.bootstrap;
export const mount = vueLifecycles.mount;
export const unmount = vueLifecycles.unmount;
