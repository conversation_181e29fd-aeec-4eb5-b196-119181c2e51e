const { defineConfig } = require('@vue/cli-service');
const webpack = require('webpack');
const path = require('path');
const devServerConfig = require('./config/devServer');

module.exports = defineConfig({
    transpileDependencies: true,
    configureWebpack: {
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
            },
        },
        devServer: {
            ...devServerConfig.getDevServerConfig(),
            proxy: {},
        },
        output: {
            libraryTarget: 'system',
        },
        externals: [/^@dealer\/.+/],
        plugins: [
            // 排除 docs 目录下的所有文件，防止被意外导入或打包
            new webpack.IgnorePlugin({
                resourceRegExp: /\/docs\//,
            }),
            require('unplugin-vue-components/webpack').default({
                /* options */
            }),
            require('unplugin-auto-import/webpack').default({
                /* options */
            }),
        ],
        devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
    },
    productionSourceMap: false, // 保留这一行
});
