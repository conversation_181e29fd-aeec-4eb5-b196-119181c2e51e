// server.js
const express = require("express");
const cors = require("cors"); // 引入 CORS 中间件模块
const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors()); // 使用 CORS 中间件

app.get("/sse", (req, res) => {
  res.setHeader("Content-Type", "text/event-stream");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");
  res.flushHeaders();

  // 每隔一秒发送一个消息
  let count = 0;
  const interval = setInterval(() => {
    const message = `data: {"count": ${count}}\n\n`;
    res.write(message);
    count += 1;
  }, 1000);

  req.on("close", () => {
    clearInterval(interval);
    res.end();
  });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
