# AI翻译工具使用说明

## 概述

本项目已集成基于LangChain.js的AI翻译工具，支持中英文互译功能。

## 功能特性

-   🌐 支持中英文双向翻译
-   🚀 多平台支持：硅基流动、DeepSeek、Groq、OpenAI、百度千帆、腾讯混元等
-   💰 免费模型可用（硅基流动BGE-M3、腾讯混元-Lite等）
-   🤖 基于各大厂商AI模型，翻译质量高
-   🎯 自动语言检测
-   🔄 语言切换功能
-   📋 一键复制翻译结果
-   📱 响应式设计，支持移动端
-   ⚙️ 可视化平台和模型选择
-   🔧 API配置状态实时显示

## 配置说明

### 1. 环境变量配置

在项目根目录创建 `.env.local` 文件，参考 `env.config.example` 文件配置：

#### 推荐平台配置（按优先级排序）：

**🥇 硅基流动 (SiliconFlow) - 推荐首选**

```env
# 提供免费模型，兼容OpenAI API
VUE_APP_SILICONFLOW_API_KEY=your-siliconflow-api-key-here
VUE_APP_SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
```

**🥈 Groq - 高速推理**

```env
# 极快的推理速度，免费额度
VUE_APP_GROQ_API_KEY=your-groq-api-key-here
VUE_APP_GROQ_BASE_URL=https://api.groq.com/openai/v1
```

**🥉 DeepSeek - 高质量模型**

```env
# DeepSeek系列模型，推理能力强
VUE_APP_DEEPSEEK_API_KEY=your-deepseek-api-key-here
VUE_APP_DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
```

**OpenAI - 稳定但需付费**

```env
# 官方OpenAI模型
VUE_APP_OPENAI_API_KEY=your-openai-api-key-here
VUE_APP_OPENAI_BASE_URL=https://api.openai.com/v1
```

### 2. 获取API密钥的详细步骤

#### 🌟 硅基流动 (推荐)

1. 访问 [硅基流动官网](https://siliconflow.cn)
2. 注册并登录账户
3. 前往"API密钥"页面
4. 创建新的API密钥
5. 复制密钥到环境变量配置
6. **优势**: 提供免费的BGE-M3等模型，兼容OpenAI API格式

#### ⚡ Groq

1. 访问 [Groq控制台](https://console.groq.com)
2. 使用Google或GitHub账户登录
3. 前往"API Keys"页面
4. 创建新的API密钥
5. **优势**: 极快的推理速度，每分钟免费请求数量高

#### 🧠 DeepSeek

1. 访问 [DeepSeek平台](https://platform.deepseek.com)
2. 注册并登录账户
3. 前往API页面获取密钥
4. **优势**: 模型推理能力强，适合复杂翻译任务

#### 🔒 OpenAI

1. 访问 [OpenAI官网](https://platform.openai.com/)
2. 注册并登录账户
3. 前往API Keys页面创建新的API密钥
4. **优势**: 最稳定的服务，但需要付费使用

## 快速开始

### 🚀 零配置试用（推荐）

1. **复制配置文件**

    ```bash
    cp env.config.example .env.local
    ```

2. **获取硅基流动免费API密钥**

    - 访问 [硅基流动官网](https://siliconflow.cn)
    - 注册账户（支持微信/手机号注册）
    - 前往控制台 → API密钥 → 创建新密钥
    - 复制API密钥

3. **配置环境变量**

    ```bash
    # 在 .env.local 文件中添加
    VUE_APP_SILICONFLOW_API_KEY=sk-your-actual-api-key-here
    ```

4. **启动项目**

    ```bash
    npm run serve
    ```

5. **开始使用**
    - 点击聊天页面的翻译工具按钮
    - 选择"硅基流动"平台和"BGE-M3 (免费)"模型
    - 输入文本开始翻译

## 使用方法

### 在聊天页面中使用

1. **打开翻译工具**

    - 打开聊天页面
    - 在工具栏中找到"翻译工具"按钮（蓝色语言图标）
    - 点击按钮打开翻译工具对话框

2. **选择AI平台和模型**

    - 在"AI平台"下拉菜单中选择您配置的平台
    - 在"模型"下拉菜单中选择具体的模型
    - 免费模型会显示绿色"免费"标签
    - 查看API配置状态提示

3. **设置翻译语言**

    - 选择源语言和目标语言
    - 可以选择"自动检测"让AI自动识别输入语言

4. **执行翻译**

    - 输入要翻译的文本（最多1000字符）
    - 点击"开始翻译"按钮
    - 等待AI处理并查看翻译结果

5. **使用翻译结果**
    - 查看翻译结果
    - 点击复制按钮将结果复制到剪贴板
    - 使用语言交换功能进行反向翻译

### 快捷功能

-   **语言切换**：点击中间的交换按钮可快速切换源语言和目标语言
-   **复制结果**：翻译完成后点击复制按钮可将结果复制到剪贴板
-   **自动检测**：选择"自动检测"可让AI自动识别输入文本的语言

## 技术实现

### 核心技术栈

-   **LangChain.js**: AI应用开发框架
-   **OpenAI GPT**: 大语言模型
-   **Vue 3**: 前端框架
-   **PrimeVue**: UI组件库

### 组件结构

```
src/pages/components/Translator/
├── index.vue                 # 翻译组件主文件
```

### 关键特性

1. **LCEL链式调用**: 使用LangChain Expression Language构建翻译管道
2. **提示词工程**: 优化的翻译提示词模板
3. **错误处理**: 完善的错误处理和用户反馈
4. **响应式设计**: 适配各种屏幕尺寸

## 故障排除

### 常见问题

1. **翻译失败**

    - 检查API密钥是否正确配置
    - 确认网络连接正常
    - 验证OpenAI账户余额

2. **翻译速度慢**

    - 检查网络连接
    - 考虑使用API代理
    - 尝试更换模型

3. **翻译质量问题**
    - 检查输入文本是否清晰
    - 尝试调整温度参数
    - 考虑使用更高级的模型

### 配置检查

```javascript
// 在浏览器控制台中检查配置
console.log(
    'OpenAI API Key:',
    process.env.VUE_APP_OPENAI_API_KEY ? '已配置' : '未配置'
);
```

## 开发说明

### 自定义配置

可以在 `Translator/index.vue` 中修改以下配置：

```javascript
// 模型配置
this.model = new ChatOpenAI({
    model: process.env.VUE_APP_TRANSLATOR_MODEL || 'gpt-3.5-turbo',
    temperature: 0.1, // 调整创造性（0-1）
    openAIApiKey: process.env.VUE_APP_OPENAI_API_KEY,
});
```

### 扩展功能

-   添加更多语言支持
-   集成语音识别和朗读
-   实现翻译历史记录
-   添加批量翻译功能

## 许可证

本翻译工具遵循项目的整体许可证。

## 贡献

欢迎提交问题和功能请求！
