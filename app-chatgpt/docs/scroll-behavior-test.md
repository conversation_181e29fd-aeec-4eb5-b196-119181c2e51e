# 滚动行为优化测试指南

## 问题描述
原始问题：模型输出过程中鼠标滚轮滚动对光标控制体验较差，用户期望手动滚动时能完全接管滚动控制，但现象是刚滚动一点就会强制滚到最下。

## 优化方案

### 核心改进
1. **明确区分用户行为**：
   - `userManuallyScrolled`: 用户是否手动触发过滚动
   - `isUserScroll`: 当前是否处于用户手动控制模式
   - `lastAutoScrollTime`: 最后一次自动滚动时间戳

2. **智能滚动逻辑**：
   - 用户从未滚动：始终自动滚动到底部
   - 用户手动滚动到中间：停止自动滚动，显示"返回底部"按钮
   - 用户手动滚动到底部：恢复自动滚动模式

### 测试场景

#### 场景1：初始状态（期望：自动滚动）
1. 打开聊天页面
2. 发送消息
3. **期望结果**：AI回复时自动滚动到底部

#### 场景2：用户中途滚动（期望：停止自动滚动）
1. 发送消息，AI开始回复
2. 在AI回复过程中，用户向上滚动
3. **期望结果**：
   - 停止自动滚动到底部
   - 显示"返回最新对话"按钮
   - AI继续回复但不强制滚动

#### 场景3：用户滚动到底部（期望：恢复自动滚动）
1. 在场景2基础上
2. 用户手动滚动到底部
3. **期望结果**：
   - 隐藏"返回最新对话"按钮
   - 恢复自动滚动模式
   - 后续AI回复自动滚动到底部

#### 场景4：点击返回按钮（期望：恢复自动滚动）
1. 在场景2基础上
2. 点击"返回最新对话"按钮
3. **期望结果**：
   - 平滑滚动到底部
   - 隐藏按钮
   - 恢复自动滚动模式

## 技术实现要点

### 关键函数
- `smartAutoScroll()`: 智能判断是否需要自动滚动
- `performAutoScroll()`: 执行自动滚动并记录时间戳
- `throttleScroll()`: 节流的滚动监听，区分用户滚动和自动滚动

### 状态管理
```javascript
// 新增状态
userManuallyScrolled: false,  // 用户是否手动触发过滚动
lastAutoScrollTime: 0,        // 最后一次自动滚动时间戳

// 原有状态优化
isUserScroll: false,          // 当前是否处于用户手动控制模式
showBackToBottom: false,      // 是否显示返回底部按钮
```

### 滚动检测逻辑
```javascript
// 检测是否为自动滚动（500ms内的滚动认为是自动滚动）
const isAutoScroll = now - this.lastAutoScrollTime < 500;

if (!isAutoScroll) {
    // 用户主动滚动
    this.userManuallyScrolled = true;
    // 根据位置决定是否显示按钮
}
```

## 预期效果
1. **用户体验提升**：用户滚动时不会被强制拉回底部
2. **智能化**：系统能准确识别用户意图
3. **一致性**：滚动行为符合用户预期
4. **可控性**：用户可以随时通过滚动到底部或点击按钮恢复自动滚动
