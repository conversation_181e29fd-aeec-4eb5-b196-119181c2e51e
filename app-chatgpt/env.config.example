# AI翻译工具 - 环境变量配置示例
# 复制此文件为 .env.local 并填入您的实际API密钥

# ===========================================
# 硅基流动 (SiliconFlow) - 推荐使用，提供免费模型
# ===========================================
# 官网：https://siliconflow.cn
# 特点：兼容OpenAI API格式，提供免费开源模型
VUE_APP_SILICONFLOW_API_KEY=your-siliconflow-api-key-here
VUE_APP_SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# ===========================================
# DeepSeek - 高性能推理模型
# ===========================================
# 官网：https://platform.deepseek.com
# 特点：DeepSeek系列模型，推理能力强
VUE_APP_DEEPSEEK_API_KEY=your-deepseek-api-key-here
VUE_APP_DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# ===========================================
# Groq - 高速推理平台
# ===========================================
# 官网：https://console.groq.com
# 特点：极快的推理速度，兼容OpenAI API
VUE_APP_GROQ_API_KEY=your-groq-api-key-here
VUE_APP_GROQ_BASE_URL=https://api.groq.com/openai/v1

# ===========================================
# OpenAI - 原生支持
# ===========================================
# 官网：https://platform.openai.com
VUE_APP_OPENAI_API_KEY=your-openai-api-key-here
VUE_APP_OPENAI_BASE_URL=https://api.openai.com/v1

# ===========================================
# 百度千帆 (Qianfan)
# ===========================================
# 官网：https://cloud.baidu.com/product/wenxinworkshop
# 注意：百度API格式可能需要特殊处理
VUE_APP_BAIDU_API_KEY=your-baidu-api-key-here
VUE_APP_BAIDU_BASE_URL=https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat

# ===========================================
# 腾讯混元 (Hunyuan)
# ===========================================
# 官网：https://cloud.tencent.com/product/hunyuan
# 特点：提供免费的Lite版本
VUE_APP_TENCENT_API_KEY=your-tencent-api-key-here
VUE_APP_TENCENT_BASE_URL=https://hunyuan.tencentcloudapi.com

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env.local
# 2. 选择一个或多个平台并配置对应的API密钥
# 3. 在翻译工具中选择对应的平台和模型
# 4. 开始使用AI翻译功能

# 推荐配置顺序：
# 1. 硅基流动 (免费模型可用)
# 2. Groq (高速免费推理)
# 3. DeepSeek (高质量模型)
# 4. OpenAI (最稳定但需付费) 