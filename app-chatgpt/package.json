{"name": "@dealer/app-chatgpt", "version": "0.1.0", "private": true, "scripts": {"develop": "vue-cli-service serve --mode local --port 9202", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "develop:standalone": "vue-cli-service serve --mode standalone --port 9208", "analyze": "vue-cli-service build --report"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@vee-validate/rules": "^4.11.1", "@vueuse/components": "^10.7.0", "@vueuse/core": "^10.7.0", "chart.js": "^4.4.6", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.10", "diff-dom": "^5.1.2", "diff2html": "^3.4.45", "github-markdown-css": "^5.2.0", "highlight.js": "^11.11.0", "markdown-it": "^13.0.2", "markdown-to-txt": "^2.0.1", "pinia": "^2.1.6", "postcss-preset-env": "^10.0.2", "primeicons": "^7.0.0", "primevue": "^3.52.0", "single-spa-vue": "^2.1.0", "unplugin-auto-import": "^0.17.6", "vee-validate": "^4.11.1", "vue": "^3.2.13", "vue-router": "next"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vicons/antd": "^0.12.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.14.2", "compression-webpack-plugin": "^11.1.0", "cssnano": "^7.0.6", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss": "^8.4.49", "prettier": "^3.2.5", "sass": "^1.64.0", "sass-loader": "^13.3.2", "tailwindcss": "^3.4.17", "unplugin-vue-components": "^0.27.4", "volar-service-vetur": "^0.0.11", "vue-cli-plugin-single-spa": "~3.3.0", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}