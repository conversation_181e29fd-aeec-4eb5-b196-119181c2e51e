/**
 * 开发服务器配置
 */

const proxyConfig = require('./proxy');

// 基础配置
const baseConfig = {
    compress: false,
};

// 微前端子应用配置
const microFrontendConfig = {
    ...baseConfig,
};

// 独立应用配置
const standaloneConfig = {
    ...baseConfig,
    proxy: proxyConfig,
    setupMiddlewares: (middlewares, devServer) => {
        if (!devServer) {
            throw new Error('webpack-dev-server is not defined');
        }

        // 添加 SSO mock 接口
        devServer.app.get('/sso/username', (_, response) => {
            response.json({ username: 'z<PERSON><PERSON><PERSON><PERSON>' });
        });

        return middlewares;
    },
};

module.exports = {
    // 根据环境变量判断使用哪种配置
    getDevServerConfig: () => {
        const isStandalone = process.env.STANDALONE_SINGLE_SPA === 'true';
        return isStandalone ? standaloneConfig : microFrontendConfig;
    },
};
