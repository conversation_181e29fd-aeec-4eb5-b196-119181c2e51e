/**
 * 代理配置
 */

const ACCOUNT_NAME = 'zhaixiaowei';

// GPT API 相关配置
const gptApiProxy = {
    '/api-cr': {
        target: 'http://gpt-api.terra.corpautohome.com',
        changeOrigin: true,
        pathRewrite: {
            '^/api-cr': '',
        },
        onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
        },
    },
    '/api': {
        target: 'http://gpt-api.terra.corpautohome.com',
        changeOrigin: true,
        pathRewrite: {
            '^/api': '/chat',
        },
        onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
        },
    },
    '/file-api': {
        target: 'http://gpt-api.terra.corpautohome.com',
        changeOrigin: true,
        pathRewrite: {
            '^/file-api': '/file',
        },
        onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
        },
    },
};

// 埋点统计配置
const statProxy = {
    '/stat': {
        target: 'http://gpt-api.terra.corpautohome.com',
        changeOrigin: true,
        onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat_local`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
        },
    },
};

// 导出所有代理配置
module.exports = {
    ...gptApiProxy,
    ...statProxy,
};
