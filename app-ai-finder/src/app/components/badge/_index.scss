.badge {
  display: inline-block;
  color: #fff;
  font-size: 75%;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  padding: 6px 10px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  button.close {
    color: inherit;
    background-color: transparent;
    border: none;
    opacity: 0.9;
  }
  button.close:hover {
    cursor: pointer;
  }
}

.badge-pill {
  border-radius: 10rem;
}

.badge.badge-info {
  background: #5780f7;
}

.badge.badge-light {
  background: #f4f4f5;
  color: #384c6d;
}
.badge.badge-secondary {
  background: #e6e6e6;
  color: #384c6d;
}

.badge.badge-dark {
  background: #37414d;
}

.badge-mini {
  font-size: 11px;
  padding: 4px;
}
