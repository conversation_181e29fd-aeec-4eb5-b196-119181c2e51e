<template>
  <div class="home-wrap card">
    <page-home-content />
  </div>
</template>

<script>
import PageHomeContent from "./content/index.vue";

export default {
  name: "PageHome",
  components: {
    PageHomeContent,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="sass" scoped>
.home-wrap
  position: relative
  font-size: 14px
  background-color: #fff
  padding: 0 0 20px
  margin: 0
  z-index: 9
  .messages
    height: 500px
    background-color: #eee
  .user-input
    z-index: 9
    .notice
      color: #b33100
    .message-setting
      display: flex
      align-items: center
      height: 40px
      line-height: 40px
    .message-send
      position: relative
      text-align: right
      padding-right:70px
      margin-top: 10px
      z-index: 9
      button
        position: absolute
        top: 0
        right: 0
        z-index: 99
.home-wrap.card
  box-shadow: none
</style>
