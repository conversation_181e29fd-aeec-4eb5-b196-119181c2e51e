const { defineConfig } = require("@vue/cli-service");
const path = require("path");

module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    devServer: {
      compress: false,
      proxy: {
        "/aifinder": {
          target: "http://gpt-api.terra.corpautohome.com", // 测试
          changeOrigin: true,
          onProxyReq(proxyReq, req) {
            const accountParam = "account=biankai";
            const delimiter = req.url.includes("?") ? "&" : "?";
            proxyReq.path += delimiter + accountParam;
          },
        },
        "/api": {
          target: "http://gpt-api.terra.corpautohome.com", // 测试
          changeOrigin: true, // 更改请求源
          onProxyReq(proxyReq, req) {
            const accountParam = "account=biankai";
            const delimiter = req.url.includes("?") ? "&" : "?";
            proxyReq.path += delimiter + accountParam;
          },
          pathRewrite: {
            "^/api": "/chat", // 重写路径（可选）
          },
        },
      },
    },
    output: {
      libraryTarget: "system",
    },
  },
});
