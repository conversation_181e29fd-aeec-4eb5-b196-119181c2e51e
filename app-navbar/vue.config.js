const { defineConfig } = require("@vue/cli-service");
const { codeInspectorPlugin } = require('code-inspector-plugin');

const path = require("path");
module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    devServer: {
      compress: false,
    },
    output: {
      libraryTarget: "system",
    },
    externals: [/^@dealer\/.+/],
    plugins: [
      codeInspectorPlugin({
        bundler: 'webpack',
        hotKeys: [
          'metaKey'
        ],
        hideDomPathAttr: true
      }),
      require('unplugin-vue-components/webpack').default({ /* options */ }),
      require('unplugin-auto-import/webpack').default({ /* options */ }),
    ]
  }
});
