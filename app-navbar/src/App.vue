<template>
    <div class="app-navbar-wrap">
        <section class="topbar flex align-items-center justify-content-between">
            <div class="flex align-items-center">
                <NavMenubar :menus="menus" @update-badge="updateBadgeCount" />
            </div>

            <!-- New buttons section -->
            <div class="topbar-actions flex align-items-center">
                <div
                    class="mcp-store-container"
                    @click="navigateTo('https://mcpstore.corpautohome.com/')"
                >
                    <div class="mcp-store-content">
                        <img
                            src="https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/logo-s-blue-m.png"
                            alt="MCP-STORE"
                            class="mcp-store-logo"
                        />
                        <span>MCP-STORE</span>
                    </div>
                    <div class="mcp-store-hot-label">HOT</div>
                    <div class="mcp-store-shine"></div>
                </div>
                <Button
                    label="更新日志"
                    icon="pi pi-bell"
                    class="p-button-sm action-button update-log-btn"
                    @click="navigateTo('/app-product-gallery/updates')"
                />
                <Button
                    label="帮助中心"
                    icon="pi pi-question-circle"
                    class="p-button-sm action-button help-center-btn"
                    @click="navigateTo('/app-product-gallery/faq')"
                />
                <Button
                    label="问题反馈"
                    icon="pi pi-comment"
                    class="p-button-sm action-button feedback-btn"
                    @click="
                        navigateTo(
                            'https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/new'
                        )
                    "
                />
                <Button
                    :label="`当前用户: ${username}`"
                    icon="pi pi-user"
                    class="p-button-sm action-button user-info-button"
                    @click="toggleUserInfoPanel"
                    aria-haspopup="true"
                    aria-controls="overlay_panel_user"
                    ref="userButton"
                />
                <OverlayPanel ref="opUser" id="overlay_panel_user">
                    <Button
                        label="退出登录"
                        icon="pi pi-sign-out"
                        class="p-button-sm p-button-danger"
                        @click="logout"
                    />
                </OverlayPanel>
            </div>
        </section>
    </div>

    <!-- 通知弹窗 : 包含"版本更新" (Start) -->
    <Notice />
    <!-- 通知弹窗 : 包含"版本更新" (End) -->
    <!-- 提示词弹窗 (Start) -->
    <PromptDialog
        v-model:visible="dialog.promptBox.visible"
        :content="dialog.promptBox.content"
        @use-prompt="handleUsePrompt"
    />
    <!-- 提示词弹窗 (End) -->
</template>

<script>
import { mapWritableState, mapActions } from 'pinia';
import { traceLog, httpService } from '@dealer/utility-lib';
import useBadgeStore from '@/app/stores/badge';

import Notice from '@/components/Notice';
import PromptDialog from '@/components/PromptDialog';
import NavMenubar from '@/components/NavMenubar';

export default {
    name: 'AppNavbar',
    components: {
        Notice,
        PromptDialog,
        NavMenubar,
    },
    data() {
        return {
            username: window._appNavbar.username,
            menus: [
                {
                    label: '首页',
                    route: '/app-chatgpt/',
                    hoverImgUrl: require('@/assets/message2.svg'),
                    imgUrl: require('@/assets/message.svg'),
                    isHovered: false,
                    isActive: false,
                },
                {
                    label: '研发',
                    hoverImgUrl: require('@/assets/code2.svg'),
                    imgUrl: require('@/assets/code.svg'),
                    isHovered: false,
                    isActive: false,
                    items: [
                        {
                            label: 'IDEA插件',
                            route: '/app-product-gallery/chatgpt-idea',
                            hoverImgUrl: require('@/assets/idea2.svg'),
                            imgUrl: require('@/assets/idea.svg'),
                            isHovered: false,
                        },
                        {
                            label: 'VSCode插件',
                            route: '/app-product-gallery/chatgpt-vscode',
                            hoverImgUrl: require('@/assets/vscode2.svg'),
                            imgUrl: require('@/assets/vscode.svg'),
                            isHovered: false,
                        },
                        {
                            label: 'CodeReview',
                            hoverImgUrl: require('@/assets/code-review2.svg'),
                            imgUrl: require('@/assets/code-review.svg'),
                            isHovered: false,
                            items: [
                                {
                                    label: '我的Review',
                                    route: '/app-chatgpt/my-review',
                                    imgUrl: require('@/assets/person.svg'),
                                    hoverImgUrl: require('@/assets/person2.svg'),
                                    badge: null,
                                },
                                {
                                    label: '团队Review',
                                    route: '/app-chatgpt/codereview',
                                    imgUrl: require('@/assets/multiple-person.svg'),
                                    hoverImgUrl: require('@/assets/multiple-person2.svg'),
                                },
                                {
                                    label: '统计报表',
                                    route: '/app-chatgpt/codereview/report',
                                    imgUrl: require('@/assets/report.svg'),
                                    hoverImgUrl: require('@/assets/report2.svg'),
                                },
                            ],
                        },
                        {
                            label: 'Sonar 代码扫描',
                            route: '/app-chatgpt/sonar',
                            hoverImgUrl: require('@/assets/sonar2.svg'),
                            imgUrl: require('@/assets/sonar.svg'),
                            isHovered: false,
                        },
                    ],
                },
                {
                    label: '测试',
                    icon: 'fa-solid fa-bug',
                    hoverImgUrl: require('@/assets/test2.svg'),
                    imgUrl: require('@/assets/test.svg'),
                    isHovered: false,
                    isActive: false,
                    items: [
                        {
                            label: 'AIQA',
                            route: '/app-aiqa-web/',
                        },
                        {
                            label: '尝试记录',
                            route: '/app-aiqa-web/history',
                        },
                        {
                            label: '需求澄清助手',
                            route: '/app-aiqa-web/improve',
                        },
                    ],
                },
            ],
            dialog: {
                promptBox: {
                    visible: false,
                    content: [],
                },
            },
        };
    },
    computed: {
        ...mapWritableState(useBadgeStore, ['unExposedCount']),
        currentPath() {
            return this.$route.path;
        },
    },
    watch: {
        $route: {
            immediate: true,
            handler(newRoute) {
                this.updateTopBarActiveStates(newRoute.path);
            },
        },
    },
    methods: {
        toggle(event) {
            this.$refs.op.toggle(event);
        },

        ...mapActions(useBadgeStore, ['getUnExposedCount']),
        reportUserEvent: traceLog.reportUserEvent,
        async init() {
            await this.getUnExposedCount();
            this.menus[1].items[2].items[0].badge = this.unExposedCount;
        },
        handleUsePrompt({ title, content, id }) {
            const myEvent = new CustomEvent('app-navbar-usePrompt', {
                detail: {
                    title,
                    content,
                    id,
                },
            });
            window.dispatchEvent(myEvent);
            this.reportUserEvent(`click_appNavbar_promptBox_item_${id}`);
        },
        // 打开百宝箱
        bindOpenPromptBox() {
            this.dialog.promptBox.visible = true;
            this.reportUserEvent('click_appNavbar_promptBox');
        },
        navigateTo(routeOrUrl) {
            if (routeOrUrl.startsWith('http') || routeOrUrl.startsWith('//')) {
                window.open(routeOrUrl, '_blank');
            } else {
                this.$router.push(routeOrUrl);
            }
            // Example: this.reportUserEvent(`navigate_to_${routeOrUrl.replace(/[^a-zA-Z0-9]/g, '_')}`);
        },
        updateTopBarActiveStates() {
            // This is a placeholder if you want to make top bar buttons active based on route
            // For now, they are just navigational
        },
        toggleUserInfoPanel(event) {
            this.$refs.opUser.toggle(event);
        },
        async logout() {
            try {
                await httpService.get('sso/logout');
                // Redirect to login page or home page after logout
                // For example: window.location.href = '/login';
                alert('已成功退出登录'); // Placeholder, replace with actual redirect or UI update
            } catch (error) {
                console.error('Logout failed:', error);
                alert('退出登录失败，请稍后重试'); // Placeholder for error handling
            } finally {
                this.$refs.opUser.hide();
            }
        },
        updateBadgeCount(count) {
            if (
                this.menus &&
                this.menus[1] &&
                this.menus[1].items &&
                this.menus[1].items[2] &&
                this.menus[1].items[2].items
            ) {
                this.menus[1].items[2].items[0].badge = count;
            }
        },
    },
    async mounted() {
        this.init();
        // ftwo
        window.ftwo && window.ftwo.context.setUserId(this.username);
        // 获取提示词
        const url = '/strapi-api/prompts';
        try {
            const data = await httpService.get(url);
            this.dialog.promptBox.content = data.data;
        } catch (err) {
            console.log(err);
        }

        // app-navbar 中的提示词百宝箱 - 使用提示词
        window.addEventListener(
            'app-chatgpt-openPromptBox',
            this.bindOpenPromptBox
        );
    },
};
</script>

<style lang="scss">
.topbar-actions {
    .action-button {
        color: #26244ce0 !important;
        border: 1px solid #e5e7eb;
        background-color: #ffffff !important;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 6px;
        margin-right: 10px;
        transition: all 0.2s ease-in-out;

        &:hover {
            background-color: #f9fafb !important;
            border-color: #d1d5db;
        }
    }

    .action-button .p-button-icon {
        margin-right: 0.5rem;
        color: #6b7280;
    }

    .mcp-store-container {
        display: flex;
        align-items: center;
        position: relative;
        margin-right: 12px;
        background: linear-gradient(135deg, #ff6a00 0%, #ee0979 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        // overflow: hidden;
        box-shadow: 0 4px 12px rgba(238, 9, 121, 0.25);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(238, 9, 121, 0.35);
            filter: brightness(1.05);
        }

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transform: translateX(-100%);
            transition: 0.6s;
        }

        &:hover::after {
            transform: translateX(100%);
        }
    }

    .mcp-store-content {
        display: flex;
        align-items: center;

        .mcp-store-logo {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            object-fit: contain;
            filter: brightness(0) invert(1);
        }

        span {
            font-size: 0.9rem;
            letter-spacing: 0.3px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            position: relative;
        }
    }

    .mcp-store-hot-label {
        position: absolute;
        top: -5px;
        right: -5px;
        background: linear-gradient(135deg, #ff3a3a 0%, #ff9500 100%);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.6rem;
        font-weight: bold;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 5px rgba(255, 58, 58, 0.3);
        transform: rotate(15deg);
        animation: hotLabel 3s infinite;
    }

    @keyframes hotLabel {
        0% {
            transform: rotate(15deg) scale(1);
            box-shadow: 0 2px 5px rgba(255, 58, 58, 0.3);
        }
        25% {
            transform: rotate(15deg) scale(1.1);
            box-shadow: 0 3px 7px rgba(255, 58, 58, 0.4),
                0 0 10px rgba(255, 149, 0, 0.3);
        }
        50% {
            transform: rotate(15deg) scale(1);
            box-shadow: 0 2px 5px rgba(255, 58, 58, 0.3);
        }
        75% {
            transform: rotate(15deg) scale(1.1);
            box-shadow: 0 3px 7px rgba(255, 58, 58, 0.4),
                0 0 10px rgba(255, 149, 0, 0.3);
        }
        100% {
            transform: rotate(15deg) scale(1);
            box-shadow: 0 2px 5px rgba(255, 58, 58, 0.3);
        }
    }

    .mcp-store-shine {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
            circle at 50% 50%,
            rgba(255, 255, 255, 0.15),
            transparent 70%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .mcp-store-container:hover .mcp-store-shine {
        opacity: 1;
    }

    .user-info-button {
        background-color: #4f46e5 !important;
        color: white !important;
        border: none;
        &:hover {
            background-color: #4338ca !important;
        }
        .p-button-icon {
            color: white !important;
        }
    }

    .p-overlaypanel {
        min-width: 150px;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
        .p-button-danger {
            width: 100%;
            background-color: #ef4444 !important;
            border: none;
            &:hover {
                background-color: #dc2626 !important;
            }
        }
    }
}
</style>

<style lang="sass">
.app-navbar-wrap
  .topbar
    height: 70px
    padding: 0 32px
    background-color: #ffffff
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03)
</style>
