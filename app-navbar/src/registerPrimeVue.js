import But<PERSON> from "primevue/button";
import Dialog from "primevue/dialog";
import Tab<PERSON>iew from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import Card from "primevue/card";
import Message from "primevue/message";
import <PERSON>ubar from "primevue/menubar";
import Badge from "primevue/badge";
import OverlayPanel from 'primevue/overlaypanel'

const components = {
  Button,
  Dialog,
  TabView,
  TabPanel,
  Card,
  Message,
  Menubar,
  Badge,
  OverlayPanel
};

import Tooltip from "primevue/tooltip";
import BadgeDirective from "primevue/badgedirective";
const directives = {
  Tooltip,
  BadgeDirective,
};

export default function (app) {
  // 循环注册所有组件
  for (const [name, component] of Object.entries(components)) {
    app.component(name, component);
  }
  // 循环注册所有指令
  for (const [name, directive] of Object.entries(directives)) {
    app.directive(name, directive);
  }
}
