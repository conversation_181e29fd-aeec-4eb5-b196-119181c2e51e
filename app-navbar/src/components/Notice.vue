<template>
    <div class="dialog-notice">
        <transition
            name="notice-animation"
            appear
            enter-active-class="animate__animated animate__fadeIn"
            leave-active-class="animate__animated animate__fadeOut"
        >
            <div v-if="visible" class="modal-overlay">
                <div class="modal-container">
                    <div class="modal-header">
                        <div class="modal-title">
                            <i class="pi pi-bell bell-icon"></i>
                            <span>版本更新通知</span>
                        </div>
                        <button class="modal-close" @click="closeNotice">
                            <i class="pi pi-times"></i>
                        </button>
                    </div>
                    <div class="modal-content">
                        <div
                            class="markdown-content"
                            v-html="renderMarkdown(contentMD)"
                            @click="handleLinkClick"
                        ></div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-primary" @click="closeNotice">
                            我知道了
                        </button>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import MarkdownIt from 'markdown-it';
const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
});
import { httpService, storage } from '@dealer/utility-lib';
import 'animate.css';

export default {
    name: 'Notice',
    data() {
        return {
            version: '',
            contentMD: '',
            visible: false,
        };
    },
    methods: {
        closeNotice() {
            storage.updateLocalStorage('app', { version: this.version });
            this.visible = false;
        },
        handleLinkClick(event) {
            // 如果点击的是链接，保持原来的行为
            if (event.target.closest('a')) {
                storage.updateLocalStorage('app', { version: this.version });
                this.visible = false;
            }
        },
        renderMarkdown(markdownContent) {
            return md.render(markdownContent || '');
        },
    },
    async mounted() {
        try {
            const data = await httpService.get('/strapi-api/notice-mod');
            const { version, contentMD } = data.data.attributes;
            const storageVersion = storage.getLocalStorage('app', 'version'); // localStorage
            if (!storageVersion || storageVersion !== version) {
                this.version = version;
                this.visible = true;
                this.contentMD = contentMD;
            }
        } catch (err) {
            console.log(err);
        }
    },
};
</script>

<style lang="scss">
.dialog-notice {
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(3px);
    }

    .modal-container {
        width: 700px;
        max-width: 90vw;
        max-height: 85vh;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: modal-appear 0.3s ease-out;
    }

    .modal-header {
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: #303133;

        .bell-icon {
            margin-right: 10px;
            color: #2948ff;
            animation: bell-ring 2s infinite;
        }
    }

    .modal-close {
        background: transparent;
        border: none;
        color: #909399;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: #f5f7fa;
            color: #606266;
        }
    }

    .modal-content {
        padding: 0;
        overflow-y: auto;
        flex: 1;
    }

    .markdown-content {
        padding: 20px 24px;
        color: #606266;
        line-height: 1.6;

        h2 {
            font-size: 20px;
            color: #303133;
            margin-top: 0;
            margin-bottom: 16px;
            font-weight: 600;
        }

        h3 {
            font-size: 16px;
            color: #303133;
            margin-top: 20px;
            margin-bottom: 12px;
            font-weight: 600;
        }

        p {
            margin: 12px 0;
        }

        a {
            color: #2948ff;
            text-decoration: none;
            transition: all 0.2s;

            &:hover {
                text-decoration: underline;
            }
        }

        img {
            max-width: 100%;
            border-radius: 6px;
            margin: 12px 0;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        ul,
        ol {
            padding-left: 20px;

            li {
                margin: 6px 0;
            }
        }

        code {
            background-color: #f5f7fa;
            padding: 2px 4px;
            border-radius: 4px;
            color: #f56c6c;
        }

        pre {
            background-color: #f5f7fa;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;

            code {
                background-color: transparent;
                padding: 0;
                color: #606266;
            }
        }

        blockquote {
            border-left: 4px solid #dcdfe6;
            margin: 16px 0;
            padding: 0 16px;
            color: #909399;
        }
    }

    .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: flex-end;
    }

    .btn-primary {
        padding: 8px 20px;
        background: #2948ff;
        border: none;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background: #396afc;
            box-shadow: 0 2px 8px rgba(57, 106, 252, 0.3);
        }
    }
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bell-ring {
    0%,
    100% {
        transform: rotate(0);
    }
    5%,
    15% {
        transform: rotate(10deg);
    }
    10%,
    20% {
        transform: rotate(-10deg);
    }
    25% {
        transform: rotate(0);
    }
}

.notice-animation-enter-active {
    animation-duration: 0.4s;
}

.notice-animation-leave-active {
    animation-duration: 0.3s;
}
</style>
