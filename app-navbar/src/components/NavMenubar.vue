<template>
    <Menubar
        :model="menus"
        :pt="{
            root: 'w-full custom-menu-root',
            menuitem: 'custom-menuitem-class mb-0',
        }"
    >
        <template #start>
            <div class="navbar-logo">
                <router-link to="/">
                    <img
                        class="app-navbar-logo"
                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-navbar/ai_dev_logo_only.png"
                    />
                </router-link>
            </div>
        </template>
        <template #item="{ item, props, hasSubmenu, root }">
            <router-link
                v-if="item.route"
                v-slot="{ href, navigate }"
                :to="item.route"
                custom
            >
                <a
                    v-ripple
                    :href="href"
                    v-bind="props.action"
                    @click="navigate"
                    @mouseover="item.isHovered = true"
                    @mouseleave="item.isHovered = false"
                    class="navbar-item"
                    :class="{
                        'navbar-item--active': item.isActive,
                    }"
                >
                    <img
                        v-if="item.imgUrl"
                        :src="
                            item.isActive || item.isHovered
                                ? item.hoverImgUrl
                                : item.imgUrl
                        "
                        alt=" "
                        style="width: 20px; height: 20px"
                    />
                    <span
                        class="ml-2 flex-1 font-bold"
                        :class="{
                            'navbar-span--active': item.isActive,
                        }"
                        >{{ item.label }}</span
                    >
                    <Badge
                        v-if="item.badge"
                        :class="{ 'ml-auto': !root, 'ml-2': root }"
                        :value="item.badge"
                    />
                </a>
            </router-link>
            <a
                v-else
                v-ripple
                :href="item.url"
                :target="item.target"
                v-bind="props.action"
                @mouseover="item.isHovered = true"
                @mouseleave="item.isHovered = false"
                class="navbar-item"
                :class="{ 'navbar-item--active': item.isActive }"
            >
                <img
                    v-if="item.imgUrl"
                    :src="
                        item.isActive || item.isHovered
                            ? item.hoverImgUrl
                            : item.imgUrl
                    "
                    alt=" "
                    style="width: 20px; height: 20px"
                />
                <span
                    class="ml-2 flex-1 font-bold"
                    :class="{
                        'navbar-span--active': item.isActive,
                    }"
                    >{{ item.label }}</span
                >
                <Badge
                    v-if="item.badge"
                    :class="{ 'ml-auto': !root, 'ml-2': root }"
                    :value="item.badge"
                />
                <span
                    v-if="hasSubmenu"
                    class="pi pi-fw pi-angle-down ml-2"
                    :class="{
                        'navbar-span--active': item.isActive,
                    }"
                />
            </a>
        </template>
    </Menubar>
</template>

<script>
import { mapWritableState, mapActions } from 'pinia';
import useBadgeStore from '@/app/stores/badge';
import Ripple from 'primevue/ripple';

export default {
    name: 'NavMenubar',
    directives: {
        ripple: Ripple,
    },
    props: {
        menus: {
            type: Array,
            required: true,
        },
    },
    computed: {
        ...mapWritableState(useBadgeStore, ['unExposedCount']),
    },
    methods: {
        ...mapActions(useBadgeStore, ['getUnExposedCount']),
        updateHoveredState(currentPath) {
            function updateMenuItems(items) {
                items.forEach((item, index) => {
                    item.isActive = item.route === currentPath;

                    if (index === 1) {
                        if (
                            currentPath.indexOf('review') > -1 ||
                            currentPath.indexOf('gallery') > -1 ||
                            currentPath.indexOf('sonar') > -1
                        ) {
                            item.isActive = true;
                        } else {
                            item.isActive = false;
                        }
                    }

                    if (index === 2) {
                        if (currentPath.indexOf('aiqa') > -1) {
                            item.isActive = true;
                        } else {
                            item.isActive = false;
                        }
                    }
                });
            }
            updateMenuItems(this.menus);
        },
    },
    watch: {
        $route: {
            immediate: true,
            handler(newRoute) {
                this.updateHoveredState(newRoute.path);
            },
        },
    },
    async mounted() {
        await this.getUnExposedCount();
        this.$emit('update-badge', this.unExposedCount);
    },
};
</script>

<style lang="scss">
.custom-menuitem-class {
    margin-right: 4px;
}

.p-menuitem-link {
    transition: all 0.2s ease-in-out;
    border-radius: 6px;
    padding: 0.75rem 1rem !important;

    &:hover {
        background-color: #f3f4f6 !important;
        span.ml-2,
        .p-menuitem-text {
            color: #4f46e5 !important;
        }
    }
}

.custom-menu-root {
    .p-menubar-root-list {
        align-items: center;
    }

    .p-menuitem {
        margin: 0 4px;
    }

    .p-menuitem-content {
        border-radius: 6px;
        transition: background-color 0.2s ease-in-out;
        &:hover {
            background-color: #f3f4f6;
        }
    }

    .p-submenu-list {
        .p-menuitem-link {
            margin: 2px 0;
            border-radius: 6px;

            &:hover {
                background-color: #eef2ff !important;
                span.ml-2,
                .p-menuitem-text {
                    color: #4f46e5 !important;
                }
            }
        }
    }
}

.navbar-span--active {
    color: #4f46e5;
    font-weight: 600;
}

.navbar-item {
    position: relative;
    overflow: visible;
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
}

.navbar-item--active {
    background-color: #fff;
}

.navbar-item--active::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    border-radius: 2px;
    background-color: #4f46e5;
    opacity: 0.8;
}

.app-navbar-wrap .p-menubar {
    .p-menuitem-link {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    .navbar-item {
        span.ml-2 {
            font-size: 1rem;

            &:not(.navbar-span--active) {
                color: #26244ce0;
                font-weight: normal;
            }
        }
    }

    .p-submenu-list {
        .p-menuitem-link {
            .p-menuitem-text:not([class*='--active']),
            > span:not(.pi):not([class*='--active']) {
                font-size: 0.875rem;
                color: #26244ce0;
                font-weight: normal;
            }
        }
    }
}
</style>

<style lang="sass">
.app-navbar-wrap
  .p-menubar
    border: none
    background-color: transparent
    padding: 0
    .p-menubar-start
      margin-right: 40px
      .navbar-logo
        a
          display: flex
          align-items: center
          cursor: pointer
          font-size: 16px
          text-decoration: none
          color: #4f46e5
          transition: color 0.2s ease
          img
            width: 30px
            height: 30px
    .p-menubar-root-list
      .p-submenu-list
        min-width: 15rem
        border-radius: 8px
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08)
        padding: 0.5rem
        border: 1px solid #e5e7eb
      .p-badge
        font-size: 0.8rem
        border-radius: 12px
        padding: 0.2rem 0.4rem
</style>
