<template>
    <Dialog
        :visible="visible"
        @update:visible="$emit('update:visible', $event)"
        modal
        :style="{ width: '1200px' }"
        :pt="{
            root: {
                class: 'custom-dialog-root',
            },
            header: {
                class: 'p-4 pb-3 justify-content-center align-items-start relative',
            },
            content: {
                class: 'custom-dialog-content',
            },
            closeButton: 'absolute top-5 right-10',
        }"
        dismissableMask
    >
        <template #header>
            <div class="flex flex-column align-items-start">
                <div
                    class="inline-flex align-items-center justify-content-center gap-2 mb-3"
                >
                    <i class="pi pi-shop text-3xl" style="color: #6366f1"></i>
                    <span
                        class="font-bold white-space-nowrap text-2xl"
                        style="color: #6366f1"
                        >指令商店</span
                    >
                </div>
                <span class="p-text-secondary block text-base"
                    >选择心仪的提示词吧！</span
                >
            </div>
        </template>

        <TabView>
            <TabPanel v-for="(prompt, index) in content" :key="prompt.id">
                <template #header>
                    <div class="flex align-items-center gap-2">
                        <i v-if="index === 0" class="fa-solid fa-code"></i>
                        <i v-if="index === 1" class="fa-solid fa-briefcase"></i>
                        <span class="font-bold white-space-nowrap text-lg">{{
                            prompt.attributes.title
                        }}</span>
                    </div>
                </template>
                <div class="flex flex-wrap">
                    <Card
                        v-for="(p, idx) in prompt.attributes.data"
                        :key="idx"
                        style="
                            background-image: linear-gradient(
                                120deg,
                                #fdfbfb 0%,
                                #ebedee 100%
                            );
                            cursor: pointer;
                        "
                        @click="
                            usePrompt(
                                p.title.text,
                                p.content,
                                `${prompt.id}_${idx + 1}`
                            )
                        "
                    >
                        <template #title>
                            <i
                                v-if="index === 0"
                                class="fa-solid fa-code mr-2"
                            ></i>
                            <i
                                v-if="index === 1"
                                class="fa-solid fa-briefcase mr-2"
                            ></i>
                            <span class="text-lg">{{ p.title.text }}</span>
                        </template>
                        <template #content>
                            <div
                                class="card-content"
                                v-html="p.content"
                                v-tooltip.bottom="{
                                    value: stripHtmlTags(p.content),
                                    pt: {
                                        text: {
                                            style: {
                                                'max-width': '400px',
                                                'max-height': '300px',
                                            },
                                        },
                                    },
                                }"
                            ></div>
                        </template>
                    </Card>
                </div>
            </TabPanel>
        </TabView>
    </Dialog>
</template>

<script>
import { traceLog } from '@dealer/utility-lib';

export default {
    name: 'PromptDialog',
    props: {
        visible: {
            type: Boolean,
            required: true,
        },
        content: {
            type: Array,
            default: () => [],
        },
    },
    emits: ['update:visible', 'use-prompt'],
    methods: {
        stripHtmlTags(htmlString) {
            if (!htmlString) {
                return '';
            }
            return htmlString.replace(/<\/?[^>]+(>|$)/g, '');
        },
        usePrompt(title, content, id) {
            this.$emit('use-prompt', { title, content, id });
            this.$emit('update:visible', false);
            this.reportUserEvent(`click_appNavbar_promptBox_item_${id}`);
        },
        reportUserEvent: traceLog.reportUserEvent,
    },
};
</script>

<style lang="scss">
.custom-dialog-root {
    .custom-dialog-content {
        .p-tabview-panels {
            padding: 0;
        }

        .p-card {
            width: calc(25% - 3rem);
            margin: 1.5rem 1.5rem;

            .p-card-body {
                height: 100%;
                justify-content: space-between;

                .p-card-content {
                    margin-bottom: auto;
                }

                .card-content {
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 5;
                    /* 限制在两行 */
                    min-height: 44px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: normal;
                    margin: 0;

                    strong {
                        color: #6366f1;
                    }
                }

                .p-card-footer {
                    text-align: right;
                }
            }
        }
    }
}
</style>
