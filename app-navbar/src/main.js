import "./main.scss";

import { h, createApp } from "vue";
import singleSpaVue from "single-spa-vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";

// 
import '@fortawesome/fontawesome-free/css/all.css';

// PrimeVue
import 'primeicons/primeicons.css'
import PrimeVue from 'primevue/config';
import 'primevue/resources/themes/aura-light-indigo/theme.css'

import registerPrimeVue from './registerPrimeVue'

const vueLifecycles = singleSpaVue({
  createApp,
  appOptions: {
    render() {
      return h(App, {
        // single-spa props are available on the "this" object. Forward them to your component as needed.
        // https://single-spa.js.org/docs/building-applications#lifecycle-props
        // name: this.name,
        // mountParcel: this.mountParcel,
        // singleSpa: this.singleSpa,
      });
    },
  },
  handleInstance: async (app) => {
    // TODO : 第一时间获取 username -> 写入全局变量 window._appNavbar.username -> 
    window._appNavbar = {
      username: ''
    };
    try {
      const res = await fetch('/sso/username');
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const data = await res.json();
      window._appNavbar.username = data.username;
    } catch (err) {
      console.error(err)
    }

    app.use(router);
    app.use(createPinia());
    // PrimeVue
    app.use(PrimeVue, { ripple: true });
    registerPrimeVue(app)
  },
});

export const bootstrap = vueLifecycles.bootstrap;
export const mount = vueLifecycles.mount;
export const unmount = vueLifecycles.unmount;
