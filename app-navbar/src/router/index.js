import { createRouter, createWebHistory } from "vue-router";
import { traceLog } from "@dealer/utility-lib";

const routes = [
  {
    path: "/",
    redirect: "/app-chatgpt/",
  },
  // 下线app-aiqa项目, 合并到app-aiqa-web
  {
    path: "/app-aiqa",
    redirect: "/app-aiqa-web/",
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeResolve((to, from, next) => {
  if (to.fullPath !== from.fullPath) {
    const regCrDetail = /app-chatgpt\/codereview\/\d+/;
    /**
     * CodeReview 详情页 - 埋点上报格式
     *   eventName : PV_/app-chatgpt/codereview/detail
     *   eventTargent : 41718
     */
    if (to.fullPath.search(regCrDetail) > -1) {
      const regCrDetailId = /\d+/;
      const [url, query] = to.fullPath.split(regCrDetailId);
      const id = regCrDetailId.exec(to.fullPath)[0];
      let subjectName, eventName, eventTarget;
      subjectName = `PV_${url}detail`;
      eventName = query ? "from_dingding" : "from_list";
      eventTarget = id;
      traceLog.reportUserEvent(eventName, eventTarget, subjectName);
    } else {
      traceLog.reportUserEvent("PV_" + to.fullPath);
    }
  }
  next();
});

export default router;
