import { defineStore } from "pinia";
import { httpService } from "@dealer/utility-lib";

export default defineStore("badge", {
  state: () => ({
    unExposedCount: 0,
  }),
  getters: {},
  actions: {
    async initApp() {},
    async getUnExposedCount() {
      const url = "/api-cr/codeCommit/unExposedCount";
      try {
        const data = await httpService.get(url);
        this.unExposedCount = data.result;
        return data.result;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
  },
});
