// @tailwind base;
// @tailwind components;
@tailwind utilities;

html {
    height: 100%;
    overflow: hidden;
    font-size: 14px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji',
        'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important;
    margin: 0;

    textarea,
    input {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
            'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif,
            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
            'Noto Color Emoji' !important;
    }

    i {
        cursor: pointer;
    }
}

body {
    .app-root-wrap {
        min-width: 1200px;
    }
    .app-root-navigation {
        position: relative;
        background-color: #fff;
        box-shadow: 0 3px 10px rgba(62, 85, 120, 0.1);
        z-index: 10;
    }

    .app-root-pages {
        height: calc(100% - 60px);
        position: relative;
        z-index: 9;
    }

    .pi {
        cursor: pointer;
    }

    .p-component {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
            'Noto Color Emoji' !important;
    }
    // markdown-it
    .markdown-body {
        font-size: 1rem !important; // windows下某些粗体的字，在浏览器上看不清楚，比如《智慧》，所以字体大小调大一点，原来是14px
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
            'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
            'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
            'Noto Color Emoji' !important;
        letter-spacing: 0.4px !important;
        line-height: 1.7 !important;
        color: #26244ce0 !important;

        pre {
            overflow: initial;
            position: relative;
        }

        code {
            font-size: 0.875rem !important;
            letter-spacing: 0.3px !important;
            line-height: 2 !important;
        }

        tt {
            font-size: 0.875rem !important;
        }

        a {
            font-size: 0.875rem !important;
            font-weight: 500;
            text-decoration: none;
        }
    }
}

/* Google fonts - https://developers.google.com/fonts/docs/material_icons#setup_method_2_self_hosting */
// issue fix : could not find .woff and .woff2 files  - https://github.com/google/material-design-icons/issues/1092#issuecomment-954483603
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://z.autoimg.cn/dealer_microfe_aidev/libs/google/material-design-icons/font/MaterialIcons-Regular.eot);
    /* For IE6-8 */
    src: local('Material Icons'), local('MaterialIcons-Regular'),
        url(https://z.autoimg.cn/dealer_microfe_aidev/libs/google/material-design-icons/font/MaterialIcons-Regular.woff2)
            format('woff2'),
        url(https://z.autoimg.cn/dealer_microfe_aidev/libs/google/material-design-icons/font/MaterialIcons-Regular.woff)
            format('woff'),
        url(https://z.autoimg.cn/dealer_microfe_aidev/libs/google/material-design-icons/font/MaterialIcons-Regular.ttf)
            format('truetype');
}

.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    /* Preferred icon size */
    display: inline-block;
    line-height: 1;
    text-transform: none;
    letter-spacing: normal;
    word-wrap: normal;
    white-space: nowrap;
    direction: ltr;
    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;
    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;
    /* Support for IE. */
    font-feature-settings: 'liga';
}

/* Rules for sizing the icon. */
.material-icons.md-18 {
    font-size: 18px;
}

.material-icons.md-24 {
    font-size: 24px;
}

.material-icons.md-36 {
    font-size: 36px;
}

.material-icons.md-48 {
    font-size: 48px;
}

/* Rules for using icons as black on a light background. */
.material-icons.md-dark {
    color: rgba(0, 0, 0, 0.54);
}

.material-icons.md-dark.md-inactive {
    color: rgba(0, 0, 0, 0.26);
}

/* Rules for using icons as white on a dark background. */
.material-icons.md-light {
    color: rgba(255, 255, 255, 1);
}

.material-icons.md-light.md-inactive {
    color: rgba(255, 255, 255, 0.3);
}

/* 针对 Webkit 浏览器（如 Chrome, Safari） */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: transparent; /* 轨道背景色透明 */
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(136, 136, 136, 0.8); /* 灰色滚动条，带透明度 */
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(85, 85, 85, 0.8); /* 鼠标悬停时的颜色 */
}

/* 针对 Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(136, 136, 136, 0.8) transparent; /* 滚动条和轨道颜色 */
}

/* 针对 IE 和 Edge */
.scrollable-element {
    -ms-overflow-style: none;
}

.scrollable-element::-ms-scrollbar {
    width: 12px;
    height: 12px;
}

.scrollable-element::-ms-scrollbar-track {
    background: transparent;
    border-radius: 10px;
}

.scrollable-element::-ms-scrollbar-thumb {
    background: rgba(136, 136, 136, 0.8);
    border-radius: 10px;
}

.scrollable-element::-ms-scrollbar-thumb:hover {
    background: rgba(85, 85, 85, 0.8);
}
