<template>
  <div class="app-demo-wrap">
    <section class="intro">
      <h1>Vue3-Demo</h1>
      <p>当前应用为vue应用, 下面示例是路由, naive ui 组件, chat接口示例</p>
    </section>
    <nav>
      <router-link to="/">Home</router-link>
      <router-link to="/about">About</router-link>
    </nav>

    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: "App",
  components: {},
};
</script>

<style lang="sass" scoped>
.app-demo-wrap
  color: #2c3e50
  margin-top: 60px
  .intro
    text-align: center
    h1
      font-size: 20px
      margin-bottom: 20px
  nav
    margin: 50px 0 20px
    a
      margin:0 10px
</style>
