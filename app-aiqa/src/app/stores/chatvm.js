import { defineStore } from "pinia";
// import httpService from "@/app/utils/httpService";

export default defineStore("chatvm", {
  state: () => ({
    chatForm: {
      data: {
        message: "",
      },
      schema: {
        message: "required",
      },
      textarea: {},
      submit: {
        disabled: false,
      },
      eventSource: {
        close: function () {},
      },
      stop: {
        visibility: false,
      },
    },
  }),
  actions: {
    stopSendMessage() {
      this.chatForm.eventSource.close();
      this.chatForm.stop.visibility = false;
    },
  },
});
