import { defineStore } from "pinia";
import httpService from "@/app/utils/httpService";

export default defineStore("app", {
  state: () => ({
    username: "<PERSON>",
    appConfig: {
      contextNum: -1, // -1代表全部
      // 是否开启推荐
      recommended: {
        name: "关闭",
        value: 0,
      },
      recommendedItems: [
        {
          name: "关闭",
          value: 0,
        },
        {
          name: "开启",
          value: 1,
        },
      ],
      shortcuts: {
        sendChatMessage: "enter",
        lineWrap: "shift_enter",
        history: {
          prev: "shift_up",
          next: "shift_down",
        },
        magic: "/",
      },
    },
  }),
  getters: {},
  actions: {
    async initApp() {
      await this.getUsername();
      const config = localStorage.getItem("app-aiqa");
      if (config) {
        this.setAppConfig(JSON.parse(config));
      }
    },
    async getUsername() {
      const url = "/sso/username";
      try {
        const data = await httpService.get(url);
        this.username = data.username;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    setAppConfig(data) {
      const _data = Object.assign({}, this.appConfig, data);
      localStorage.setItem("app-aiqa", JSON.stringify(_data));
      this.appConfig = _data;
    },
  },
});
