import { defineStore } from "pinia";
import httpService from "@/app/utils/httpService";

export default defineStore("prompt", {
  state: () => ({
    prompt: {},
    promptList: {
      returncode: 0,
      message: "",
      result: {
        pagecount: 5,
        pageindex: 1,
        rowcount: 45,
        list: [
          // {
          //   id: "",
          //   type: "", //（1用例生成、2需求可落地性）
          //   name: "",
          //   desc: "",
          //   createUser: "",
          //   createdStime: "",
          //   modifiedStime: "",
          //   promptContent: "",
          //   sessionMarkType: 1,
          //   sessionMarkName: "",
          //   sessionMarkTagName: "",
          // },
        ],
      },
    },
  }),
  actions: {
    async getPromptList(params) {
      const _params = {
        pageIndex: 1,
        pageSize: 10,
        ...params,
      };
      const url = "/aiqa-api/prompt/list";
      try {
        const data = await httpService.get(url, _params);
        this.promptList = data;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async addPrompt(body) {
      const url = "/aiqa-api/prompt/add";
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async editPrompt(body, params) {
      const url = "/aiqa-api/prompt/edit";
      try {
        const data = await httpService.post(url, body, params);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async deletePrompt(params) {
      const url = "/aiqa-api/prompt/del";
      const body = {};
      try {
        const data = await httpService.post(url, body, params);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
  },
});
