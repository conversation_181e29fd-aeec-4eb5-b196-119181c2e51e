import {
  Form as <PERSON>ee<PERSON><PERSON>,
  Field as <PERSON><PERSON><PERSON><PERSON>,
  defineRule,
  ErrorMessage,
  configure,
} from "vee-validate";
import {
  required,
  min,
  max,
  alpha_spaces,
  email,
  numeric,
  min_value,
  max_value,
  confirmed,
  not_one_of,
} from "@vee-validate/rules";

export default {
  install(app) {
    app.component("VeeForm", VeeForm);
    app.component("Vee<PERSON>ield", VeeField);
    app.component("ErrorMessage", ErrorMessage);

    defineRule("required", required);
    defineRule("min", min);
    defineRule("max", max);
    defineRule("alpha_spaces", alpha_spaces);
    defineRule("email", email);
    defineRule("numeric", numeric);
    defineRule("min_value", min_value);
    defineRule("max_value", max_value);
    defineRule("confirmed", confirmed);
    defineRule("not_one_of", not_one_of);

    configure({
      generateMessage: (ctx) => {
        const messages = {
          required: `This field ${ctx.field} is required`,
          min: `This field ${ctx.field} is too short`,
          max: `This field ${ctx.field} is too long`,
          alpha_spaces: `This field ${ctx.field} may only contain alphabetical character`,
        };
        const message = messages[ctx.rule.name]
          ? messages[ctx.rule.name]
          : `This field ${ctx.field} is invalid`;
        return message;
      },
    });
  },
};
