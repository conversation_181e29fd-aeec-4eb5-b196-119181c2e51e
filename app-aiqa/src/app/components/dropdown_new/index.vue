<template>
  <section class="l-dropdown">
    <div class="l-dropdown-toggle">
      <div class="l-dropdown-toggle-inner">
        <slot name="toogle"></slot>
      </div>
      <slot name="toogle-custom"></slot>
    </div>

    <div class="l-dropdown-content">
      <div class="l-dropdown-content-inner">
        <slot name="content"> </slot>
      </div>
      <slot name="content-custom"></slot>
    </div>
  </section>
</template>

<script>
export default {
  name: "Dropdownnew",
};
</script>

<style lang="scss">
.l-dropdown {
  position: relative;
  z-index: 9;
  .l-dropdown-content {
    position: absolute;
    left: 20px;
    top: 20px;
    background: #fff;
    z-index: 99;
  }
}
</style>
