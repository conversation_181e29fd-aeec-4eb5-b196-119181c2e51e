@import "@/app/components/modal/index";
@import "@/app/components/button/index";
@import "@/app/components/form/index";
@import "@/app/components/table/index";
@import "@/app/components/card/index";
@import "@/app/components/pagination/index";
@import "@/app/components/markdown-it/index";
@import "@/app/components/toast/index";
@import "@/app/components/badge/index";
@import "@/app/components/responsive/index";

.home-wrap {
  .markdown-body pre code,
  .markdown-body pre tt {
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
  }
}

/* Extra small devices (phones, 600px and down) */
@media (max-width: 750px) {
  body {
    // background-color: lightblue;
    .home-wrap {
      overflow: hidden;
      .chat-list {
        left: -350px;
        width: 258px;
        background-color: #fff;
        padding: 0 8px;
        z-index: 11;
        transition: left 0.2s ease-in-out;
      }
      .chat-content {
        margin-left: 16px;
        margin-top: 20px;
        .status {
          display: none;
        }
      }
      .btn-chatlist-toggle {
        display: none;
      }
      .mobile-toggle {
        position: absolute;
        left: 0;
        top: 0px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        height: 40px;
        background-color: #fff;
        z-index: 11;
        .material-icons {
          cursor: pointer;
          margin: 0 10px 0 20px;
        }
      }
      .home-wrap-bg {
        position: absolute;
        display: none;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 10;
        .material-icons {
          position: absolute;
          right: 10px;
          top: 30px;
          cursor: pointer;
          color: #eee;
          z-index: 9;
        }
      }
    }

    .home-wrap-mobile {
      .chat-list {
        left: 0;
        top: 40px;
        height: calc(100% - 20px);
      }
      .mobile-toggle {
        opacity: 1;
      }
      .home-wrap-bg {
        display: block;
      }
    }
  }
}

/* Medium devices (landscape tablets, 768px and up) */
@media (min-width: 751px) {
  body {
    // background-color: orange;
    .home-wrap {
      .mobile-toggle {
        display: none;
      }
      .home-wrap-bg {
        .material-icons {
          display: none;
        }
      }
    }
  }
}
