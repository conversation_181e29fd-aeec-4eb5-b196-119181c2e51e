<template>
  <div class="home-wrap card">
    <page-home-list />
    <page-home-content />
    <button-chat-toggle />
    <div class="mobile-toggle">
      <i @click.prevent="mobileToggle" class="material-icons"> menu </i>
      <span>{{ session.sessionName }}</span>
    </div>
    <div class="home-wrap-bg">
      <i class="material-icons">close</i>
    </div>
  </div>
</template>

<script>
import { mapState } from "pinia";
import useChatStore from "@/app/stores/chat";

import PageHomeList from "./list/index.vue";
import PageHomeContent from "./content/index.vue";
import ButtonChatToggle from "@/pages/components/ButtonChatToggle/index.vue";

export default {
  name: "PageHome",
  components: {
    PageHomeList,
    PageHomeContent,
    ButtonChatToggle,
  },
  computed: {
    ...mapState(useChatStore, ["session"]),
  },
  methods: {
    mobileToggle() {
      const $homeWrap = document.querySelector(".home-wrap");
      const $homeWrapBg = document.querySelector(".home-wrap-bg");
      if ($homeWrap.classList.contains("home-wrap-mobile")) {
        $homeWrap.classList.remove("home-wrap-mobile");
        $homeWrapBg.removeEventListener("click", this.mobileToggle);
      } else {
        $homeWrap.classList.add("home-wrap-mobile");
        $homeWrapBg.addEventListener("click", this.mobileToggle);
      }
    },
  },
};
</script>

<style lang="sass" scoped>
.home-wrap
  position: relative
  font-size: 14px
  background-color: #fff
  box-shadow: none
  padding: 20px 0
  margin: 0
  z-index: 9
  .messages
    height: 500px
    background-color: #eee
  .user-input
    z-index: 9
    .notice
      color: #b33100
    .message-setting
      display: flex
      align-items: center
      height: 40px
      line-height: 40px
    .message-send
      position: relative
      text-align: right
      padding-right:70px
      margin-top: 10px
      z-index: 9
      button
        position: absolute
        top: 0
        right: 0
        z-index: 99
</style>
