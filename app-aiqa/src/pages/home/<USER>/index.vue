<template>
  <section class="chat-content">
    <div class="chat-content-inner" :style="{ height: autoHeight }">
      <div class="conversation" ref="infiniteScroller">
        <section class="item" :class="{ 'item-highlight': highlightContext(index) }" v-for="(message, index) in messages"
          :key="message.id">
          <div class="user-query">
            <img class="pic" src="https://z.autoimg.cn/dealer_microfe_aidev/assets/pic_head_user_1.jpeg" alt="user" />
            <div class="content">{{ message.questionContent }}</div>
            <span class="button-gutter">
              <!-- <i class="material-icons">edit</i> -->
            </span>
          </div>
          <div class="model-response">
            <img class="pic" src="https://z.autoimg.cn/dealer_microfe_aidev/assets/pic_head_robot.jpg" alt="model" />
            <div class="content markdown-body" v-html="renderMarkdown(message.answerContent || '')"></div>
          </div>
        </section>
        <section class="stop" v-show="chatForm.stop.visibility">
          <button type="button" class="btn btn-warning" @click.prevent="stopSendMessage">
            <i class="material-icons"> stop </i>
            <span>停止当前会话</span>
          </button>
        </section>
      </div>
      <div class="user-input">
        <div class="status">
          <ul>
            <li>会话ID:{{ session.id }}</li>
            <li>
              <div class="form-group">
                <label for="contextNum">携带上下文数量</label>
                <select name="contextNum" class="form-control custom-select" v-model="appConfig.contextNum"
                  @input.prevent="updateContextNum">
                  <option value="0">0</option>
                  <option value="1">1</option>
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="-1">全部</option>
                </select>
              </div>
            </li>
          </ul>
        </div>
        <form @submit.prevent="sendMessage">
          <div class="input-area" @click.prevent="focusMessage">
            <textarea id="chat_input" name="message" as="textarea" rows="1" placeholder="请输入内容..."
              v-model="chatForm.data.message" @input.prevent="autoHeightChatForm"
              @keydown.enter.exact.prevent="sendMessage" />
          </div>
          <button class="submit" type="submit" :disabled="chatForm.submit.disabled"
            :class="{ disabled: chatForm.submit.disabled }">
            <i class="material-icons">send</i>
          </button>
        </form>
        <div class="disclaimer">
          <p>
            为确保信息安全，请遵守
            <a href="http://athm.cn/fQGg57c" target="_blank">《需求方向GPT安全规范》！</a>
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapState, mapActions, mapWritableState } from "pinia";
import useAppStore from "@/app/stores/app";
import useChatStore from "@/app/stores/chat";
import useChatvmStore from "@/app/stores/chatvm";

import MarkdownIt from "markdown-it";
const md = new MarkdownIt();

import { scrollToBottom } from "@/app/utils/index";

export default {
  name: "PageHomeContent",
  components: {},
  data() {
    return {
      autoHeight: `${window.innerHeight - 125}px` || "500px",
      sse: "",
    };
  },
  computed: {
    ...mapState(useAppStore, ["appConfig"]),
    ...mapWritableState(useChatStore, ["session", "messages"]),
    ...mapWritableState(useChatvmStore, ["chatForm"]),
  },
  methods: {
    ...mapActions(useAppStore, ["setAppConfig"]),
    ...mapActions(useChatStore, ["createMessage", "getMessageIds"]),
    ...mapActions(useChatvmStore, ["stopSendMessage"]),
    async sendMessage() {
      const values = this.chatForm.data;
      const sessionId = this.session.id;
      if (!sessionId) {
        this.$toast.warning("请先创建会话!");
        return;
      }
      this.chatForm.submit.disabled = true;
      const body = {
        questionContent: values.message,
        contextQuestionIds: this.getMessageIds(this.appConfig.contextNum) || [],
        sessionId,
        messageType: 0,
        temperature: 0.8,
        answerRole: "gpt-4",
      };
      const data = await this.createMessage(body);
      this.chatForm.data.message = "";
      this.chatForm.stop.visibility = true;

      const url2 = `/aiqa-gpt-api/sendMessage?messageId=${data.data.id}`;
      const source = new EventSource(url2);
      // 将重连间隔设置为一个非常大的值（例如24小时）
      source.retry = 24 * 60 * 60 * 1000;
      this.chatForm.eventSource = source;
      const message = data.data;
      const messageLenth = this.messages.length;
      this.messages[messageLenth] = message;
      this.$nextTick(() => {
        scrollToBottom(".chat-content-inner .conversation");
      });

      source.onopen = (err) => {
        console.log(err);
        console.log("The connection has been established.");
      };
      source.onmessage = (e) => {
        console.log("event : message");
        console.log("data:", e.data);
        const data = JSON.parse(e.data);
        const _message = this.messages[messageLenth];
        _message.answerContent = data.answerContent;
        _message.answerTime = data.answerTime;
        scrollToBottom(".chat-content-inner .conversation");
      };
      source.addEventListener("end", (e) => {
        console.log("event : end");
        console.log("data:", e.data);
        this.chatForm.submit.disabled = false;
        source.close();
        this.chatForm.stop.visibility = false;
      });
      // onerror version
      source.onerror = (err) => {
        console.log(err);
        console.log("An error occurred while attempting to connect.");
        this.chatForm.submit.disabled = false;
        source.close();
        this.chatForm.stop.visibility = false;
      };
    },
    focusMessage() {
      this.autoHeightChatForm();
    },
    // 参考 : https://stackoverflow.com/questions/17772260/textarea-auto-height
    autoHeightChatForm() {
      document.querySelector(".input-area textarea").style.height = "24px";
      document.querySelector(".input-area textarea").style.height =
        document.querySelector(".input-area textarea").scrollHeight + "px";
    },
    renderMarkdown(markdownContent) {
      return md.render(markdownContent);
    },
    highlightContext(index) {
      const contextNum = this.appConfig.contextNum;
      return index >= this.messages.length - contextNum;
    },
    updateContextNum() {
      setTimeout(() => {
        const contextNum = this.appConfig.contextNum;
        const msgNum =
          contextNum == "-1"
            ? "聊天时携带全部上下文"
            : `聊天时携带${contextNum}条聊天上下文（浅绿底色的聊天内容）`;
        this.setAppConfig({ contextNum });
        this.$toast.success(msgNum);
      }, 500);
    },
    handleResize() {
      this.autoHeight = `${window.innerHeight - 125}px` || "500px";
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  unmounted() {
    window.removeEventListener('resize', this.handleResize);
  }
};
</script>

<style lang="sass" scoped>
.chat-content
  width: auto
  background-color: #f4f7fc
  border-radius: 24px
  margin: 0 16px 0 320px
  z-index: 9
  .chat-content-inner
    display: flex
    flex-direction: column
    padding: 0 16px
    margin: 0 auto
    .conversation
      position: relative
      flex: 1
      overflow-y: auto
      scroll-behavior: smooth
      section.item
        max-width: 1200px
        margin: 0 auto
        .user-query
          display: flex
          justify-content: space-between
          align-items: flex-start
          padding: 16px 0 16px 12px
          .pic
            display: inline-block
            width: 32px
            height: 32px
            border-radius: 50%
            margin-right: 10px
          .content
            flex: 1
            line-height: 32px
            white-space: pre-wrap
          .button-gutter
            width: 45px
            .material-icons
              cursor: pointer
              font-size: 20px
        .model-response
          display: flex
          justify-content: space-between
          align-items: flex-start
          background-color: #fff
          border-radius: 16px
          padding: 12px
          .pic
            display: inline-block
            width: 32px
            height: 32px
            margin-right: 10px
          .content
            flex: 1
            line-height: 32px
      section.item-highlight
        background-color: #ccedd2
      section.stop
        position: sticky
        left: 0
        bottom: 0
        display: flex
        width: 100%
        justify-content: center
        z-index: 99
        button
          display: flex
          justify-content: center
          align-items: center
    .user-input
      width: 95%
      max-width: 1200px
      color: #444746
      padding: 25px 0px 15px
      margin: 0 auto
      z-index: 9
      .status
        padding: 0 20px 20px
        ul
          display: flex
          justify-content: flex-start
          align-items: center
          list-style: none
          padding: 0
          margin: 0
          li
            margin-right: 20px
            .form-group
              display: flex
              justify-content: flex-start
              align-items: center
              margin-bottom: 0
              label
                width: 105px
                margin-bottom: 0
              .custom-select
                width: 70px
                height: 30px
                line-height: 30px
                font-size: 14px
                text-align: center
                padding: 0 30px 0 10px

      form
        display: flex
        justify-content: center
        align-items: center
        .input-area
          flex:1
          height: auto
          background-color: #fff
          border: 1px solid #757b7f
          border-radius: 40px
          padding: 16px 25px
          textarea
            display: block
            width: 100%
            color: #3c4043
            min-height: 24px
            height: 24px
            line-height: 24px
            max-height: 96px
            padding: 0
            border: none
            outline: none
            resize: none
          textarea:focus
            background-color: lightyellow
        .submit
          width: 48px
          height: 48px
          color: #0b57d0
          cursor: pointer
          background-color: transparent
          border: none
          border-radius: 50%
          margin-left: 10px
        .submit:hover
          background-color: #ecf1fa
        .submit:active
          background-color: #dce6f7
        .submit.disabled
          color: #000
          cursor: auto
          opacity:0.3
        .submit.disabled:hover,.submit.disabled:active
          background-color: transparent
      .disclaimer
        font-size: 12px
        text-align: center
        margin-top: 20px
        z-index: 9
        a
          color: #037afb
          text-decoration: none
      .notice
        font-size: 12px
        text-align: center
        margin-bottom: 0
</style>
