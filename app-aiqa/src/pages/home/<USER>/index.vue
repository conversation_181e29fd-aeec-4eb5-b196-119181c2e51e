<template>
  <section class="chat-prompt">
    <!-- button -->
    <section>
      <button
        class="btn btn-link"
        type="button"
        @click.prevent="dialog.list.visibility = true"
      >
        提示词列表
      </button>
    </section>

    <!-- 弹窗 : 提示词列表 (Start)  -->
    <teleport to="body">
      <section class="modal model-prompt-list" v-show="dialog.list.visibility">
        <div class="modal-dialog modal-xl modal-dialog-centered">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.list.title }}
            </h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <i
                class="material-icons"
                @click.prevent="dialog.list.visibility = false"
                >close</i
              >
            </button>
          </div>
          <div class="modal-body">
            <div class="filter">
              <form @submit.prevent="submitPromptList">
                <div class="form-group">
                  <label for="type">类型</label>
                  <select
                    name="type"
                    class="form-control custom-select"
                    v-model="dialog.list.data.type"
                  >
                    <option value="">全部</option>
                    <option value="1">用例生成</option>
                    <option value="2">需求可落地性</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="name">标题</label>
                  <input
                    class="form-control"
                    name="name"
                    type="text"
                    placeholder="请输入名称"
                    v-model="dialog.list.data.name"
                  />
                </div>
                <div class="form-group">
                  <label for="promptContent">题词内容</label>
                  <input
                    class="form-control"
                    name="promptContent"
                    type="text"
                    placeholder="请输入名称"
                    v-model="dialog.list.data.promptContent"
                  />
                </div>
                <div class="form-group">
                  <label for="createUser">创建人</label>
                  <input
                    class="form-control"
                    name="createUser"
                    type="text"
                    placeholder="请输入名称"
                    v-model="dialog.list.data.createUser"
                  />
                </div>
                <div class="form-group">
                  <button
                    class="btn btn-primary"
                    type="submit"
                    @click.prevent="() => submitPromptList()"
                  >
                    查询
                  </button>
                </div>
              </form>
            </div>
            <div class="content">
              <p>
                <button
                  class="btn btn-link"
                  type="button"
                  @click.prevent="showPromptDialog('create')"
                >
                  新增提示词
                </button>
              </p>
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th scope="col">ID</th>
                      <th scope="col" width="50">类型</th>
                      <th scope="col" width="100">标题</th>
                      <th scope="col">题词内容</th>
                      <th scope="col">创建人</th>
                      <th scope="col">创建时间</th>
                      <th scope="col" width="140">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="prompt in promptList.result.list"
                      :key="prompt.id"
                    >
                      <th scope="row">{{ prompt.id }}</th>
                      <td>{{ promptTypeMap(prompt.type) }}</td>
                      <td>{{ prompt.name }}</td>
                      <td class="prompt-content">{{ prompt.promptContent }}</td>
                      <td>{{ prompt.createUser }}</td>
                      <td>{{ prompt.createdStime }}</td>
                      <td class="action">
                        <button
                          class="btn btn-link"
                          type="button"
                          @click.prevent="insertPrompt(prompt)"
                        >
                          引用
                        </button>
                        <button
                          class="btn btn-link"
                          type="button"
                          @click.prevent="showPromptDialog('edit', prompt)"
                        >
                          编辑
                        </button>
                        <button
                          class="btn btn-link"
                          type="button"
                          @click.prevent="showDeleteDialog(prompt)"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <nav aria-label="Page navigation example">
                <ul class="pagination justify-content-end">
                  <li
                    class="page-item"
                    :class="{ disabled: promptList.result.pageindex === 1 }"
                    @click.prevent="
                      submitPromptList(promptList.result.pageindex - 1)
                    "
                  >
                    <a class="page-link" href="#">上一页</a>
                  </li>
                  <li
                    class="page-item"
                    v-for="index in promptList.result.pagecount"
                    :key="index"
                    :class="{ active: index === promptList.result.pageindex }"
                    @click.prevent="submitPromptList(index)"
                  >
                    <a class="page-link" href="#">{{ index }}</a>
                  </li>
                  <li
                    class="page-item"
                    :class="{
                      disabled:
                        promptList.result.pageindex ===
                        promptList.result.pagecount,
                    }"
                    @click.prevent="
                      submitPromptList(promptList.result.pageindex + 1)
                    "
                  >
                    <a class="page-link" href="#">下一页</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>
    <!-- 弹窗 : 提示词列表 (End)  -->

    <!-- 弹窗 : 提示词 - 创建&编辑(Start)  -->
    <teleport to="body">
      <section class="modal model-prompt-edit" v-show="dialog.edit.visibility">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.edit.title }}
            </h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <i
                class="material-icons"
                @click.prevent="dialog.edit.visibility = false"
                >close</i
              >
            </button>
          </div>
          <!-- 表单组件开始 -->
          <div class="modal-body">
            <form>
              <div class="form-group">
                <label for="type">类型</label>
                <select
                  name="type"
                  class="form-control custom-select"
                  v-model="dialog.edit.data.type"
                >
                  <option value="1">用例生成</option>
                  <option value="2">需求可落地性</option>
                </select>
              </div>
              <div class="form-group">
                <label for="name">标题</label>
                <input
                  class="form-control"
                  name="name"
                  type="text"
                  placeholder="请输入名称"
                  v-model="dialog.edit.data.name"
                />
              </div>
              <div class="form-group">
                <label for="promptContent">提示词内容</label>
                <textarea
                  class="form-control"
                  name="promptContent"
                  row="4"
                  placeholder="请输入提示词内容"
                  v-model="dialog.edit.data.promptContent"
                />
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              type="button"
              @click.prevent="dialog.edit.visibility = false"
            >
              取消
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              @click.prevent="sumbitPromptDialog"
            >
              确认
            </button>
          </div>
          <!-- 表单组件结束 -->
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>
    <!-- 弹窗 : 提示词 - 创建&编辑(End)  -->

    <!-- 弹窗 : 二次确认 (Start)  -->
    <teleport to="body">
      <section class="modal" v-show="dialog.confirm.visibility">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.confirm.title }}
            </h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <i
                class="material-icons"
                @click.prevent="dialog.confirm.visibility = false"
                >close</i
              >
            </button>
          </div>
          <div class="modal-body">{{ dialog.confirm.content }}</div>
          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              type="button"
              @click.prevent="dialog.confirm.visibility = false"
            >
              取消
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              @click.prevent="deletePromptDialog"
            >
              确认
            </button>
          </div>
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>
    <!-- 弹窗 : 二次确认 (End)  -->
  </section>
</template>

<script>
import { mapWritableState, mapActions } from "pinia";
import usePromptStore from "@/app/stores/prompt";
import useChatvmStore from "@/app/stores/chatvm";

export default {
  name: "PageHomePrompt",
  data() {
    return {
      dialog: {
        list: {
          title: "提示词列表",
          visibility: false,
          activeIndex: 0,
          data: {
            id: "",
            type: "",
            name: "",
            desc: "",
            createUser: "",
            promptContent: "",
            pageIndex: 1,
            pageSize: 10,
          },
        },
        edit: {
          title: "新建",
          visibility: false,
          type: "create",
          data: {
            id: null,
            type: 1, //（1用例生成、2需求可落地性）
            name: "",
            promptContent: "",
          },
        },
        confirm: {
          title: "删除",
          visibility: false,
          content: "您确认要删除吗?",
        },
      },
    };
  },
  computed: {
    ...mapWritableState(usePromptStore, ["promptList"]),
    ...mapWritableState(useChatvmStore, ["chatForm"]),
  },
  methods: {
    ...mapActions(usePromptStore, [
      "getPromptList",
      "addPrompt",
      "editPrompt",
      "deletePrompt",
    ]),
    async init() {
      await this.getPromptList();
    },
    promptTypeMap(typeId) {
      let result = "";
      switch (typeId) {
        case 1:
          result = "用例生成";
          break;
        case 2:
          result = "需求可落地性";
          break;
        default:
          result = "用例生成";
          break;
      }
      return result;
    },
    async submitPromptList(pageindex) {
      if (pageindex) {
        if (pageindex > this.promptList.result.pagecount || pageindex < 1) {
          return;
        }
        this.dialog.list.data.pageIndex = pageindex;
      }
      await this.getPromptList(this.dialog.list.data);
    },
    showPromptDialog(type, data) {
      let options;
      if (type === "create") {
        options = {
          title: "新建",
          visibility: true,
          type: "create",
          data: {
            type: 1, //（1用例生成、2需求可落地性）
            name: "",
            promptContent: "",
          },
        };
      } else {
        options = {
          title: "编辑",
          visibility: true,
          type: "edit",
          data: Object.assign({}, data),
        };
      }
      this.dialog.edit = options;
    },
    async sumbitPromptDialog() {
      console.log("sumbitEditDialog");
      const type = this.dialog.edit.type;
      const body = this.dialog.edit.data;
      if (type === "create") {
        await this.addPrompt(body);
      } else {
        await this.editPrompt(body, { id: body.id });
      }
      this.dialog.edit.visibility = false;
      this.init();
    },
    insertPrompt(data) {
      this.chatForm.data.message = data.promptContent;
      this.dialog.list.visibility = false;
      // todo : 手动触发一下 textarea 的 input 事件 (加个控制)
    },
    showDeleteDialog(data) {
      this.dialog.confirm.visibility = true;
      this.dialog.edit.data = data;
    },
    async deletePromptDialog() {
      const id = this.dialog.edit.data.id;
      await this.deletePrompt({ id });
      this.dialog.confirm.visibility = false;
      this.init();
    },
  },
  created() {
    this.init();
  },
};
</script>

<style lang="sass" scoped>
.model-prompt-list
  .modal-body
    .filter
      form
        display: flex
        justify-content: center
        align-items: center
        .form-group
          flex: 1
          display: flex
          justify-content: center
          align-items: center
          margin-right: 20px
          label
            min-width: 60px
            max-width: 80px
            text-align: right
            margin-right: 10px
        .form-group:last-child
          flex: none
          width: 50px
    .content
      .table-responsive
        max-height: 500px
        overflow-y: auto
      td.prompt-content

      td.action
        a
          margin: 0 5px
.model-prompt-edit
  .modal-body
    textarea.form-control
      height: 118px
</style>
