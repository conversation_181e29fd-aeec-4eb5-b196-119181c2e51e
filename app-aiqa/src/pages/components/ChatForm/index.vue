<template>
  <section class="chat-content">
    <div class="chat-content-inner" :style="{ height: autoHeight }">
      <div class="conversation" ref="infiniteScroller">
        <section class="item" v-for="message in messages" :key="message.id">
          <div class="user-query">
            <img class="pic" src="https://z.autoimg.cn/dealer_microfe_aidev/assets/pic_head_user_1.jpeg" alt="user" />
            <div class="content">{{ message.questionContent }}</div>
          </div>
          <div class="model-response">
            <img class="pic" src="https://z.autoimg.cn/dealer_microfe_aidev/assets/pic_head_robot.jpg" alt="model" />
            <div class="content markdown-body" v-html="renderMarkdown(message.answerContent || '')"></div>
          </div>
        </section>
        <p v-if="messages.length === 0">无聊天内容...</p>
      </div>
    </div>
  </section>
</template>

<script>
import MarkdownIt from "markdown-it";
const md = new MarkdownIt();

export default {
  name: "ChatForm",
  components: {},
  props: {
    messages: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      autoHeight: `${window.innerHeight - 40 - 10 - 65}px` || "500px",
      sse: "",
    };
  },
  methods: {
    renderMarkdown(markdownContent) {
      return md.render(markdownContent);
    },
  },
  mounted() { },
};
</script>

<style lang="sass" scoped>
.chat-content
  width: auto
  background-color: #f4f7fc
  border-radius: 24px
  margin: 0 16px 0 16px
  z-index: 9
  .chat-content-inner
    display: flex
    flex-direction: column
    padding: 0 16px
    margin: 0 auto
    .conversation
      position: relative
      flex: 1
      overflow-y: auto
      scroll-behavior: smooth
      section.item
        max-width: 1200px
        margin: 0 auto
        .user-query
          display: flex
          justify-content: space-between
          align-items: flex-start
          padding: 16px 0 16px 12px
          .pic
            display: inline-block
            width: 32px
            height: 32px
            border-radius: 50%
            margin-right: 10px
          .content
            flex: 1
            line-height: 32px
            white-space: pre-wrap
          .button-gutter
            width: 45px
            .material-icons
              cursor: pointer
              font-size: 20px
        .model-response
          display: flex
          justify-content: space-between
          align-items: flex-start
          background-color: #fff
          border-radius: 16px
          padding: 12px
          .pic
            display: inline-block
            width: 32px
            height: 32px
            margin-right: 10px
          .content
            flex: 1
            line-height: 32px
</style>
