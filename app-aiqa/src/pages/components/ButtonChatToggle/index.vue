<!-- 折叠会话列表 -->
<template>
  <div class="btn-chatlist-toggle">
    <span></span>
    <span></span>
  </div>
</template>

<script>
export default {
  name: "ButtonChatToggle",
  mounted() {
    const $listToggle = document.querySelector(".btn-chatlist-toggle");
    $listToggle.addEventListener("click", function () {
      this.classList.toggle("hide-chatlist");
      document
        .querySelector(".home-wrap .chat-content")
        .classList.toggle("hide-chatlist");
      document
        .querySelector(".home-wrap .chat-list")
        .classList.toggle("hide-chatlist");
    });
  },
};
</script>

<style lang="sass">
.btn-chatlist-toggle
  position: absolute
  left: 295px
  top: 40%
  width: 32px
  cursor: pointer
  z-index: 9
  span
    display: block
    width: 4px
    height: 38px
    background-color: #b7b7b7
    border-radius: 2px
    margin: 0 auto
    transition: all 0.3s ease-in-out
  span:last-child
    margin-top: -2px
.btn-chatlist-toggle:hover
  span
    background-color: #8e8e8e
  span:first-child
    transform: rotate(12deg) scale(1.15) translateY(-2px)
  span:last-child
    transform: rotate(-12deg) scale(1.15) translateY(2px)

.btn-chatlist-toggle.hide-chatlist
  left: 20px
.btn-chatlist-toggle.hide-chatlist:hover
  span:first-child
    transform: rotate(-12deg) scale(1.15) translateY(-2px)
  span:last-child
    transform: rotate(12deg) scale(1.15) translateY(2px)

.home-wrap
  .chat-list.hide-chatlist
    left: -350px
  .chat-content.hide-chatlist
    margin-left : 16px
</style>
