const { defineConfig } = require("@vue/cli-service");
const path = require("path");
// const ftriSso = require("@athm-dealer.arch/ftrig-sso");

module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    devServer: {
      compress: false,
    },
    output: {
      libraryTarget: "system",
    },
  },
});
