{"license": "MIT", "homepage": "http://actionanand.github.io/single-spa-utility", "scripts": {"develop": "webpack serve --port 9301", "develop:standalone": "webpack serve --env standalone", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js", "format": "prettier --write .", "check-format": "prettier --check .", "prepare": "husky install", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage", "predeploy": "yarn run build:webpack", "deploy": "gh-pages -d dist"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/runtime": "^7.15.3", "babel-jest": "^27.0.6", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-important-stuff": "^1.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "gh-pages": "^4.0.0", "husky": "^7.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^27.0.6", "jest-cli": "^27.0.6", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-config-single-spa": "^5.0.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@types/jest": "^27.0.1", "@types/systemjs": "^6.1.1", "highlight.js": "^11.9.0", "markdown-it": "^14.1.0"}}