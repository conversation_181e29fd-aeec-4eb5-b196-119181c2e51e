// utils

/**
 * markdown-it : md文档渲染组件
 */
import markdownit from "markdown-it";
import hljs from "highlight.js"; // https://highlightjs.org
import "highlight.js/styles/github.css"; // 引入 highlight.js 的样式文件
import '@/css/highlight-default.min.css'; // 本地引入default样式，避免从CDN加载
import '@/css/markdownit.css'

// 禁用highlight.js自动加载CDN样式
hljs.configure({
  noAutodetect: true,
  cssSelector: 'none', // 防止自动加载样式
});

export const md = new markdownit({
  highlight: function (str, lang) {
    let code
    if (lang && hljs.getLanguage(lang)) {
      try {
        code = '<pre><code class="hljs">' +
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
          "</code><div class='copy-button'>copy</div></pre>"
      } catch (__) {}
    } else {
      code = '<pre><code class="hljs">' + md.utils.escapeHtml(str) + "</code></pre>"
    }

    return code
  },
});

/**
 * 用于修复GPT4-turbo, markdown格式嵌套错误
 * 见issue : https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/218
 */
export const fixMarkdownCodeBlocks = function (markdownString) {
  let result = markdownString;
  const regex = /```markdown[\s\S]*?(?:```[\s\S]*?```[\s\S]*?)*```/gm;
  if (regex.test(markdownString)) {
    result = markdownString
      .replace(/^```markdown/, "````markdown")
      .replace(/```$/, "````");
  }
  return result;
};

export const noLoginRequired = function () {
  return location.search.indexOf("authCode") > -1 ? true : false;
};


/**
 * 通过url判断当前环境 (本地 测试 线上)
 * @param {String} url
 */
export const detectEnv = function (url) {
  let result = "";
  const regIsLocalEnv = /localhost/;
  const regIsTestEnv = /\.mulan\./;
  const regIsOnlineEnv = /aidev.*\.corpautohome\.com/; // 预发和线上,都统一识别为线上

  if (url.search(regIsLocalEnv) > -1) {
    result = "local";
  }
  if (url.search(regIsTestEnv) > -1) {
    result = "test";
  }
  if (url.search(regIsOnlineEnv) > -1) {
    result = "online";
  }
  return result;
};

/**
 * 获取各环境下的配置
 */
export const getEnvConfig = function (env) {
  let result;
  const config = {
    local: {
      url: "http://localhost:9000",
    },
    test: {
      url: "http://aidev-test.mulan.corpautohome.com",
    },
    online: {
      url: "https://aidev.corpautohome.com",
    },
  };
  return result;
};

export const scrollToBottom = function (selector) {
  const element = document.querySelector(selector);
  element.style.scrollBehavior = "smooth";
  element.scrollTop = element.scrollHeight;
};
