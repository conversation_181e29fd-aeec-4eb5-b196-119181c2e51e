@media (prefers-color-scheme: dark) {
  body {
    .markdown-body,
    [data-theme="dark"] {
      /*light*/
      color-scheme: light;
      --focus-outlineColor: #0969da;
      --fgColor-default: #1f2328;
      --fgColor-muted: #636c76;
      --fgColor-accent: #0969da;
      --fgColor-success: #1a7f37;
      --fgColor-attention: #9a6700;
      --fgColor-danger: #d1242f;
      --fgColor-done: #8250df;
      --bgColor-default: #ffffff;
      --bgColor-muted: #f6f8fa;
      --bgColor-neutral-muted: #afb8c133;
      --bgColor-attention-muted: #fff8c5;
      --borderColor-default: #d0d7de;
      --borderColor-muted: #d0d7deb3;
      --borderColor-neutral-muted: #afb8c133;
      --borderColor-accent-emphasis: #0969da;
      --borderColor-success-emphasis: #1a7f37;
      --borderColor-attention-emphasis: #bf8700;
      --borderColor-danger-emphasis: #cf222e;
      --borderColor-done-emphasis: #8250df;
      --color-prettylights-syntax-comment: #57606a;
      --color-prettylights-syntax-constant: #0550ae;
      --color-prettylights-syntax-constant-other-reference-link: #0a3069;
      --color-prettylights-syntax-entity: #6639ba;
      --color-prettylights-syntax-storage-modifier-import: #24292f;
      --color-prettylights-syntax-entity-tag: #0550ae;
      --color-prettylights-syntax-keyword: #cf222e;
      --color-prettylights-syntax-string: #0a3069;
      --color-prettylights-syntax-variable: #953800;
      --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
      --color-prettylights-syntax-brackethighlighter-angle: #57606a;
      --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
      --color-prettylights-syntax-invalid-illegal-bg: #82071e;
      --color-prettylights-syntax-carriage-return-text: #f6f8fa;
      --color-prettylights-syntax-carriage-return-bg: #cf222e;
      --color-prettylights-syntax-string-regexp: #116329;
      --color-prettylights-syntax-markup-list: #3b2300;
      --color-prettylights-syntax-markup-heading: #0550ae;
      --color-prettylights-syntax-markup-italic: #24292f;
      --color-prettylights-syntax-markup-bold: #24292f;
      --color-prettylights-syntax-markup-deleted-text: #82071e;
      --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
      --color-prettylights-syntax-markup-inserted-text: #116329;
      --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
      --color-prettylights-syntax-markup-changed-text: #953800;
      --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
      --color-prettylights-syntax-markup-ignored-text: #eaeef2;
      --color-prettylights-syntax-markup-ignored-bg: #0550ae;
      --color-prettylights-syntax-meta-diff-range: #8250df;
      --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
      --color-fg-default: #24292f;
      --color-fg-muted: #57606a;
      --color-fg-subtle: #6e7781;
      --color-canvas-default: #ffffff;
      --color-canvas-subtle: #f6f8fa;
      --color-border-default: #d0d7de;
      --color-border-muted: hsla(210,18%,87%,1);
      --color-neutral-muted: rgba(175,184,193,0.2);
      --color-accent-fg: #0969da;
      --color-accent-emphasis: #0969da;
      --color-attention-subtle: #fff8c5;
      --color-danger-fg: #cf222e;
    }
  }
}