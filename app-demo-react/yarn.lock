# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: ada901b9e7c680d190f1d012c84217ce0063d8f5c5a7725bb91ec3c5ed99bb7572680eb2d2938a531ccbaec39a95422fcd8a6b4a13110c7d98dd75402f66a0cd
  languageName: node
  linkType: hard

"@adobe/css-tools@npm:^4.0.1":
  version: 4.2.0
  resolution: "@adobe/css-tools@npm:4.2.0"
  checksum: dc5cc92ba3d562e7ffddb79d6d222c7e00b65f255fd2725b3d71490ff268844be322f917415d8c4ab39eca646343b632058db8bd5b1d646193fcc94d1d3e420b
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 03c04fd526acc64a1f4df22651186f3e5ef0a9d6d6530ce4482ec9841269cf7a11dbb8af79237c282d721c5312024ff17529cd72cc4768c11e999b58e2302079
  languageName: node
  linkType: hard

"@babel/code-frame@npm:7.12.11":
  version: 7.12.11
  resolution: "@babel/code-frame@npm:7.12.11"
  dependencies:
    "@babel/highlight": ^7.10.4
  checksum: 3963eff3ebfb0e091c7e6f99596ef4b258683e4ba8a134e4e95f77afe85be5c931e184fff6435fb4885d12eba04a5e25532f7fbc292ca13b48e7da943474e2f3
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.10.4, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/code-frame@npm:7.22.5"
  dependencies:
    "@babel/highlight": ^7.22.5
  checksum: cfe804f518f53faaf9a1d3e0f9f74127ab9a004912c3a16fda07fb6a633393ecb9918a053cb71804204c1b7ec3d49e1699604715e2cfb0c9f7bc4933d324ebb6
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.5, @babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/compat-data@npm:7.22.9"
  checksum: bed77d9044ce948b4327b30dd0de0779fa9f3a7ed1f2d31638714ed00229fa71fc4d1617ae0eb1fad419338d3658d0e9a5a083297451e09e73e078d0347ff808
  languageName: node
  linkType: hard

"@babel/core@npm:^7.1.0, @babel/core@npm:^7.12.3, @babel/core@npm:^7.15.0, @babel/core@npm:^7.7.2, @babel/core@npm:^7.8.0":
  version: 7.22.9
  resolution: "@babel/core@npm:7.22.9"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.22.5
    "@babel/generator": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.9
    "@babel/helper-module-transforms": ^7.22.9
    "@babel/helpers": ^7.22.6
    "@babel/parser": ^7.22.7
    "@babel/template": ^7.22.5
    "@babel/traverse": ^7.22.8
    "@babel/types": ^7.22.5
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.2
    semver: ^6.3.1
  checksum: 7bf069aeceb417902c4efdaefab1f7b94adb7dea694a9aed1bda2edf4135348a080820529b1a300c6f8605740a00ca00c19b2d5e74b5dd489d99d8c11d5e56d1
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.15.0":
  version: 7.22.9
  resolution: "@babel/eslint-parser@npm:7.22.9"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": 5.1.1-v1
    eslint-visitor-keys: ^2.1.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ">=7.11.0"
    eslint: ^7.5.0 || ^8.0.0
  checksum: 4f417796c803056aad2c8fa69b8a7a78a1fdacc307d95702f22894cab42b83554e47de7d0b3cfbee667f25014bca0179f859aa86ceb684b09803192e1200b48d
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.22.7, @babel/generator@npm:^7.22.9, @babel/generator@npm:^7.7.2":
  version: 7.22.9
  resolution: "@babel/generator@npm:7.22.9"
  dependencies:
    "@babel/types": ^7.22.5
    "@jridgewell/gen-mapping": ^0.3.2
    "@jridgewell/trace-mapping": ^0.3.17
    jsesc: ^2.5.1
  checksum: 7c9d2c58b8d5ac5e047421a6ab03ec2ff5d9a5ff2c2212130a0055e063ac349e0b19d435537d6886c999771aef394832e4f54cd9fc810100a7f23d982f6af06b
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 53da330f1835c46f26b7bf4da31f7a496dee9fd8696cca12366b94ba19d97421ce519a74a837f687749318f94d1a37f8d1abcbf35e8ed22c32d16373b2f6198d
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: d753acac62399fc6dd354cf1b9441bde0c331c2fe792a4c14904c5e5eafc3cac79478f6aa038e8a51c1148b0af6710a2e619855e4b5d54497ac972eaffed5884
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.5, @babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-compilation-targets@npm:7.22.9"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-validator-option": ^7.22.5
    browserslist: ^4.21.9
    lru-cache: ^5.1.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: ea0006c6a93759025f4a35a25228ae260538c9f15023e8aac2a6d45ca68aef4cf86cfc429b19af9a402cbdd54d5de74ad3fbcf6baa7e48184dc079f1a791e178
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.22.5":
  version: 7.22.9
  resolution: "@babel/helper-create-class-features-plugin@npm:7.22.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.9
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 6c2436d1a5a3f1ff24628d78fa8c6d3120c40285aa3eda7815b1adbf8c5951e0dd73d368cf845825888fa3dc2f207dadce53309825598d7c67953e5ed9dd51d2
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.22.5":
  version: 7.22.9
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.22.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    regexpu-core: ^5.3.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 87cb48a7ee898ab205374274364c3adc70b87b08c7bd07f51019ae4562c0170d7148e654d591f825dee14b5fe11666a0e7966872dfdbfa0d1b94b861ecf0e4e1
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.4.2":
  version: 0.4.2
  resolution: "@babel/helper-define-polyfill-provider@npm:0.4.2"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-plugin-utils": ^7.22.5
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 1f6dec0c5d0876d278fe15b71238eccc5f74c4e2efa2c78aaafa8bc2cc96336b8e68d94cd1a78497356c96e8b91b8c1f4452179820624d1702aee2f9832e6569
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-environment-visitor@npm:7.22.5"
  checksum: 248532077d732a34cd0844eb7b078ff917c3a8ec81a7f133593f71a860a582f05b60f818dc5049c2212e5baa12289c27889a4b81d56ef409b4863db49646c4b1
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-function-name@npm:7.22.5"
  dependencies:
    "@babel/template": ^7.22.5
    "@babel/types": ^7.22.5
  checksum: 6b1f6ce1b1f4e513bf2c8385a557ea0dd7fa37971b9002ad19268ca4384bbe90c09681fe4c076013f33deabc63a53b341ed91e792de741b4b35e01c00238177a
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 394ca191b4ac908a76e7c50ab52102669efe3a1c277033e49467913c7ed6f7c64d7eacbeabf3bed39ea1f41731e22993f763b1edce0f74ff8563fd1f380d92cc
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-member-expression-to-functions@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 4bd5791529c280c00743e8bdc669ef0d4cd1620d6e3d35e0d42b862f8262bc2364973e5968007f960780344c539a4b9cf92ab41f5b4f94560a9620f536de2a39
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-module-imports@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 9ac2b0404fa38b80bdf2653fbeaf8e8a43ccb41bd505f9741d820ed95d3c4e037c62a1bcdcb6c9527d7798d2e595924c4d025daed73283badc180ada2c9c49ad
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.22.5, @babel/helper-module-transforms@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-module-transforms@npm:7.22.9"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/helper-validator-identifier": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2751f77660518cf4ff027514d6f4794f04598c6393be7b04b8e46c6e21606e11c19f3f57ab6129a9c21bacdf8b3ffe3af87bb401d972f34af2d0ffde02ac3001
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: c70ef6cc6b6ed32eeeec4482127e8be5451d0e5282d5495d5d569d39eb04d7f1d66ec99b327f45d1d5842a9ad8c22d48567e93fc502003a47de78d122e355f7c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: c0fc7227076b6041acd2f0e818145d2e8c41968cc52fb5ca70eed48e21b8fe6dd88a0a91cbddf4951e33647336eb5ae184747ca706817ca3bef5e9e905151ff5
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.22.5":
  version: 7.22.9
  resolution: "@babel/helper-remap-async-to-generator@npm:7.22.9"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-wrap-function": ^7.22.9
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 05538079447829b13512157491cc77f9cf1ea7e1680e15cff0682c3ed9ee162de0c4862ece20a6d6b2df28177a1520bcfe45993fbeccf2747a81795a7c3f6290
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.22.5, @babel/helper-replace-supers@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-replace-supers@npm:7.22.9"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-member-expression-to-functions": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d41471f56ff2616459d35a5df1900d5f0756ae78b1027040365325ef332d66e08e3be02a9489756d870887585ff222403a228546e93dd7019e19e59c0c0fe586
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: fe9686714caf7d70aedb46c3cce090f8b915b206e09225f1e4dbc416786c2fdbbee40b38b23c268b7ccef749dd2db35f255338fb4f2444429874d900dede5ad2
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: 1012ef2295eb12dc073f2b9edf3425661e9b8432a3387e62a8bc27c42963f1f216ab3124228015c748770b2257b4f1fda882ca8fa34c0bf485e929ae5bc45244
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": ^7.22.5
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-string-parser@npm:7.22.5"
  checksum: 836851ca5ec813077bbb303acc992d75a360267aa3b5de7134d220411c852a6f17de7c0d0b8c8dcc0f567f67874c00f4528672b2a4f1bc978a3ada64c8c78467
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-validator-identifier@npm:7.22.5"
  checksum: 7f0f30113474a28298c12161763b49de5018732290ca4de13cdaefd4fd0d635a6fe3f6686c37a02905fb1e64f21a5ee2b55140cf7b070e729f1bd66866506aea
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-validator-option@npm:7.22.5"
  checksum: bbeca8a85ee86990215c0424997438b388b8d642d69b9f86c375a174d3cdeb270efafd1ff128bc7a1d370923d13b6e45829ba8581c027620e83e3a80c5c414b3
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.22.9":
  version: 7.22.9
  resolution: "@babel/helper-wrap-function@npm:7.22.9"
  dependencies:
    "@babel/helper-function-name": ^7.22.5
    "@babel/template": ^7.22.5
    "@babel/types": ^7.22.5
  checksum: 037317dc06dac6593e388738ae1d3e43193bc1d31698f067c0ef3d4dc6f074dbed860ed42aa137b48a67aa7cb87336826c4bdc13189260481bcf67eb7256c789
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helpers@npm:7.22.6"
  dependencies:
    "@babel/template": ^7.22.5
    "@babel/traverse": ^7.22.6
    "@babel/types": ^7.22.5
  checksum: 5c1f33241fe7bf7709868c2105134a0a86dca26a0fbd508af10a89312b1f77ca38ebae43e50be3b208613c5eacca1559618af4ca236f0abc55d294800faeff30
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4, @babel/highlight@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/highlight@npm:7.22.5"
  dependencies:
    "@babel/helper-validator-identifier": ^7.22.5
    chalk: ^2.0.0
    js-tokens: ^4.0.0
  checksum: f61ae6de6ee0ea8d9b5bcf2a532faec5ab0a1dc0f7c640e5047fc61630a0edb88b18d8c92eb06566d30da7a27db841aca11820ecd3ebe9ce514c9350fbed39c4
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.22.5, @babel/parser@npm:^7.22.7":
  version: 7.22.7
  resolution: "@babel/parser@npm:7.22.7"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 02209ddbd445831ee8bf966fdf7c29d189ed4b14343a68eb2479d940e7e3846340d7cc6bd654a5f3d87d19dc84f49f50a58cf9363bee249dc5409ff3ba3dab54
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 1e353a060fb2cd8f1256d28cd768f16fb02513f905b9b6d656fb0242c96c341a196fa188b27c2701506a6e27515359fbcc1a5ca7fa8b9b530cf88fbd137baefc
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-transform-optional-chaining": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 16e7a5f3bf2f2ac0ca032a70bf0ebd7e886d84dbb712b55c0643c04c495f0f221fbcbca14b5f8f8027fa6c87a3dafae0934022ad2b409384af6c5c356495b7bd
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-proposal-unicode-property-regex@npm:^7.4.4":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-unicode-property-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8575ecb7ff24bf6c6e94808d5c84bb5a0c6dd7892b54f09f4646711ba0ee1e1668032b3c43e3e1dfec2c5716c302e851ac756c1645e15882d73df6ad21ae951
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a10849d83e47aec50f367a9e56a6b22d662ddce643334b087f9828f4c3dd73bdc5909aaeabe123fed78515767f9ca43498a0e621c438d1cd2802d7fae3c9648
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13, @babel/plugin-syntax-class-properties@npm:^7.8.3":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b8b5572db04a7bef1e6cd20debf447e4eef7cb012616f5eceb8fa3e23ce469b8f76ee74fd6d1e158ba17a8f58b0aec579d092fb67c5a30e83ccfbc5754916c1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 197b3c5ea2a9649347f033342cb222ab47f4645633695205c0250c6bf2af29e643753b8bb24a2db39948bef08e7c540babfd365591eb57fc110cb30b425ffc47
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4, @babel/plugin-syntax-import-meta@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8829d30c2617ab31393d99cec2978e41f014f4ac6f01a1cecf4c4dd8320c3ec12fdc3ce121126b2d8d32f6887e99ca1a0bad53dedb1e6ad165640b92b24980ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4, @babel/plugin-syntax-logical-assignment-operators@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4, @babel/plugin-syntax-numeric-separator@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5, @babel/plugin-syntax-top-level-await@npm:^7.8.3":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.22.5
  resolution: "@babel/plugin-syntax-typescript@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8ab7718fbb026d64da93681a57797d60326097fd7cb930380c8bffd9eb101689e90142c760a14b51e8e69c88a73ba3da956cb4520a3b0c65743aee5c71ef360a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35abb6c57062802c7ce8bd96b2ef2883e3124370c688bbd67609f7d2453802fb73944df8808f893b6c67de978eb2bcf87bbfe325e46d6f39b5fcb09ece11d01a
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.22.7":
  version: 7.22.7
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.22.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.5
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 57cd2cce3fb696dadf00e88f168683df69e900b92dadeae07429243c43bc21d5ccdc0c2db61cf5c37bd0fbd893fc455466bef6babe4aa5b79d9cb8ba89f40ae7
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.22.5"
  dependencies:
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-remap-async-to-generator": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b95f23f99dcb379a9f0a1c2a3bbea3f8dc0e1b16dc1ac8b484fe378370169290a7a63d520959a9ba1232837cf74a80e23f6facbe14fd42a3cda6d3c2d7168e62
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 416b1341858e8ca4e524dee66044735956ced5f478b2c3b9bc11ec2285b0c25d7dbb96d79887169eb938084c95d0a89338c8b2fe70d473bd9dc92e5d9db1732c
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-block-scoping@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 26987002cfe6e24544e60fa35f07052b6557f590c1a1cc5cf35d6dc341d7fea163c1222a2d70d5d2692f0b9860d942fd3ba979848b2995d4debffa387b9b19ae
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-class-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b830152dfc2ff2f647f0abe76e6251babdfbef54d18c4b2c73a6bf76b1a00050a5d998dac80dc901a48514e95604324943a9dd39317073fe0928b559e0e0c579
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-class-static-block@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: bc48b92dbaf625a14f2bf62382384eef01e0515802426841636ae9146e27395d068c7a8a45e9e15699491b0a01d990f38f179cbc9dc89274a393f85648772f12
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/plugin-transform-classes@npm:7.22.6"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-optimise-call-expression": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8380e855c01033dbc7460d9acfbc1fc37c880350fa798c2de8c594ef818ade0e4c96173ec72f05f2a4549d8d37135e18cb62548352d51557b45a0fb4388d2f3f
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-computed-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/template": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c2a77a0f94ec71efbc569109ec14ea2aa925b333289272ced8b33c6108bdbb02caf01830ffc7e49486b62dec51911924d13f3a76f1149f40daace1898009e131
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-destructuring@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 76f6ea2aee1fcfa1c3791eb7a5b89703c6472650b993e8666fff0f1d6e9d737a84134edf89f63c92297f3e75064c1263219463b02dd9bc7434b6e5b9935e3f20
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.22.5, @babel/plugin-transform-dotall-regex@npm:^7.4.4":
  version: 7.22.5
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 409b658d11e3082c8f69e9cdef2d96e4d6d11256f005772425fb230cc48fd05945edbfbcb709dab293a1a2f01f9c8a5bb7b4131e632b23264039d9f95864b453
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bb1280fbabaab6fab2ede585df34900712698210a3bd413f4df5bae6d8c24be36b496c92722ae676a7a67d060a4624f4d6c23b923485f906bfba8773c69f55b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 186a6d59f36eb3c5824739fc9c22ed0f4ca68e001662aa3a302634346a8b785cb9579b23b0c158f4570604d697d19598ca09b58c60a7fa2894da1163c4eb1907
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.22.5"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f2d660c1b1d51ad5fec1cd5ad426a52187204068c4158f8c4aa977b31535c61b66898d532603eef21c15756827be8277f724c869b888d560f26d7fe848bb5eae
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3d197b788758044983c96b9c49bed4b456055f35a388521a405968db0f6e2ffb6fd59110e3931f4dcc5e126ae9e5e00e154a0afb47a7ea359d8d0dea79f480d7
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-for-of@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d7b8d4db010bce7273674caa95c4e6abd909362866ce297e86a2ecaa9ae636e05d525415811db9b3c942155df7f3651d19b91dd6c41f142f7308a97c7cb06023
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-function-name@npm:7.22.5"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cff3b876357999cb8ae30e439c3ec6b0491a53b0aa6f722920a4675a6dd5b53af97a833051df4b34791fe5b3dd326ccf769d5c8e45b322aa50ee11a660b17845
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-json-strings@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4e00b902487a670b6c8948f33f9108133fd745cf9d1478aca515fb460b9b2f12e137988ebc1663630fb82070a870aed8b0c1aa4d007a841c18004619798f255c
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec37cc2ffb32667af935ab32fe28f00920ec8a1eb999aa6dc6602f2bebd8ba205a558aeedcdccdebf334381d5c57106c61f52332045730393e73410892a9735b
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 18748e953c08f64885f18c224eac58df10a13eac4d845d16b5d9b6276907da7ca2530dfebe6ed41cdc5f8a75d9db3e36d8eb54ddce7cd0364af1cab09b435302
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ec4b0e07915ddd4fda0142fd104ee61015c208608a84cfa13643a95d18760b1dc1ceb6c6e0548898b8c49e5959a994e46367260176dbabc4467f729b21868504
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-amd@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7da4c4ebbbcf7d182abb59b2046b22d86eee340caf8a22a39ef6a727da2d8acfec1f714fcdcd5054110b280e4934f735e80a6848d192b6834c5d4459a014f04d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-simple-access": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2067aca8f6454d54ffcce69b02c457cfa61428e11372f6a1d99ff4fcfbb55c396ed2ca6ca886bf06c852e38c1a205b8095921b2364fd0243f3e66bc1dda61caa
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.22.5"
  dependencies:
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 04f4178589543396b3c24330a67a59c5e69af5e96119c9adda730c0f20122deaff54671ebbc72ad2df6495a5db8a758bd96942de95fba7ad427de9c80b1b38c8
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-modules-umd@npm:7.22.5"
  dependencies:
    "@babel/helper-module-transforms": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46622834c54c551b231963b867adbc80854881b3e516ff29984a8da989bd81665bd70e8cba6710345248e97166689310f544aee1a5773e262845a8f1b3e5b8b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3ee564ddee620c035b928fdc942c5d17e9c4b98329b76f9cefac65c111135d925eb94ed324064cd7556d4f5123beec79abea1d4b97d1c8a2a5c748887a2eb623
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-new-target@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b72112773487a881a1d6ffa680afde08bad699252020e86122180ee7a88854d5da3f15d9bca3331cf2e025df045604494a8208a2e63b486266b07c14e2ffbf3
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e6a059169d257fc61322d0708edae423072449b7c33de396261e68dee582aec5396789a1c22bce84e5bd88a169623c2e750b513fc222930979e6accd52a44bf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9e7837d4eae04f211ebaa034fe5003d2927b6bf6d5b9dc09f2b1183c01482cdde5a75b8bd5c7ff195c2abc7b923339eb0b2a9d27cb78359d38248a3b2c2367c4
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.22.5"
  dependencies:
    "@babel/compat-data": ^7.22.5
    "@babel/helper-compilation-targets": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3b5e091f0dc67108f2e41ed5a97e15bbe4381a19d9a7eea80b71c7de1d8169fd28784e1e41a3d2ad12709ab212e58fc481282a5bb65d591fae7b443048de3330
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-object-super@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-replace-supers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b71887877d74cb64dbccb5c0324fa67e31171e6a5311991f626650e44a4083e5436a1eaa89da78c0474fb095d4ec322d63ee778b202d33aa2e4194e1ed8e62d7
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b0e8b4233ff06b5c9d285257f49c5bd441f883189b24282e6200f9ebdf5db29aeeebbffae57fbbcd5df9f4387b3e66e5d322aaae5652a78e89685ddbae46bbd1
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.22.5, @babel/plugin-transform-optional-chaining@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.22.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9713f7920ed04090c149fc5ec024dd1638e8b97aa4ae3753b93072d84103b8de380afb96d6cf03e53b285420db4f705f3ac13149c6fd54f322b61dc19e33c54f
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-parameters@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b44f89cf97daf23903776ba27c2ab13b439d80d8c8a95be5c476ab65023b1e0c0e94c28d3745f3b60a58edc4e590fa0cd4287a0293e51401ca7d29a2ddb13b8e
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-private-methods@npm:7.22.5"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 321479b4fcb6d3b3ef622ab22fd24001e43d46e680e8e41324c033d5810c84646e470f81b44cbcbef5c22e99030784f7cac92f1829974da7a47a60a7139082c3
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-create-class-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ac019fb2772f3af6278a7f4b8b14b0663accb3fd123d87142ceb2fbc57fd1afa07c945d1329029b026b9ee122096ef71a3f34f257a9e04cf4245b87298c38b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-property-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 796176a3176106f77fcb8cd04eb34a8475ce82d6d03a88db089531b8f0453a2fb8b0c6ec9a52c27948bc0ea478becec449893741fc546dfc3930ab927e3f9f2e
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-display-name@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a12bfd1e4e93055efca3ace3c34722571bda59d9740dca364d225d9c6e3ca874f134694d21715c42cc63d79efd46db9665bd4a022998767f9245f1e29d5d204d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.22.5"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 36bc3ff0b96bb0ef4723070a50cfdf2e72cfd903a59eba448f9fe92fea47574d6f22efd99364413719e1f3fb3c51b6c9b2990b87af088f8486a84b2a5f9e4560
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/plugin-syntax-jsx": ^7.22.5
    "@babel/types": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c8f93f29f32cf79683ca2b8958fd62f38155674846ef27a7d4b6fbeb8713c37257418391731b58ff8024ec37b888bed5960e615a3f552e28245d2082e7f2a2df
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.22.5"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 092021c4f404e267002099ec20b3f12dd730cb90b0d83c5feed3dc00dbe43b9c42c795a18e7c6c7d7bddea20c7dd56221b146aec81b37f2e7eb5137331c61120
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    regenerator-transform: ^0.15.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f7c5ca5151321963df777cc02725d10d1ccc3b3b8323da0423aecd9ac6144cbdd2274af5281a5580db2fc2f8b234e318517b5d76b85669118906533a559f2b6a
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-reserved-words@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3ffd7dbc425fe8132bfec118b9817572799cab1473113a635d25ab606c1f5a2341a636c04cf6b22df3813320365ed5a965b5eeb3192320a10e4cc2c137bd8bfc
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.15.0":
  version: 7.22.9
  resolution: "@babel/plugin-transform-runtime@npm:7.22.9"
  dependencies:
    "@babel/helper-module-imports": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
    babel-plugin-polyfill-corejs2: ^0.4.4
    babel-plugin-polyfill-corejs3: ^0.8.2
    babel-plugin-polyfill-regenerator: ^0.5.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2fe5e41f83015ca174feda841d77aa9012fc855c907f9b360a11927f41b100537c8c83487771769147668e797eec26d5294e972b997f4759133cc43a22a43eec
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a5ac902c56ea8effa99f681340ee61bac21094588f7aef0bc01dff98246651702e677552fa6d10e548c4ac22a3ffad047dd2f8c8f0540b68316c2c203e56818b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-spread@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-skip-transparent-expression-wrappers": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5587f0deb60b3dfc9b274e269031cc45ec75facccf1933ea2ea71ced9fd3ce98ed91bb36d6cd26817c14474b90ed998c5078415f0eab531caf301496ce24c95c
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 63b2c575e3e7f96c32d52ed45ee098fb7d354b35c2223b8c8e76840b32cc529ee0c0ceb5742fd082e56e91e3d82842a367ce177e82b05039af3d602c9627a729
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-template-literals@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 27e9bb030654cb425381c69754be4abe6a7c75b45cd7f962cd8d604b841b2f0fb7b024f2efc1c25cc53f5b16d79d5e8cfc47cacbdaa983895b3aeefa3e7e24ff
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 82a53a63ffc3010b689ca9a54e5f53b2718b9f4b4a9818f36f9b7dba234f38a01876680553d2716a645a61920b5e6e4aaf8d4a0064add379b27ca0b403049512
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: da5e85ab3bb33a75cbf6181bfd236b208dc934702fd304db127232f17b4e0f42c6d3f238de8589470b4190906967eea8ca27adf3ae9d8ee4de2a2eae906ed186
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2495e5f663cb388e3d888b4ba3df419ac436a5012144ac170b622ddfc221f9ea9bdba839fa2bc0185cb776b578030666406452ec7791cbf0e7a3d4c88ae9574c
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6b5d1404c8c623b0ec9bd436c00d885a17d6a34f3f2597996343ddb9d94f6379705b21582dfd4cec2c47fd34068872e74ab6b9580116c0566b3f9447e2a7fa06
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.22.5
    "@babel/helper-plugin-utils": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c042070f980b139547f8b0179efbc049ac5930abec7fc26ed7a41d89a048d8ab17d362200e204b6f71c3c20d6991a0e74415e1a412a49adc8131c2a40c04822e
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.15.0":
  version: 7.22.9
  resolution: "@babel/preset-env@npm:7.22.9"
  dependencies:
    "@babel/compat-data": ^7.22.9
    "@babel/helper-compilation-targets": ^7.22.9
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.5
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.22.5
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.22.5
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-import-assertions": ^7.22.5
    "@babel/plugin-syntax-import-attributes": ^7.22.5
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.22.5
    "@babel/plugin-transform-async-generator-functions": ^7.22.7
    "@babel/plugin-transform-async-to-generator": ^7.22.5
    "@babel/plugin-transform-block-scoped-functions": ^7.22.5
    "@babel/plugin-transform-block-scoping": ^7.22.5
    "@babel/plugin-transform-class-properties": ^7.22.5
    "@babel/plugin-transform-class-static-block": ^7.22.5
    "@babel/plugin-transform-classes": ^7.22.6
    "@babel/plugin-transform-computed-properties": ^7.22.5
    "@babel/plugin-transform-destructuring": ^7.22.5
    "@babel/plugin-transform-dotall-regex": ^7.22.5
    "@babel/plugin-transform-duplicate-keys": ^7.22.5
    "@babel/plugin-transform-dynamic-import": ^7.22.5
    "@babel/plugin-transform-exponentiation-operator": ^7.22.5
    "@babel/plugin-transform-export-namespace-from": ^7.22.5
    "@babel/plugin-transform-for-of": ^7.22.5
    "@babel/plugin-transform-function-name": ^7.22.5
    "@babel/plugin-transform-json-strings": ^7.22.5
    "@babel/plugin-transform-literals": ^7.22.5
    "@babel/plugin-transform-logical-assignment-operators": ^7.22.5
    "@babel/plugin-transform-member-expression-literals": ^7.22.5
    "@babel/plugin-transform-modules-amd": ^7.22.5
    "@babel/plugin-transform-modules-commonjs": ^7.22.5
    "@babel/plugin-transform-modules-systemjs": ^7.22.5
    "@babel/plugin-transform-modules-umd": ^7.22.5
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.22.5
    "@babel/plugin-transform-new-target": ^7.22.5
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.22.5
    "@babel/plugin-transform-numeric-separator": ^7.22.5
    "@babel/plugin-transform-object-rest-spread": ^7.22.5
    "@babel/plugin-transform-object-super": ^7.22.5
    "@babel/plugin-transform-optional-catch-binding": ^7.22.5
    "@babel/plugin-transform-optional-chaining": ^7.22.6
    "@babel/plugin-transform-parameters": ^7.22.5
    "@babel/plugin-transform-private-methods": ^7.22.5
    "@babel/plugin-transform-private-property-in-object": ^7.22.5
    "@babel/plugin-transform-property-literals": ^7.22.5
    "@babel/plugin-transform-regenerator": ^7.22.5
    "@babel/plugin-transform-reserved-words": ^7.22.5
    "@babel/plugin-transform-shorthand-properties": ^7.22.5
    "@babel/plugin-transform-spread": ^7.22.5
    "@babel/plugin-transform-sticky-regex": ^7.22.5
    "@babel/plugin-transform-template-literals": ^7.22.5
    "@babel/plugin-transform-typeof-symbol": ^7.22.5
    "@babel/plugin-transform-unicode-escapes": ^7.22.5
    "@babel/plugin-transform-unicode-property-regex": ^7.22.5
    "@babel/plugin-transform-unicode-regex": ^7.22.5
    "@babel/plugin-transform-unicode-sets-regex": ^7.22.5
    "@babel/preset-modules": ^0.1.5
    "@babel/types": ^7.22.5
    babel-plugin-polyfill-corejs2: ^0.4.4
    babel-plugin-polyfill-corejs3: ^0.8.2
    babel-plugin-polyfill-regenerator: ^0.5.1
    core-js-compat: ^3.31.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6caa2897bbda30c6932aed0a03827deb1337c57108050c9f97dc9a857e1533c7125b168b6d70b9d191965bf05f9f233f0ad20303080505dff7ce39740aaa759d
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:^0.1.5":
  version: 0.1.6
  resolution: "@babel/preset-modules@npm:0.1.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/plugin-proposal-unicode-property-regex": ^7.4.4
    "@babel/plugin-transform-dotall-regex": ^7.4.4
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 9700992d2b9526e703ab49eb8c4cd0b26bec93594d57c6b808967619df1a387565e0e58829b65b5bd6d41049071ea0152c9195b39599515fddb3e52b09a55ff0
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.14.5":
  version: 7.22.5
  resolution: "@babel/preset-react@npm:7.22.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.22.5
    "@babel/helper-validator-option": ^7.22.5
    "@babel/plugin-transform-react-display-name": ^7.22.5
    "@babel/plugin-transform-react-jsx": ^7.22.5
    "@babel/plugin-transform-react-jsx-development": ^7.22.5
    "@babel/plugin-transform-react-pure-annotations": ^7.22.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b977c7ee83e93f62d77e61929ca3d97e5291e026e2f025a1b8b7ac9186486ed56c7d5bc36f0becabe0c24e8c42a4e4f2243a3cf841384cfafc3204c5d3e6c619
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: 89c338fee774770e5a487382170711014d49a68eb281e74f2b5eac88f38300a4ad545516a7786a8dd5702e9cf009c94c2f582d200f077ac5decd74c56b973730
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.1.2, @babel/runtime@npm:^7.12.13, @babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.15.3, @babel/runtime@npm:^7.20.7, @babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.8.4, @babel/runtime@npm:^7.9.2":
  version: 7.22.6
  resolution: "@babel/runtime@npm:7.22.6"
  dependencies:
    regenerator-runtime: ^0.13.11
  checksum: e585338287c4514a713babf4fdb8fc2a67adcebab3e7723a739fc62c79cfda875b314c90fd25f827afb150d781af97bc16c85bfdbfa2889f06053879a1ddb597
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.5, @babel/template@npm:^7.3.3":
  version: 7.22.5
  resolution: "@babel/template@npm:7.22.5"
  dependencies:
    "@babel/code-frame": ^7.22.5
    "@babel/parser": ^7.22.5
    "@babel/types": ^7.22.5
  checksum: c5746410164039aca61829cdb42e9a55410f43cace6f51ca443313f3d0bdfa9a5a330d0b0df73dc17ef885c72104234ae05efede37c1cc8a72dc9f93425977a3
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.22.6, @babel/traverse@npm:^7.22.8, @babel/traverse@npm:^7.7.2":
  version: 7.22.8
  resolution: "@babel/traverse@npm:7.22.8"
  dependencies:
    "@babel/code-frame": ^7.22.5
    "@babel/generator": ^7.22.7
    "@babel/helper-environment-visitor": ^7.22.5
    "@babel/helper-function-name": ^7.22.5
    "@babel/helper-hoist-variables": ^7.22.5
    "@babel/helper-split-export-declaration": ^7.22.6
    "@babel/parser": ^7.22.7
    "@babel/types": ^7.22.5
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: a381369bc3eedfd13ed5fef7b884657f1c29024ea7388198149f0edc34bd69ce3966e9f40188d15f56490a5e12ba250ccc485f2882b53d41b054fccefb233e33
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.22.5, @babel/types@npm:^7.3.3, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.22.5
  resolution: "@babel/types@npm:7.22.5"
  dependencies:
    "@babel/helper-string-parser": ^7.22.5
    "@babel/helper-validator-identifier": ^7.22.5
    to-fast-properties: ^2.0.0
  checksum: c13a9c1dc7d2d1a241a2f8363540cb9af1d66e978e8984b400a20c4f38ba38ca29f06e26a0f2d49a70bad9e57615dac09c35accfddf1bb90d23cd3e0a0bab892
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 850f9305536d0f2bd13e9e0881cb5f02e4f93fad1189f7b2d4bebf694e3206924eadee1068130d43c11b750efcc9405f88a8e42ef098b6d75239c0f047de1a27
  languageName: node
  linkType: hard

"@dealer/app-demo-react@workspace:.":
  version: 0.0.0-use.local
  resolution: "@dealer/app-demo-react@workspace:."
  dependencies:
    "@babel/core": ^7.15.0
    "@babel/eslint-parser": ^7.15.0
    "@babel/plugin-transform-runtime": ^7.15.0
    "@babel/preset-env": ^7.15.0
    "@babel/preset-react": ^7.14.5
    "@babel/runtime": ^7.15.3
    "@testing-library/jest-dom": ^5.14.1
    "@testing-library/react": ^12.0.0
    babel-jest: ^27.0.6
    concurrently: ^6.2.1
    cross-env: ^7.0.3
    eslint: ^7.32.0
    eslint-config-prettier: ^8.3.0
    eslint-config-react-important-stuff: ^3.0.0
    eslint-plugin-prettier: ^3.4.1
    husky: ^7.0.2
    identity-obj-proxy: ^3.0.0
    jest: ^27.0.6
    jest-cli: ^27.0.6
    prettier: ^2.3.2
    pretty-quick: ^3.1.1
    react: ^17.0.2
    react-dom: ^17.0.2
    react-router: ^5.2.0
    react-router-dom: ^5.2.0
    single-spa-react: ^4.3.1
    webpack: ^5.75.0
    webpack-cli: ^4.10.0
    webpack-config-single-spa-react: ^4.0.0
    webpack-dev-server: ^4.0.0
    webpack-merge: ^5.8.0
  languageName: unknown
  linkType: soft

"@discoveryjs/json-ext@npm:0.5.7, @discoveryjs/json-ext@npm:^0.5.0":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^0.4.3":
  version: 0.4.3
  resolution: "@eslint/eslintrc@npm:0.4.3"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.1.1
    espree: ^7.3.0
    globals: ^13.9.0
    ignore: ^4.0.6
    import-fresh: ^3.2.1
    js-yaml: ^3.13.1
    minimatch: ^3.0.4
    strip-json-comments: ^3.1.1
  checksum: 03a7704150b868c318aab6a94d87a33d30dc2ec579d27374575014f06237ba1370ae11178db772f985ef680d469dc237e7b16a1c5d8edaaeb8c3733e7a95a6d3
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.5.0":
  version: 0.5.0
  resolution: "@humanwhocodes/config-array@npm:0.5.0"
  dependencies:
    "@humanwhocodes/object-schema": ^1.2.0
    debug: ^4.1.1
    minimatch: ^3.0.4
  checksum: 44ee6a9f05d93dd9d5935a006b17572328ba9caff8002442f601736cbda79c580cc0f5a49ce9eb88fbacc5c3a6b62098357c2e95326cd17bb9f1a6c61d6e95e7
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.0":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: a824a1ec31591231e4bad5787641f59e9633827d0a2eaae131a288d33c9ef0290bd16fda8da6f7c0fcb014147865d12118df10db57f27f41e20da92369fcb3f1
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: ^5.3.1
    find-up: ^4.1.0
    get-package-type: ^0.1.0
    js-yaml: ^3.13.1
    resolve-from: ^5.0.0
  checksum: d578da5e2e804d5c93228450a1380e1a3c691de4953acc162f387b717258512a3e07b83510a936d9fab03eac90817473917e24f5d16297af3867f59328d58568
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 5282759d961d61350f33d9118d16bcaed914ebf8061a52f4fa474b2cb08720c9c81d165e13b82f2e5a8a212cc5af482f0c6fc1ac27b9e067e5394c9a6ed186c9
  languageName: node
  linkType: hard

"@jest/console@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/console@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    jest-message-util: ^27.5.1
    jest-util: ^27.5.1
    slash: ^3.0.0
  checksum: 7cb20f06a34b09734c0342685ec53aa4c401fe3757c13a9c58fce76b971a322eb884f6de1068ef96f746e5398e067371b89515a07c268d4440a867c87748a706
  languageName: node
  linkType: hard

"@jest/core@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/core@npm:27.5.1"
  dependencies:
    "@jest/console": ^27.5.1
    "@jest/reporters": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    emittery: ^0.8.1
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-changed-files: ^27.5.1
    jest-config: ^27.5.1
    jest-haste-map: ^27.5.1
    jest-message-util: ^27.5.1
    jest-regex-util: ^27.5.1
    jest-resolve: ^27.5.1
    jest-resolve-dependencies: ^27.5.1
    jest-runner: ^27.5.1
    jest-runtime: ^27.5.1
    jest-snapshot: ^27.5.1
    jest-util: ^27.5.1
    jest-validate: ^27.5.1
    jest-watcher: ^27.5.1
    micromatch: ^4.0.4
    rimraf: ^3.0.0
    slash: ^3.0.0
    strip-ansi: ^6.0.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 904a94ad8f1b43cd6b48de3b0226659bff3696150ff8cf7680fc2faffdc8a115203bb9ab6e817c1f79f9d6a81f67953053cbc64d8a4604f2e0c42a04c28cf126
  languageName: node
  linkType: hard

"@jest/environment@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/environment@npm:27.5.1"
  dependencies:
    "@jest/fake-timers": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    jest-mock: ^27.5.1
  checksum: 2a9e18c35a015508dbec5b90b21c150230fa6c1c8cb8fabe029d46ee2ca4c40eb832fb636157da14c66590d0a4c8a2c053226b041f54a44507d6f6a89abefd66
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.6.1":
  version: 29.6.1
  resolution: "@jest/expect-utils@npm:29.6.1"
  dependencies:
    jest-get-type: ^29.4.3
  checksum: 037ee017eca62f7b45e1465fb5c6f9e92d5709a9ac716b8bff0bd294240a54de734e8f968fb69309cc4aef6c83b9552d5a821f3b18371af394bf04783859d706
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/fake-timers@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    "@sinonjs/fake-timers": ^8.0.1
    "@types/node": "*"
    jest-message-util: ^27.5.1
    jest-mock: ^27.5.1
    jest-util: ^27.5.1
  checksum: 02a0561ed2f4586093facd4ae500b74694f187ac24d4a00e949a39a1c5325bca8932b4fcb0388a2c5ed0656506fc1cf51fd3e32cdd48cea7497ad9c6e028aba8
  languageName: node
  linkType: hard

"@jest/globals@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/globals@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/types": ^27.5.1
    expect: ^27.5.1
  checksum: 087f97047e9dcf555f76fe2ce54aee681e005eaa837a0c0c2d251df6b6412c892c9df54cb871b180342114389a5ff895a4e52e6e6d3d0015bf83c02a54f64c3c
  languageName: node
  linkType: hard

"@jest/reporters@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/reporters@npm:27.5.1"
  dependencies:
    "@bcoe/v8-coverage": ^0.2.3
    "@jest/console": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    collect-v8-coverage: ^1.0.0
    exit: ^0.1.2
    glob: ^7.1.2
    graceful-fs: ^4.2.9
    istanbul-lib-coverage: ^3.0.0
    istanbul-lib-instrument: ^5.1.0
    istanbul-lib-report: ^3.0.0
    istanbul-lib-source-maps: ^4.0.0
    istanbul-reports: ^3.1.3
    jest-haste-map: ^27.5.1
    jest-resolve: ^27.5.1
    jest-util: ^27.5.1
    jest-worker: ^27.5.1
    slash: ^3.0.0
    source-map: ^0.6.0
    string-length: ^4.0.1
    terminal-link: ^2.0.0
    v8-to-istanbul: ^8.1.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: faba5eafb86e62b62e152cafc8812d56308f9d1e8b77f3a7dcae4a8803a20a60a0909cc43ed73363ef649bf558e4fb181c7a336d144c89f7998279d1882bb69e
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.0":
  version: 29.6.0
  resolution: "@jest/schemas@npm:29.6.0"
  dependencies:
    "@sinclair/typebox": ^0.27.8
  checksum: c00511c69cf89138a7d974404d3a5060af375b5a52b9c87215d91873129b382ca11c1ff25bd6d605951404bb381ddce5f8091004a61e76457da35db1f5c51365
  languageName: node
  linkType: hard

"@jest/source-map@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/source-map@npm:27.5.1"
  dependencies:
    callsites: ^3.0.0
    graceful-fs: ^4.2.9
    source-map: ^0.6.0
  checksum: 4fb1e743b602841babf7e22bd84eca34676cb05d4eb3b604cae57fc59e406099f5ac759ac1a0d04d901237d143f0f4f234417306e823bde732a1d19982230862
  languageName: node
  linkType: hard

"@jest/test-result@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/test-result@npm:27.5.1"
  dependencies:
    "@jest/console": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/istanbul-lib-coverage": ^2.0.0
    collect-v8-coverage: ^1.0.0
  checksum: 338f7c509d6a3bc6d7dd7388c8f6f548b87638e171dc1fddfedcacb4e8950583288832223ba688058cbcf874b937d22bdc0fa88f79f5fc666f77957e465c06a5
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/test-sequencer@npm:27.5.1"
  dependencies:
    "@jest/test-result": ^27.5.1
    graceful-fs: ^4.2.9
    jest-haste-map: ^27.5.1
    jest-runtime: ^27.5.1
  checksum: f21f9c8bb746847f7f89accfd29d6046eec1446f0b54e4694444feaa4df379791f76ef0f5a4360aafcbc73b50bc979f68b8a7620de404019d3de166be6720cb0
  languageName: node
  linkType: hard

"@jest/transform@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/transform@npm:27.5.1"
  dependencies:
    "@babel/core": ^7.1.0
    "@jest/types": ^27.5.1
    babel-plugin-istanbul: ^6.1.1
    chalk: ^4.0.0
    convert-source-map: ^1.4.0
    fast-json-stable-stringify: ^2.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^27.5.1
    jest-regex-util: ^27.5.1
    jest-util: ^27.5.1
    micromatch: ^4.0.4
    pirates: ^4.0.4
    slash: ^3.0.0
    source-map: ^0.6.1
    write-file-atomic: ^3.0.0
  checksum: a22079121aedea0f20a03a9c026be971f7b92adbfb4d5fd1fb67be315741deac4f056936d7c72a53b24aa5a1071bc942c003925fd453bf3f6a0ae5da6384e137
  languageName: node
  linkType: hard

"@jest/types@npm:^27.5.1":
  version: 27.5.1
  resolution: "@jest/types@npm:27.5.1"
  dependencies:
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^16.0.0
    chalk: ^4.0.0
  checksum: d1f43cc946d87543ddd79d49547aab2399481d34025d5c5f2025d3d99c573e1d9832fa83cef25e9d9b07a8583500229d15bbb07b8e233d127d911d133e2f14b1
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.1":
  version: 29.6.1
  resolution: "@jest/types@npm:29.6.1"
  dependencies:
    "@jest/schemas": ^29.6.0
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^17.0.8
    chalk: ^4.0.0
  checksum: 89fc1ccf71a84fe0da643e0675b1cfe6a6f19ea72e935b2ab1dbdb56ec547e94433fb59b3536d3832a6e156c077865b7176fe9dae707dab9c3d2f9405ba6233c
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.2":
  version: 0.3.3
  resolution: "@jridgewell/gen-mapping@npm:0.3.3"
  dependencies:
    "@jridgewell/set-array": ^1.0.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 4a74944bd31f22354fc01c3da32e83c19e519e3bbadafa114f6da4522ea77dd0c2842607e923a591d60a76699d819a2fbb6f3552e277efdb9b58b081390b60ab
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:3.1.0":
  version: 3.1.0
  resolution: "@jridgewell/resolve-uri@npm:3.1.0"
  checksum: b5ceaaf9a110fcb2780d1d8f8d4a0bfd216702f31c988d8042e5f8fbe353c55d9b0f55a1733afdc64806f8e79c485d2464680ac48a0d9fcadb9548ee6b81d267
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.5
  resolution: "@jridgewell/source-map@npm:0.3.5"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 1ad4dec0bdafbade57920a50acec6634f88a0eb735851e0dda906fa9894e7f0549c492678aad1a10f8e144bfe87f238307bf2a914a1bc85b7781d345417e9f6f
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:1.4.14":
  version: 1.4.14
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.14"
  checksum: 61100637b6d173d3ba786a5dff019e1a74b1f394f323c1fee337ff390239f053b87266c7a948777f4b1ee68c01a8ad0ab61e5ff4abb5a012a0b091bec391ab97
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: b881c7e503db3fc7f3c1f35a1dd2655a188cc51a3612d76efc8a6eb74728bef5606e6758ee77423e564092b4a518aba569bbb21c9bac5ab7a35b0c6ae7e344c8
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.17, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.18
  resolution: "@jridgewell/trace-mapping@npm:0.3.18"
  dependencies:
    "@jridgewell/resolve-uri": 3.1.0
    "@jridgewell/sourcemap-codec": 1.4.14
  checksum: 0572669f855260808c16fe8f78f5f1b4356463b11d3f2c7c0b5580c8ba1cbf4ae53efe9f627595830856e57dbac2325ac17eb0c3dd0ec42102e6f227cc289c02
  languageName: node
  linkType: hard

"@leichtgewicht/ip-codec@npm:^2.0.1":
  version: 2.0.4
  resolution: "@leichtgewicht/ip-codec@npm:2.0.4"
  checksum: 468de1f04d33de6d300892683d7c8aecbf96d1e2c5fe084f95f816e50a054d45b7c1ebfb141a1447d844b86a948733f6eebd92234da8581c84a1ad4de2946a2d
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: 5.1.1
  checksum: f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: ^7.3.5
  checksum: a50a6818de5fc557d0b0e6f50ec780a7a02ab8ad07e5ac8b16bf519e0ad60a144ac64f97d05c443c3367235d337182e1d012bbac0eb8dbae8dc7b40b193efd0e
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.20":
  version: 1.0.0-next.21
  resolution: "@polka/url@npm:1.0.0-next.21"
  checksum: c7654046d38984257dd639eab3dc770d1b0340916097b2fac03ce5d23506ada684e05574a69b255c32ea6a144a957c8cd84264159b545fca031c772289d88788
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 00bd7362a3439021aa1ea51b0e0d0a0e8ca1351a3d54c606b115fdcc49b51b16db6e5f43b4fe7a28c38688523e22a94d49dd31168868b655f0d4d50f032d07a1
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^1.7.0":
  version: 1.8.6
  resolution: "@sinonjs/commons@npm:1.8.6"
  dependencies:
    type-detect: 4.0.8
  checksum: 7d3f8c1e85f30cd4e83594fc19b7a657f14d49eb8d95a30095631ce15e906c869e0eff96c5b93dffea7490c00418b07f54582ba49c6560feb2a8c34c0b16832d
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^8.0.1":
  version: 8.1.0
  resolution: "@sinonjs/fake-timers@npm:8.1.0"
  dependencies:
    "@sinonjs/commons": ^1.7.0
  checksum: 09b5a158ce013a6c37613258bad79ca4efeb99b1f59c41c73cca36cac00b258aefcf46eeea970fccf06b989414d86fe9f54c1102272c0c3bdd51a313cea80949
  languageName: node
  linkType: hard

"@testing-library/dom@npm:^8.0.0":
  version: 8.20.1
  resolution: "@testing-library/dom@npm:8.20.1"
  dependencies:
    "@babel/code-frame": ^7.10.4
    "@babel/runtime": ^7.12.5
    "@types/aria-query": ^5.0.1
    aria-query: 5.1.3
    chalk: ^4.1.0
    dom-accessibility-api: ^0.5.9
    lz-string: ^1.5.0
    pretty-format: ^27.0.2
  checksum: 06fc8dc67849aadb726cbbad0e7546afdf8923bd39acb64c576d706249bd7d0d05f08e08a31913fb621162e3b9c2bd0dce15964437f030f9fa4476326fdd3007
  languageName: node
  linkType: hard

"@testing-library/jest-dom@npm:^5.14.1":
  version: 5.17.0
  resolution: "@testing-library/jest-dom@npm:5.17.0"
  dependencies:
    "@adobe/css-tools": ^4.0.1
    "@babel/runtime": ^7.9.2
    "@types/testing-library__jest-dom": ^5.9.1
    aria-query: ^5.0.0
    chalk: ^3.0.0
    css.escape: ^1.5.1
    dom-accessibility-api: ^0.5.6
    lodash: ^4.17.15
    redent: ^3.0.0
  checksum: 9f28dbca8b50d7c306aae40c3aa8e06f0e115f740360004bd87d57f95acf7ab4b4f4122a7399a76dbf2bdaaafb15c99cc137fdcb0ae457a92e2de0f3fbf9b03b
  languageName: node
  linkType: hard

"@testing-library/react@npm:^12.0.0":
  version: 12.1.5
  resolution: "@testing-library/react@npm:12.1.5"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@testing-library/dom": ^8.0.0
    "@types/react-dom": <18.0.0
  peerDependencies:
    react: <18.0.0
    react-dom: <18.0.0
  checksum: 4abd0490405e709a7df584a0db604e508a4612398bb1326e8fa32dd9393b15badc826dcf6d2f7525437886d507871f719f127b9860ed69ddd204d1fa834f576a
  languageName: node
  linkType: hard

"@tootallnate/once@npm:1":
  version: 1.1.2
  resolution: "@tootallnate/once@npm:1.1.2"
  checksum: e1fb1bbbc12089a0cb9433dc290f97bddd062deadb6178ce9bcb93bb7c1aecde5e60184bc7065aec42fe1663622a213493c48bbd4972d931aae48315f18e1be9
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@types/aria-query@npm:^5.0.1":
  version: 5.0.1
  resolution: "@types/aria-query@npm:5.0.1"
  checksum: 69fd7cceb6113ed370591aef04b3fd0742e9a1b06dd045c43531448847b85de181495e4566f98e776b37c422a12fd71866e0a1dfd904c5ec3f84d271682901de
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.0.0, @types/babel__core@npm:^7.1.14":
  version: 7.20.1
  resolution: "@types/babel__core@npm:7.20.1"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: 9fcd9691a33074802d9057ff70b0e3ff3778f52470475b68698a0f6714fbe2ccb36c16b43dc924eb978cd8a81c1f845e5ff4699e7a47606043b539eb8c6331a8
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.4
  resolution: "@types/babel__generator@npm:7.6.4"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: 20effbbb5f8a3a0211e95959d06ae70c097fb6191011b73b38fe86deebefad8e09ee014605e0fd3cdaedc73d158be555866810e9166e1f09e4cfd880b874dcb0
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.1
  resolution: "@types/babel__template@npm:7.4.1"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: 649fe8b42c2876be1fd28c6ed9b276f78152d5904ec290b6c861d9ef324206e0a5c242e8305c421ac52ecf6358fa7e32ab7a692f55370484825c1df29b1596ee
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.4, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.1
  resolution: "@types/babel__traverse@npm:7.20.1"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 58341e23c649c0eba134a1682d4f20d027fad290d92e5740faa1279978f6ed476fc467ae51ce17a877e2566d805aeac64eae541168994367761ec883a4150221
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.2
  resolution: "@types/body-parser@npm:1.19.2"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: e17840c7d747a549f00aebe72c89313d09fbc4b632b949b2470c5cb3b1cb73863901ae84d9335b567a79ec5efcfb8a28ff8e3f36bc8748a9686756b6d5681f40
  languageName: node
  linkType: hard

"@types/bonjour@npm:^3.5.9":
  version: 3.5.10
  resolution: "@types/bonjour@npm:3.5.10"
  dependencies:
    "@types/node": "*"
  checksum: bfcadb042a41b124c4e3de4925e3be6d35b78f93f27c4535d5ff86980dc0f8bc407ed99b9b54528952dc62834d5a779392f7a12c2947dd19330eb05a6bcae15a
  languageName: node
  linkType: hard

"@types/connect-history-api-fallback@npm:^1.3.5":
  version: 1.5.0
  resolution: "@types/connect-history-api-fallback@npm:1.5.0"
  dependencies:
    "@types/express-serve-static-core": "*"
    "@types/node": "*"
  checksum: f180e7c540728d6dd3a1eb2376e445fe7f9de4ee8a5b460d5ad80062cdb6de6efc91c6851f39e9d5933b3dcd5cd370673c52343a959aa091238b6f863ea4447c
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.35
  resolution: "@types/connect@npm:3.4.35"
  dependencies:
    "@types/node": "*"
  checksum: fe81351470f2d3165e8b12ce33542eef89ea893e36dd62e8f7d72566dfb7e448376ae962f9f3ea888547ce8b55a40020ca0e01d637fab5d99567673084542641
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.3":
  version: 3.7.4
  resolution: "@types/eslint-scope@npm:3.7.4"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: ea6a9363e92f301cd3888194469f9ec9d0021fe0a397a97a6dd689e7545c75de0bd2153dfb13d3ab532853a278b6572c6f678ce846980669e41029d205653460
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 8.44.0
  resolution: "@types/eslint@npm:8.44.0"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 2655f409a4ecdd64bb9dd9eb6715e7a2ac30c0e7f902b414e10dbe9d6d497baa5a0f13105e1f7bd5ad7a913338e2ab4bed1faf192a7a0d27d1acd45ba79d3f69
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0":
  version: 1.0.1
  resolution: "@types/estree@npm:1.0.1"
  checksum: e9aa175eacb797216fafce4d41e8202c7a75555bc55232dee0f9903d7171f8f19f0ae7d5191bb1a88cb90e65468be508c0df850a9fb81b4433b293a5a749899d
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:*, @types/express-serve-static-core@npm:^4.17.33":
  version: 4.17.35
  resolution: "@types/express-serve-static-core@npm:4.17.35"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: cc8995d10c6feda475ec1b3a0e69eb0f35f21ab6b49129ad5c6f279e0bc5de8175bc04ec51304cb79a43eec3ed2f5a1e01472eb6d5f827b8c35c6ca8ad24eb6e
  languageName: node
  linkType: hard

"@types/express@npm:*, @types/express@npm:^4.17.13":
  version: 4.17.17
  resolution: "@types/express@npm:4.17.17"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^4.17.33
    "@types/qs": "*"
    "@types/serve-static": "*"
  checksum: 0196dacc275ac3ce89d7364885cb08e7fb61f53ca101f65886dbf1daf9b7eb05c0943e2e4bbd01b0cc5e50f37e0eea7e4cbe97d0304094411ac73e1b7998f4da
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.2":
  version: 4.1.6
  resolution: "@types/graceful-fs@npm:4.1.6"
  dependencies:
    "@types/node": "*"
  checksum: c3070ccdc9ca0f40df747bced1c96c71a61992d6f7c767e8fd24bb6a3c2de26e8b84135ede000b7e79db530a23e7e88dcd9db60eee6395d0f4ce1dae91369dd4
  languageName: node
  linkType: hard

"@types/html-minifier-terser@npm:^6.0.0":
  version: 6.1.0
  resolution: "@types/html-minifier-terser@npm:6.1.0"
  checksum: eb843f6a8d662d44fb18ec61041117734c6aae77aa38df1be3b4712e8e50ffaa35f1e1c92fdd0fde14a5675fecf457abcd0d15a01fae7506c91926176967f452
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.1
  resolution: "@types/http-errors@npm:2.0.1"
  checksum: 3bb0c50b0a652e679a84c30cd0340d696c32ef6558518268c238840346c077f899315daaf1c26c09c57ddd5dc80510f2a7f46acd52bf949e339e35ed3ee9654f
  languageName: node
  linkType: hard

"@types/http-proxy@npm:^1.17.8":
  version: 1.17.11
  resolution: "@types/http-proxy@npm:1.17.11"
  dependencies:
    "@types/node": "*"
  checksum: 38ef4f8c91c7a5b664cf6dd4d90de7863f88549a9f8ef997f2f1184e4f8cf2e7b9b63c04f0b7b962f34a09983073a31a9856de5aae5159b2ddbb905a4c44dc9f
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.4
  resolution: "@types/istanbul-lib-coverage@npm:2.0.4"
  checksum: a25d7589ee65c94d31464c16b72a9dc81dfa0bea9d3e105ae03882d616e2a0712a9c101a599ec482d297c3591e16336962878cb3eb1a0a62d5b76d277a890ce7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.0
  resolution: "@types/istanbul-lib-report@npm:3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: 656398b62dc288e1b5226f8880af98087233cdb90100655c989a09f3052b5775bf98ba58a16c5ae642fb66c61aba402e07a9f2bff1d1569e3b306026c59f3f36
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.1
  resolution: "@types/istanbul-reports@npm:3.0.1"
  dependencies:
    "@types/istanbul-lib-report": "*"
  checksum: f1ad54bc68f37f60b30c7915886b92f86b847033e597f9b34f2415acdbe5ed742fa559a0a40050d74cdba3b6a63c342cac1f3a64dba5b68b66a6941f4abd7903
  languageName: node
  linkType: hard

"@types/jest@npm:*":
  version: 29.5.3
  resolution: "@types/jest@npm:29.5.3"
  dependencies:
    expect: ^29.0.0
    pretty-format: ^29.0.0
  checksum: e36bb92e0b9e5ea7d6f8832baa42f087fc1697f6cd30ec309a07ea4c268e06ec460f1f0cfd2581daf5eff5763475190ec1ad8ac6520c49ccfe4f5c0a48bfa676
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.12
  resolution: "@types/json-schema@npm:7.0.12"
  checksum: 00239e97234eeb5ceefb0c1875d98ade6e922bfec39dd365ec6bd360b5c2f825e612ac4f6e5f1d13601b8b30f378f15e6faa805a3a732f4a1bbe61915163d293
  languageName: node
  linkType: hard

"@types/mime@npm:*":
  version: 3.0.1
  resolution: "@types/mime@npm:3.0.1"
  checksum: 4040fac73fd0cea2460e29b348c1a6173da747f3a87da0dbce80dd7a9355a3d0e51d6d9a401654f3e5550620e3718b5a899b2ec1debf18424e298a2c605346e7
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.2
  resolution: "@types/mime@npm:1.3.2"
  checksum: 0493368244cced1a69cb791b485a260a422e6fcc857782e1178d1e6f219f1b161793e9f87f5fae1b219af0f50bee24fcbe733a18b4be8fdd07a38a8fb91146fd
  languageName: node
  linkType: hard

"@types/minimatch@npm:^3.0.3":
  version: 3.0.5
  resolution: "@types/minimatch@npm:3.0.5"
  checksum: c41d136f67231c3131cf1d4ca0b06687f4a322918a3a5adddc87ce90ed9dbd175a3610adee36b106ae68c0b92c637c35e02b58c8a56c424f71d30993ea220b92
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.4.4
  resolution: "@types/node@npm:20.4.4"
  checksum: 43f3c4a8acc38ae753e15a0e79bae0447d255b3742fa87f8e065d7b9d20ecb0e03d6c5b46c00d5d26f4552160381a00255f49205595a8ee48c2423e00263c930
  languageName: node
  linkType: hard

"@types/prettier@npm:^2.1.5":
  version: 2.7.3
  resolution: "@types/prettier@npm:2.7.3"
  checksum: 705384209cea6d1433ff6c187c80dcc0b95d99d5c5ce21a46a9a58060c527973506822e428789d842761e0280d25e3359300f017fbe77b9755bc772ab3dc2f83
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.5
  resolution: "@types/prop-types@npm:15.7.5"
  checksum: 5b43b8b15415e1f298243165f1d44390403bb2bd42e662bca3b5b5633fdd39c938e91b7fce3a9483699db0f7a715d08cef220c121f723a634972fdf596aec980
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.7
  resolution: "@types/qs@npm:6.9.7"
  checksum: 7fd6f9c25053e9b5bb6bc9f9f76c1d89e6c04f7707a7ba0e44cc01f17ef5284adb82f230f542c2d5557d69407c9a40f0f3515e8319afd14e1e16b5543ac6cdba
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.4
  resolution: "@types/range-parser@npm:1.2.4"
  checksum: b7c0dfd5080a989d6c8bb0b6750fc0933d9acabeb476da6fe71d8bdf1ab65e37c136169d84148034802f48378ab94e3c37bb4ef7656b2bec2cb9c0f8d4146a95
  languageName: node
  linkType: hard

"@types/react-dom@npm:<18.0.0":
  version: 17.0.20
  resolution: "@types/react-dom@npm:17.0.20"
  dependencies:
    "@types/react": ^17
  checksum: 525439fb14a033fc5dbe74711ecc50ec82273a528df9656594066a6219401e975101dafffd15d9a1a57a9442d52ea0c92eaacae09554dde27cd792e773f67467
  languageName: node
  linkType: hard

"@types/react@npm:^17":
  version: 17.0.62
  resolution: "@types/react@npm:17.0.62"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: 428a5aff44824ef504e9a9259b5894fe44a5db1c344b536990f07e132900ff5b34cbef0be77a84f30f37be1f88fc8b56dce328f568de8d65de3bfe414c05b2e1
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.0":
  version: 0.12.0
  resolution: "@types/retry@npm:0.12.0"
  checksum: 61a072c7639f6e8126588bf1eb1ce8835f2cb9c2aba795c4491cf6310e013267b0c8488039857c261c387e9728c1b43205099223f160bb6a76b4374f741b5603
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.3
  resolution: "@types/scheduler@npm:0.16.3"
  checksum: 2b0aec39c24268e3ce938c5db2f2e77f5c3dd280e05c262d9c2fe7d890929e4632a6b8e94334017b66b45e4f92a5aa42ba3356640c2a1175fa37bef2f5200767
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.1
  resolution: "@types/send@npm:0.17.1"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: 10b620a5960058ef009afbc17686f680d6486277c62f640845381ec4baa0ea683fdd77c3afea4803daf5fcddd3fb2972c8aa32e078939f1d4e96f83195c89793
  languageName: node
  linkType: hard

"@types/serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "@types/serve-index@npm:1.9.1"
  dependencies:
    "@types/express": "*"
  checksum: 026f3995fb500f6df7c3fe5009e53bad6d739e20b84089f58ebfafb2f404bbbb6162bbe33f72d2f2af32d5b8d3799c8e179793f90d9ed5871fb8591190bb6056
  languageName: node
  linkType: hard

"@types/serve-static@npm:*, @types/serve-static@npm:^1.13.10":
  version: 1.15.2
  resolution: "@types/serve-static@npm:1.15.2"
  dependencies:
    "@types/http-errors": "*"
    "@types/mime": "*"
    "@types/node": "*"
  checksum: 15c261dbfc57890f7cc17c04d5b22b418dfa0330c912b46c5d8ae2064da5d6f844ef7f41b63c7f4bbf07675e97ebe6ac804b032635ec742ae45d6f1274259b3e
  languageName: node
  linkType: hard

"@types/sockjs@npm:^0.3.33":
  version: 0.3.33
  resolution: "@types/sockjs@npm:0.3.33"
  dependencies:
    "@types/node": "*"
  checksum: b9bbb2b5c5ead2fb884bb019f61a014e37410bddd295de28184e1b2e71ee6b04120c5ba7b9954617f0bdf962c13d06249ce65004490889c747c80d3f628ea842
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.1
  resolution: "@types/stack-utils@npm:2.0.1"
  checksum: 205fdbe3326b7046d7eaf5e494d8084f2659086a266f3f9cf00bccc549c8e36e407f88168ad4383c8b07099957ad669f75f2532ed4bc70be2b037330f7bae019
  languageName: node
  linkType: hard

"@types/testing-library__jest-dom@npm:^5.9.1":
  version: 5.14.8
  resolution: "@types/testing-library__jest-dom@npm:5.14.8"
  dependencies:
    "@types/jest": "*"
  checksum: 18f5ba7d0db8ebd91667b544537762ce63f11c4fd02b3d57afa92f9457a384d3ecf9bc7b50b6b445af1b3ea38b69862b4f95130720d4dd23621d598ac074d93a
  languageName: node
  linkType: hard

"@types/ws@npm:^8.5.5":
  version: 8.5.5
  resolution: "@types/ws@npm:8.5.5"
  dependencies:
    "@types/node": "*"
  checksum: d00bf8070e6938e3ccf933010921c6ce78ac3606696ce37a393b27a9a603f7bd93ea64f3c5fa295a2f743575ba9c9a9fdb904af0f5fe2229bf2adf0630386e4a
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.0
  resolution: "@types/yargs-parser@npm:21.0.0"
  checksum: b2f4c8d12ac18a567440379909127cf2cec393daffb73f246d0a25df36ea983b93b7e9e824251f959e9f928cbc7c1aab6728d0a0ff15d6145f66cec2be67d9a2
  languageName: node
  linkType: hard

"@types/yargs@npm:^16.0.0":
  version: 16.0.5
  resolution: "@types/yargs@npm:16.0.5"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 22697f7cc8aa32dcc10981a87f035e183303a58351c537c81fb450270d5c494b1d918186210e445b0eb2e4a8b34a8bda2a595f346bdb1c9ed2b63d193cb00430
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.24
  resolution: "@types/yargs@npm:17.0.24"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 5f3ac4dc4f6e211c1627340160fbe2fd247ceba002190da6cf9155af1798450501d628c9165a183f30a224fc68fa5e700490d740ff4c73e2cdef95bc4e8ba7bf
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.11.6, @webassemblyjs/ast@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/ast@npm:1.11.6"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
  checksum: 38ef1b526ca47c210f30975b06df2faf1a8170b1636ce239fc5738fc231ce28389dd61ecedd1bacfc03cbe95b16d1af848c805652080cb60982836eb4ed2c6cf
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.6"
  checksum: 29b08758841fd8b299c7152eda36b9eb4921e9c584eb4594437b5cd90ed6b920523606eae7316175f89c20628da14326801090167cc7fbffc77af448ac84b7e2
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.6"
  checksum: e8563df85161096343008f9161adb138a6e8f3c2cc338d6a36011aa55eabb32f2fd138ffe63bc278d009ada001cc41d263dadd1c0be01be6c2ed99076103689f
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-buffer@npm:1.11.6"
  checksum: b14d0573bf680d22b2522e8a341ec451fddd645d1f9c6bd9012ccb7e587a2973b86ab7b89fe91e1c79939ba96095f503af04369a3b356c8023c13a5893221644
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.6"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.6
    "@webassemblyjs/helper-api-error": 1.11.6
    "@xtuc/long": 4.2.2
  checksum: f4b562fa219f84368528339e0f8d273ad44e047a07641ffcaaec6f93e5b76fd86490a009aa91a294584e1436d74b0a01fa9fde45e333a4c657b58168b04da424
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.6"
  checksum: 3535ef4f1fba38de3475e383b3980f4bbf3de72bbb631c2b6584c7df45be4eccd62c6ff48b5edd3f1bcff275cfd605a37679ec199fc91fd0a7705d7f1e3972dc
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
  checksum: b2cf751bf4552b5b9999d27bbb7692d0aca75260140195cb58ea6374d7b9c2dc69b61e10b211a0e773f66209c3ddd612137ed66097e3684d7816f854997682e9
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/ieee754@npm:1.11.6"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 13574b8e41f6ca39b700e292d7edf102577db5650fe8add7066a320aa4b7a7c09a5056feccac7a74eb68c10dea9546d4461412af351f13f6b24b5f32379b49de
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/leb128@npm:1.11.6"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 7ea942dc9777d4b18a5ebfa3a937b30ae9e1d2ce1fee637583ed7f376334dd1d4274f813d2e250056cca803e0952def4b954913f1a3c9068bcd4ab4ee5143bf0
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/utf8@npm:1.11.6"
  checksum: 807fe5b5ce10c390cfdd93e0fb92abda8aebabb5199980681e7c3743ee3306a75729bcd1e56a3903980e96c885ee53ef901fcbaac8efdfa480f9c0dae1d08713
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-edit@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/helper-wasm-section": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
    "@webassemblyjs/wasm-opt": 1.11.6
    "@webassemblyjs/wasm-parser": 1.11.6
    "@webassemblyjs/wast-printer": 1.11.6
  checksum: 29ce75870496d6fad864d815ebb072395a8a3a04dc9c3f4e1ffdc63fc5fa58b1f34304a1117296d8240054cfdbc38aca88e71fb51483cf29ffab0a61ef27b481
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-gen@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: a645a2eecbea24833c3260a249704a7f554ef4a94c6000984728e94bb2bc9140a68dfd6fd21d5e0bbb09f6dfc98e083a45760a83ae0417b41a0196ff6d45a23a
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-opt@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-buffer": 1.11.6
    "@webassemblyjs/wasm-gen": 1.11.6
    "@webassemblyjs/wasm-parser": 1.11.6
  checksum: b4557f195487f8e97336ddf79f7bef40d788239169aac707f6eaa2fa5fe243557c2d74e550a8e57f2788e70c7ae4e7d32f7be16101afe183d597b747a3bdd528
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.11.6, @webassemblyjs/wasm-parser@npm:^1.11.5":
  version: 1.11.6
  resolution: "@webassemblyjs/wasm-parser@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@webassemblyjs/helper-api-error": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: 8200a8d77c15621724a23fdabe58d5571415cda98a7058f542e670ea965dd75499f5e34a48675184947c66f3df23adf55df060312e6d72d57908e3f049620d8a
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/wast-printer@npm:1.11.6"
  dependencies:
    "@webassemblyjs/ast": 1.11.6
    "@xtuc/long": 4.2.2
  checksum: d2fa6a4c427325ec81463e9c809aa6572af6d47f619f3091bf4c4a6fc34f1da3df7caddaac50b8e7a457f8784c62cd58c6311b6cb69b0162ccd8d4c072f79cf8
  languageName: node
  linkType: hard

"@webpack-cli/configtest@npm:^1.2.0":
  version: 1.2.0
  resolution: "@webpack-cli/configtest@npm:1.2.0"
  peerDependencies:
    webpack: 4.x.x || 5.x.x
    webpack-cli: 4.x.x
  checksum: a2726cd9ec601d2b57e5fc15e0ebf5200a8892065e735911269ac2038e62be4bfc176ea1f88c2c46ff09b4d05d4c10ae045e87b3679372483d47da625a327e28
  languageName: node
  linkType: hard

"@webpack-cli/info@npm:^1.5.0":
  version: 1.5.0
  resolution: "@webpack-cli/info@npm:1.5.0"
  dependencies:
    envinfo: ^7.7.3
  peerDependencies:
    webpack-cli: 4.x.x
  checksum: 7f56fe037cd7d1fd5c7428588519fbf04a0cad33925ee4202ffbafd00f8ec1f2f67d991245e687d50e0f3e23f7b7814273d56cb9f7da4b05eed47c8d815c6296
  languageName: node
  linkType: hard

"@webpack-cli/serve@npm:^1.7.0":
  version: 1.7.0
  resolution: "@webpack-cli/serve@npm:1.7.0"
  peerDependencies:
    webpack-cli: 4.x.x
  peerDependenciesMeta:
    webpack-dev-server:
      optional: true
  checksum: d475e8effa23eb7ff9a48b14d4de425989fd82f906ce71c210921cc3852327c22873be00c35e181a25a6bd03d424ae2b83e7f3b3f410ac7ee31b128ab4ac7713
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abab@npm:^2.0.3, abab@npm:^2.0.5":
  version: 2.0.6
  resolution: "abab@npm:2.0.6"
  checksum: 6ffc1af4ff315066c62600123990d87551ceb0aafa01e6539da77b0f5987ac7019466780bf480f1787576d4385e3690c81ccc37cfda12819bf510b8ab47e5a3e
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.5, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-globals@npm:^6.0.0":
  version: 6.0.0
  resolution: "acorn-globals@npm:6.0.0"
  dependencies:
    acorn: ^7.1.1
    acorn-walk: ^7.1.1
  checksum: 72d95e5b5e585f9acd019b993ab8bbba68bb3cbc9d9b5c1ebb3c2f1fe5981f11deababfb4949f48e6262f9c57878837f5958c0cca396f81023814680ca878042
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.9.0":
  version: 1.9.0
  resolution: "acorn-import-assertions@npm:1.9.0"
  peerDependencies:
    acorn: ^8
  checksum: 944fb2659d0845c467066bdcda2e20c05abe3aaf11972116df457ce2627628a81764d800dd55031ba19de513ee0d43bb771bc679cc0eda66dc8b4fade143bc0c
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.1":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^7.1.1":
  version: 7.2.0
  resolution: "acorn-walk@npm:7.2.0"
  checksum: 9252158a79b9d92f1bc0dd6acc0fcfb87a67339e84bcc301bb33d6078936d27e35d606b4d35626d2962cd43c256d6f27717e70cbe15c04fff999ab0b2260b21f
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^7.1.1, acorn@npm:^7.4.0":
  version: 7.4.1
  resolution: "acorn@npm:7.4.1"
  bin:
    acorn: bin/acorn
  checksum: 1860f23c2107c910c6177b7b7be71be350db9e1080d814493fae143ae37605189504152d1ba8743ba3178d0b37269ce1ffc42b101547fdc1827078f82671e407
  languageName: node
  linkType: hard

"acorn@npm:^8.0.4, acorn@npm:^8.2.4, acorn@npm:^8.7.1, acorn@npm:^8.8.2":
  version: 8.10.0
  resolution: "acorn@npm:8.10.0"
  bin:
    acorn: bin/acorn
  checksum: 538ba38af0cc9e5ef983aee196c4b8b4d87c0c94532334fa7e065b2c8a1f85863467bb774231aae91613fcda5e68740c15d97b1967ae3394d20faddddd8af61d
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.3.0
  resolution: "agentkeepalive@npm:4.3.0"
  dependencies:
    debug: ^4.1.0
    depd: ^2.0.0
    humanize-ms: ^1.2.1
  checksum: 982453aa44c11a06826c836025e5162c846e1200adb56f2d075400da7d32d87021b3b0a58768d949d824811f5654223d5a8a3dad120921a2439625eb847c6260
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.0.1, ajv@npm:^8.9.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: 4dc13714e316e67537c8b31bc063f99a1d9d9a497eb4bbd55191ac0dcd5e4985bbb71570352ad6f1e76684fb6d790928f96ba3b2d4fd6e10024be9612fe3f001
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: a9c2ec842038a1fabc7db9ece7d3177e2fe1c5dc6f0c51ecfbf5f39911427b89c00b5dc6b8bd95f82a26e9b16aaae2e83d45f060e98070ce4d1333038edceb0e
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-html-community@npm:^0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 04c568e8348a636963f915e48eaa3e01218322e1169acafdd79c384f22e5558c003f79bbc480c1563865497482817c7eed025f0653ebc17642fededa5cb42089
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"aria-query@npm:5.1.3":
  version: 5.1.3
  resolution: "aria-query@npm:5.1.3"
  dependencies:
    deep-equal: ^2.0.5
  checksum: 929ff95f02857b650fb4cbcd2f41072eee2f46159a6605ea03bf63aa572e35ffdff43d69e815ddc462e16e07de8faba3978afc2813650b4448ee18c9895d982b
  languageName: node
  linkType: hard

"aria-query@npm:^5.0.0, aria-query@npm:^5.1.3":
  version: 5.3.0
  resolution: "aria-query@npm:5.3.0"
  dependencies:
    dequal: ^2.0.3
  checksum: 305bd73c76756117b59aba121d08f413c7ff5e80fa1b98e217a3443fcddb9a232ee790e24e432b59ae7625aebcf4c47cb01c2cac872994f0b426f5bdfcd96ba9
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-buffer-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    is-array-buffer: ^3.0.1
  checksum: 044e101ce150f4804ad19c51d6c4d4cfa505c5b2577bd179256e4aa3f3f6a0a5e9874c78cd428ee566ac574c8a04d7ce21af9fe52e844abfdccb82b33035a7c3
  languageName: node
  linkType: hard

"array-differ@npm:^3.0.0":
  version: 3.0.0
  resolution: "array-differ@npm:3.0.0"
  checksum: 117edd9df5c1530bd116c6e8eea891d4bd02850fd89b1b36e532b6540e47ca620a373b81feca1c62d1395d9ae601516ba538abe5e8172d41091da2c546b05fb7
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.2":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6":
  version: 3.1.6
  resolution: "array-includes@npm:3.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    get-intrinsic: ^1.1.3
    is-string: ^1.0.7
  checksum: f22f8cd8ba8a6448d91eebdc69f04e4e55085d09232b5216ee2d476dab3ef59984e8d1889e662c6a0ed939dcb1b57fd05b2c0209c3370942fc41b752c82a2ca5
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.1
  resolution: "array.prototype.flat@npm:1.3.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
  checksum: 5a8415949df79bf6e01afd7e8839bbde5a3581300e8ad5d8449dea52639e9e59b26a467665622783697917b43bf39940a6e621877c7dd9b3d1c1f97484b9b88b
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.1":
  version: 1.3.1
  resolution: "array.prototype.flatmap@npm:1.3.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
    es-shim-unscopables: ^1.0.0
  checksum: 8c1c43a4995f12cf12523436da28515184c753807b3f0bc2ca6c075f71c470b099e2090cc67dba8e5280958fea401c1d0c59e1db0143272aef6cd1103921a987
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.1":
  version: 1.0.1
  resolution: "arraybuffer.prototype.slice@npm:1.0.1"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    get-intrinsic: ^1.2.1
    is-array-buffer: ^3.0.2
    is-shared-array-buffer: ^1.0.2
  checksum: e3e9b2a3e988ebfeddce4c7e8f69df730c9e48cb04b0d40ff0874ce3d86b3d1339dd520ffde5e39c02610bc172ecfbd4bc93324b1cabd9554c44a56b131ce0ce
  languageName: node
  linkType: hard

"arrify@npm:^2.0.1":
  version: 2.0.1
  resolution: "arrify@npm:2.0.1"
  checksum: 067c4c1afd182806a82e4c1cb8acee16ab8b5284fbca1ce29408e6e91281c36bb5b612f6ddfbd40a0f7a7e0c75bf2696eb94c027f6e328d6e9c52465c98e4209
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.7":
  version: 0.0.7
  resolution: "ast-types-flow@npm:0.0.7"
  checksum: a26dcc2182ffee111cad7c471759b0bda22d3b7ebacf27c348b22c55f16896b18ab0a4d03b85b4020dce7f3e634b8f00b593888f622915096ea1927fa51866c4
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 20eb47b3cefd7db027b9bbb993c658abd36d4edd3fe1060e83699a03ee275b0c9b216cc076ff3f2db29073225fb70e7613987af14269ac1fe2a19803ccc97f1a
  languageName: node
  linkType: hard

"axe-core@npm:^4.6.2":
  version: 4.7.2
  resolution: "axe-core@npm:4.7.2"
  checksum: 5d86fa0f45213b0e54cbb5d713ce885c4a8fe3a72b92dd915a47aa396d6fd149c4a87fec53aa978511f6d941402256cfeb26f2db35129e370f25a453c688655a
  languageName: node
  linkType: hard

"axobject-query@npm:^3.1.1":
  version: 3.2.1
  resolution: "axobject-query@npm:3.2.1"
  dependencies:
    dequal: ^2.0.3
  checksum: a94047e702b57c91680e6a952ec4a1aaa2cfd0d80ead76bc8c954202980d8c51968a6ea18b4d8010e8e2cf95676533d8022a8ebba9abc1dfe25686721df26fd2
  languageName: node
  linkType: hard

"babel-jest@npm:^27.0.6, babel-jest@npm:^27.5.1":
  version: 27.5.1
  resolution: "babel-jest@npm:27.5.1"
  dependencies:
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/babel__core": ^7.1.14
    babel-plugin-istanbul: ^6.1.1
    babel-preset-jest: ^27.5.1
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    slash: ^3.0.0
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 4e93e6e9fb996cc5f1505e924eb8e8cc7b25c294ba9629762a2715390f48af6a4c14dbb84cd9730013ac0e03267a5a9aa2fb6318c544489cda7f50f4e506def4
  languageName: node
  linkType: hard

"babel-loader@npm:^8.2.2":
  version: 8.3.0
  resolution: "babel-loader@npm:8.3.0"
  dependencies:
    find-cache-dir: ^3.3.1
    loader-utils: ^2.0.0
    make-dir: ^3.1.0
    schema-utils: ^2.6.5
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: d48bcf9e030e598656ad3ff5fb85967db2eaaf38af5b4a4b99d25618a2057f9f100e6b231af2a46c1913206db506115ca7a8cbdf52c9c73d767070dae4352ab5
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@istanbuljs/load-nyc-config": ^1.0.0
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-instrument: ^5.0.4
    test-exclude: ^6.0.0
  checksum: cb4fd95738219f232f0aece1116628cccff16db891713c4ccb501cddbbf9272951a5df81f2f2658dfdf4b3e7b236a9d5cbcf04d5d8c07dd5077297339598061a
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^27.5.1":
  version: 27.5.1
  resolution: "babel-plugin-jest-hoist@npm:27.5.1"
  dependencies:
    "@babel/template": ^7.3.3
    "@babel/types": ^7.3.3
    "@types/babel__core": ^7.0.0
    "@types/babel__traverse": ^7.0.6
  checksum: 709c17727aa8fd3be755d256fb514bf945a5c2ea6017f037d80280fc44ae5fe7dfeebf63d8412df53796455c2c216119d628d8cc90b099434fd819005943d058
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.4":
  version: 0.4.5
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.5"
  dependencies:
    "@babel/compat-data": ^7.22.6
    "@babel/helper-define-polyfill-provider": ^0.4.2
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 33a8e06aa54e2858d211c743d179f0487b03222f9ca1bfd7c4865bca243fca942a3358cb75f6bb894ed476cbddede834811fbd6903ff589f055821146f053e1a
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.8.2":
  version: 0.8.3
  resolution: "babel-plugin-polyfill-corejs3@npm:0.8.3"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
    core-js-compat: ^3.31.0
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: dcbb30e551702a82cfd4d2c375da2c317658e55f95e9edcda93b9bbfdcc8fb6e5344efcb144e04d3406859e7682afce7974c60ededd9f12072a48a83dd22a0da
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.5.1":
  version: 0.5.2
  resolution: "babel-plugin-polyfill-regenerator@npm:0.5.2"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.4.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d962200f604016a9a09bc9b4aaf60a3db7af876bb65bcefaeac04d44ac9d9ec4037cf24ce117760cc141d7046b6394c7eb0320ba9665cb4a2ee64df2be187c93
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.0.1
  resolution: "babel-preset-current-node-syntax@npm:1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-bigint": ^7.8.3
    "@babel/plugin-syntax-class-properties": ^7.8.3
    "@babel/plugin-syntax-import-meta": ^7.8.3
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.8.3
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.8.3
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-top-level-await": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d118c2742498c5492c095bc8541f4076b253e705b5f1ad9a2e7d302d81a84866f0070346662355c8e25fc02caa28dc2da8d69bcd67794a0d60c4d6fab6913cc8
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^27.5.1":
  version: 27.5.1
  resolution: "babel-preset-jest@npm:27.5.1"
  dependencies:
    babel-plugin-jest-hoist: ^27.5.1
    babel-preset-current-node-syntax: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 251bcea11c18fd9672fec104eadb45b43f117ceeb326fa7345ced778d4c1feab29343cd7a87a1dcfae4997d6c851a8b386d7f7213792da6e23b74f4443a8976d
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 61f9934c7378a51dce61b915586191078ef7f1c3eca707fdd58b96ff2ff56d9e0af2bdab66b1462301a73c73374239e6542d9821c0af787f3209a23365d07e7f
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"body-parser@npm:1.20.1":
  version: 1.20.1
  resolution: "body-parser@npm:1.20.1"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.4
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.11.0
    raw-body: 2.5.1
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: f1050dbac3bede6a78f0b87947a8d548ce43f91ccc718a50dd774f3c81f2d8b04693e52acf62659fad23101827dd318da1fb1363444ff9a8482b886a3e4a5266
  languageName: node
  linkType: hard

"bonjour-service@npm:^1.0.11":
  version: 1.1.1
  resolution: "bonjour-service@npm:1.1.1"
  dependencies:
    array-flatten: ^2.1.2
    dns-equal: ^1.0.0
    fast-deep-equal: ^3.1.3
    multicast-dns: ^7.2.5
  checksum: 832d0cf78b91368fac8bb11fd7a714e46f4c4fb1bb14d7283bce614a6fb3aae2f3fe209aba5b4fa051811c1cab6921d073a83db8432fb23292f27dd4161fb0f1
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"browser-process-hrtime@npm:^1.0.0":
  version: 1.0.0
  resolution: "browser-process-hrtime@npm:1.0.0"
  checksum: e30f868cdb770b1201afb714ad1575dd86366b6e861900884665fb627109b3cc757c40067d3bfee1ff2a29c835257ea30725a8018a9afd02ac1c24b408b1e45f
  languageName: node
  linkType: hard

"browserslist-config-single-spa@npm:^1.0.1":
  version: 1.0.1
  resolution: "browserslist-config-single-spa@npm:1.0.1"
  checksum: 7857db70f4733237caeef2022a3662cf373ee83ab813904aaa0ae40626666b4c5c72488aedb833e92b622c54154c3f452647f5e70499950d05898d81dc0f7109
  languageName: node
  linkType: hard

"browserslist@npm:^4.14.5, browserslist@npm:^4.21.9":
  version: 4.21.9
  resolution: "browserslist@npm:4.21.9"
  dependencies:
    caniuse-lite: ^1.0.30001503
    electron-to-chromium: ^1.4.431
    node-releases: ^2.0.12
    update-browserslist-db: ^1.0.11
  bin:
    browserslist: cli.js
  checksum: 80d3820584e211484ad1b1a5cfdeca1dd00442f47be87e117e1dda34b628c87e18b81ae7986fa5977b3e6a03154f6d13cd763baa6b8bf5dd9dd19f4926603698
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 9ba4dc58ce86300c862bffc3ae91f00b2a03b01ee07f3564beeeaf82aa243b8b03ba53f123b0b842c190d4399b94697970c8e7cf7b1ea44b61aa28c3526a4449
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"bytes@npm:3.0.0":
  version: 3.0.0
  resolution: "bytes@npm:3.0.0"
  checksum: a2b386dd8188849a5325f58eef69c3b73c51801c08ffc6963eddc9be244089ba32d19347caf6d145c86f315ae1b1fc7061a32b0c1aa6379e6a719090287ed101
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^17.0.0":
  version: 17.1.3
  resolution: "cacache@npm:17.1.3"
  dependencies:
    "@npmcli/fs": ^3.1.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^7.7.1
    minipass: ^5.0.0
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^4.0.0
    ssri: ^10.0.0
    tar: ^6.1.11
    unique-filename: ^3.0.0
  checksum: 385756781e1e21af089160d89d7462b7ed9883c978e848c7075b90b73cb823680e66092d61513050164588387d2ca87dd6d910e28d64bc13a9ac82cd8580c796
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: ^3.1.2
    tslib: ^2.0.3
  checksum: bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001503":
  version: 1.0.30001517
  resolution: "caniuse-lite@npm:1.0.30001517"
  checksum: e4e87436ae1c4408cf4438aac22902b31eb03f3f5bad7f33bc518d12ffb35f3fd9395ccf7efc608ee046f90ce324ec6f7f26f8a8172b8c43c26a06ecee612a29
  languageName: node
  linkType: hard

"chalk@npm:^2.0.0":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 8e3ddf3981c4da405ddbd7d9c8d91944ddf6e33d6837756979f7840a29272a69a5189ecae0ff84006750d6d1e92368d413335eab4db5476db6e6703a1d1e0505
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: b563e4b6039b15213114626621e7a3d12f31008bdce20f9c741d69987f62aeaace7ec30f6018890ad77b2e9b4d95324c9f5acfca58a9441e3b1dcdd1e2525d17
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: b49fcde40176ba007ff361b198a2d35df60d9bb2a5aab228279eb810feae9294a6b4649ab15981304447afe1e6ffbf4788ad5db77235dc770ab777c6e771980c
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.8.0
  resolution: "ci-info@npm:3.8.0"
  checksum: d0a4d3160497cae54294974a7246202244fff031b0a6ea20dd57b10ec510aa17399c41a1b0982142c105f3255aff2173e5c0dd7302ee1b2f28ba3debda375098
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.2.3
  resolution: "cjs-module-lexer@npm:1.2.3"
  checksum: 5ea3cb867a9bb609b6d476cd86590d105f3cfd6514db38ff71f63992ab40939c2feb68967faa15a6d2b1f90daa6416b79ea2de486e9e2485a6f8b66a21b4fb0a
  languageName: node
  linkType: hard

"clean-css@npm:^5.2.2":
  version: 5.3.2
  resolution: "clean-css@npm:5.3.2"
  dependencies:
    source-map: ~0.6.0
  checksum: 8787b281acc9878f309b5f835d410085deedfd4e126472666773040a6a8a72f472a1d24185947d23b87b1c419bf2c5ed429395d5c5ff8279c98b05d8011e9758
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cliui@npm:^7.0.2":
  version: 7.0.4
  resolution: "cliui@npm:7.0.4"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^7.0.0
  checksum: ce2e8f578a4813806788ac399b9e866297740eecd4ad1823c27fd344d78b22c5f8597d548adbcc46f0573e43e21e751f39446c5a5e804a12aace402b7a315d7f
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: c10f41c39ab84629d16f9f6137bc8a63d332244383fc368caf2d2052b5e04c20cd1fd70f66fcf4e2422b84c8226598b776d39d5f2d2a51867cc1ed5d1982b4da
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"colorette@npm:^2.0.10, colorette@npm:^2.0.14":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^7.0.0, commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"compressible@npm:~2.0.16":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.7.4
  resolution: "compression@npm:1.7.4"
  dependencies:
    accepts: ~1.3.5
    bytes: 3.0.0
    compressible: ~2.0.16
    debug: 2.6.9
    on-headers: ~1.0.2
    safe-buffer: 5.1.2
    vary: ~1.1.2
  checksum: 35c0f2eb1f28418978615dc1bc02075b34b1568f7f56c62d60f4214d4b7cc00d0f6d282b5f8a954f59872396bd770b6b15ffd8aa94c67d4bce9b8887b906999b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concurrently@npm:^6.2.1":
  version: 6.5.1
  resolution: "concurrently@npm:6.5.1"
  dependencies:
    chalk: ^4.1.0
    date-fns: ^2.16.1
    lodash: ^4.17.21
    rxjs: ^6.6.3
    spawn-command: ^0.0.2-1
    supports-color: ^8.1.0
    tree-kill: ^1.2.2
    yargs: ^16.2.0
  bin:
    concurrently: bin/concurrently.js
  checksum: 3f4d89b464fa5c9fb6f9489b46594c30ba54eff6ff10ab3cb5f30f64b74c83be664623a0f0cc731a3cb3f057a1f4a3292f7d3470c012a292c44aca31f214a3fa
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^2.0.0":
  version: 2.0.0
  resolution: "connect-history-api-fallback@npm:2.0.0"
  checksum: dc5368690f4a5c413889792f8df70d5941ca9da44523cde3f87af0745faee5ee16afb8195434550f0504726642734f2683d6c07f8b460f828a12c45fbd4c9a68
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.4.0, convert-source-map@npm:^1.6.0, convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: 1f4bd2ca5765f8c9689a7e8954183f5332139eb72b6ff783d8947032ec1fdf43109852c178e21a953a30c0dd42257828185be01b49d1eb1a67fd054ca588a180
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.31.0":
  version: 3.31.1
  resolution: "core-js-compat@npm:3.31.1"
  dependencies:
    browserslist: ^4.21.9
  checksum: 9a16d6992621f4e099169297381a28d5712cdef7df1fa85352a7c285a5885d5d7a117ec2eae9ad715ed88c7cc774787a22cdb8aceababf6775fbc8b0cbeccdb7
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"css-loader@npm:^5.2.7":
  version: 5.2.7
  resolution: "css-loader@npm:5.2.7"
  dependencies:
    icss-utils: ^5.1.0
    loader-utils: ^2.0.0
    postcss: ^8.2.15
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.0
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.1.0
    schema-utils: ^3.0.0
    semver: ^7.3.5
  peerDependencies:
    webpack: ^4.27.0 || ^5.0.0
  checksum: fb0742b30ac0919f94b99a323bdefe6d48ae46d66c7d966aae59031350532f368f8bba5951fcd268f2e053c5e6e4655551076268e9073ccb58e453f98ae58f8e
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.0.1
    domhandler: ^4.3.1
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: d6202736839194dd7f910320032e7cfc40372f025e4bf21ca5bf6eb0a33264f322f50ba9c0adc35dadd342d3d6fae5ca244779a4873afbfa76561e343f2058e0
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"css.escape@npm:^1.5.1":
  version: 1.5.1
  resolution: "css.escape@npm:1.5.1"
  checksum: f6d38088d870a961794a2580b2b2af1027731bb43261cfdce14f19238a88664b351cc8978abc20f06cc6bbde725699dec8deb6fe9816b139fc3f2af28719e774
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssom@npm:^0.4.4":
  version: 0.4.4
  resolution: "cssom@npm:0.4.4"
  checksum: e3bc1076e7ee4213d4fef05e7ae03bfa83dc05f32611d8edc341f4ecc3d9647b89c8245474c7dd2cdcdb797a27c462e99da7ad00a34399694559f763478ff53f
  languageName: node
  linkType: hard

"cssom@npm:~0.3.6":
  version: 0.3.8
  resolution: "cssom@npm:0.3.8"
  checksum: 24beb3087c76c0d52dd458be9ee1fbc80ac771478a9baef35dd258cdeb527c68eb43204dd439692bb2b1ae5272fa5f2946d10946edab0d04f1078f85e06bc7f6
  languageName: node
  linkType: hard

"cssstyle@npm:^2.3.0":
  version: 2.3.0
  resolution: "cssstyle@npm:2.3.0"
  dependencies:
    cssom: ~0.3.6
  checksum: 5f05e6fd2e3df0b44695c2f08b9ef38b011862b274e320665176467c0725e44a53e341bc4959a41176e83b66064ab786262e7380fd1cabeae6efee0d255bb4e3
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.2
  resolution: "csstype@npm:3.1.2"
  checksum: e1a52e6c25c1314d6beef5168da704ab29c5186b877c07d822bd0806717d9a265e8493a2e35ca7e68d0f5d472d43fac1cdce70fd79fd0853dff81f3028d857b5
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: d240b7757544460ae0586a341a53110ab0a61126570ef2d8c731e3eab3f0cb6e488e2609e6a69b46727635de49be20b071688698744417ff1b6c1d7ccd03e0de
  languageName: node
  linkType: hard

"data-urls@npm:^2.0.0":
  version: 2.0.0
  resolution: "data-urls@npm:2.0.0"
  dependencies:
    abab: ^2.0.3
    whatwg-mimetype: ^2.3.0
    whatwg-url: ^8.0.0
  checksum: 97caf828aac25e25e04ba6869db0f99c75e6859bb5b424ada28d3e7841941ebf08ddff3c1b1bb4585986bd507a5d54c2a716853ea6cb98af877400e637393e71
  languageName: node
  linkType: hard

"date-fns@npm:^2.16.1":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": ^7.21.0
  checksum: f7be01523282e9bb06c0cd2693d34f245247a29098527d4420628966a2d9aad154bd0e90a6b1cf66d37adcb769cd108cf8a7bd49d76db0fb119af5cdd13644f4
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.1, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.3":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"decimal.js@npm:^10.2.1":
  version: 10.4.3
  resolution: "decimal.js@npm:10.4.3"
  checksum: 796404dcfa9d1dbfdc48870229d57f788b48c21c603c3f6554a1c17c10195fc1024de338b0cf9e1efe0c7c167eeb18f04548979bcc5fdfabebb7cc0ae3287bae
  languageName: node
  linkType: hard

"dedent@npm:^0.7.0":
  version: 0.7.0
  resolution: "dedent@npm:0.7.0"
  checksum: 87de191050d9a40dd70cad01159a0bcf05ecb59750951242070b6abf9569088684880d00ba92a955b4058804f16eeaf91d604f283929b4f614d181cd7ae633d2
  languageName: node
  linkType: hard

"deep-equal@npm:^2.0.5":
  version: 2.2.2
  resolution: "deep-equal@npm:2.2.2"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    call-bind: ^1.0.2
    es-get-iterator: ^1.1.3
    get-intrinsic: ^1.2.1
    is-arguments: ^1.1.1
    is-array-buffer: ^3.0.2
    is-date-object: ^1.0.5
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.2
    isarray: ^2.0.5
    object-is: ^1.1.5
    object-keys: ^1.1.1
    object.assign: ^4.1.4
    regexp.prototype.flags: ^1.5.0
    side-channel: ^1.0.4
    which-boxed-primitive: ^1.0.2
    which-collection: ^1.0.1
    which-typed-array: ^1.1.9
  checksum: eb61c35157b6ecb96a5359b507b083fbff8ddb4c86a78a781ee38485f77a667465e45d63ee2ebd8a00e86d94c80e499906900cd82c2debb400237e1662cd5397
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"default-gateway@npm:^6.0.3":
  version: 6.0.3
  resolution: "default-gateway@npm:6.0.3"
  dependencies:
    execa: ^5.0.0
  checksum: 126f8273ecac8ee9ff91ea778e8784f6cd732d77c3157e8c5bdd6ed03651b5291f71446d05bc02d04073b1e67583604db5394ea3cf992ede0088c70ea15b7378
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 0115fdb065e0490918ba271d7339c42453d209d4cb619dfe635870d906731eff3e1ade8028bb461ea27ce8264ec5e22c6980612d332895977e89c1bbc80fcee2
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.1.4, define-properties@npm:^1.2.0":
  version: 1.2.0
  resolution: "define-properties@npm:1.2.0"
  dependencies:
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: e60aee6a19b102df4e2b1f301816804e81ab48bb91f00d0d935f269bf4b3f79c88b39e4f89eaa132890d23267335fd1140dfcd8d5ccd61031a0a2c41a54e33a6
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 8679b850e1a3d0ebbc46ee780d5df7b478c23f335887464023a631d1b9af051ad4a6595a44220f9ff8ff95a8ddccf019b5ad778a976fd7bbf77383d36f412f90
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"diff-sequences@npm:^27.5.1":
  version: 27.5.1
  resolution: "diff-sequences@npm:27.5.1"
  checksum: a00db5554c9da7da225db2d2638d85f8e41124eccbd56cbaefb3b276dcbb1c1c2ad851c32defe2055a54a4806f030656cbf6638105fd6ce97bb87b90b32a33ca
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.4.3":
  version: 29.4.3
  resolution: "diff-sequences@npm:29.4.3"
  checksum: 28b265e04fdddcf7f9f814effe102cc95a9dec0564a579b5aed140edb24fc345c611ca52d76d725a3cab55d3888b915b5e8a4702e0f6058968a90fa5f41fcde7
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: a8471ac849c7c13824f053babea1bc26e2f359394dd5a460f8340d8abd13434be01e3327a5c59d212f8c8997817450efd3f3ac77bec709b21979cf0235644524
  languageName: node
  linkType: hard

"dns-packet@npm:^5.2.2":
  version: 5.6.0
  resolution: "dns-packet@npm:5.6.0"
  dependencies:
    "@leichtgewicht/ip-codec": ^2.0.1
  checksum: 1b643814e5947a87620f8a906287079347492282964ce1c236d52c414e3e3941126b96581376b180ba6e66899e70b86b587bc1aa23e3acd9957765be952d83fc
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-accessibility-api@npm:^0.5.6, dom-accessibility-api@npm:^0.5.9":
  version: 0.5.16
  resolution: "dom-accessibility-api@npm:0.5.16"
  checksum: 005eb283caef57fc1adec4d5df4dd49189b628f2f575af45decb210e04d634459e3f1ee64f18b41e2dcf200c844bc1d9279d80807e686a30d69a4756151ad248
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: ~0.4
  checksum: ea52fe303f5392e48dea563abef0e6fb3a478b8dbe3c599e99bb5d53981c6c38fc4944e56bb92a8ead6bb989d10b7914722ae11febbd2fd0910e33b9fc4aaa77
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: fbb0b01f87a8a2d18e6e5a388ad0f7ec4a5c05c06d219377da1abc7bb0f674d804f4a8a94e3f71ff15f6cb7dcfc75704a54b261db672b9b3ab03da6b758b0b22
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domexception@npm:^2.0.1":
  version: 2.0.1
  resolution: "domexception@npm:2.0.1"
  dependencies:
    webidl-conversions: ^5.0.0
  checksum: d638e9cb05c52999f1b2eb87c374b03311ea5b1d69c2f875bc92da73e17db60c12142b45c950228642ff7f845c536b65305483350d080df59003a653da80b691
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: ^2.2.0
  checksum: 4c665ceed016e1911bf7d1dadc09dc888090b64dee7851cccd2fcf5442747ec39c647bb1cb8c8919f8bbdd0f0c625a6bafeeed4b2d656bbecdbae893f43ffaaa
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.431":
  version: 1.4.468
  resolution: "electron-to-chromium@npm:1.4.468"
  checksum: 3da242381c95286a928ef03501c0fdcc71b773e382cc8574aa998562ae8c9b14712c910879b41d6184fd954ccecc2424865754a86e20d83077a35a0b75a1cfea
  languageName: node
  linkType: hard

"emittery@npm:^0.8.1":
  version: 0.8.1
  resolution: "emittery@npm:0.8.1"
  checksum: 2457e8c7b0688bb006126f2c025b2655abe682f66b184954122a8a065b5277f9813d49d627896a10b076b81c513ec5f491fd9c14fbd42c04b95ca3c9f3c365ee
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.15.0":
  version: 5.15.0
  resolution: "enhanced-resolve@npm:5.15.0"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: fbd8cdc9263be71cc737aa8a7d6c57b43d6aa38f6cc75dde6fcd3598a130cc465f979d2f4d01bb3bf475acb43817749c79f8eef9be048683602ca91ab52e4f11
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.5":
  version: 2.3.6
  resolution: "enquirer@npm:2.3.6"
  dependencies:
    ansi-colors: ^4.1.1
  checksum: 1c0911e14a6f8d26721c91e01db06092a5f7675159f0261d69c403396a385afd13dd76825e7678f66daffa930cfaa8d45f506fb35f818a2788463d022af1b884
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"envinfo@npm:^7.7.3":
  version: 7.10.0
  resolution: "envinfo@npm:7.10.0"
  bin:
    envinfo: dist/cli.js
  checksum: 05e81a5768c42cbd5c580dc3f274db3401facadd53e9bd52e2aa49dfbb5d8b26f6181c25a6652d79618a6994185bd2b1c137673101690b147f758e4e71d42f7d
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.19.0, es-abstract@npm:^1.20.4":
  version: 1.22.1
  resolution: "es-abstract@npm:1.22.1"
  dependencies:
    array-buffer-byte-length: ^1.0.0
    arraybuffer.prototype.slice: ^1.0.1
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    es-set-tostringtag: ^2.0.1
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.5
    get-intrinsic: ^1.2.1
    get-symbol-description: ^1.0.0
    globalthis: ^1.0.3
    gopd: ^1.0.1
    has: ^1.0.3
    has-property-descriptors: ^1.0.0
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.5
    is-array-buffer: ^3.0.2
    is-callable: ^1.2.7
    is-negative-zero: ^2.0.2
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.2
    is-string: ^1.0.7
    is-typed-array: ^1.1.10
    is-weakref: ^1.0.2
    object-inspect: ^1.12.3
    object-keys: ^1.1.1
    object.assign: ^4.1.4
    regexp.prototype.flags: ^1.5.0
    safe-array-concat: ^1.0.0
    safe-regex-test: ^1.0.0
    string.prototype.trim: ^1.2.7
    string.prototype.trimend: ^1.0.6
    string.prototype.trimstart: ^1.0.6
    typed-array-buffer: ^1.0.0
    typed-array-byte-length: ^1.0.0
    typed-array-byte-offset: ^1.0.0
    typed-array-length: ^1.0.4
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.10
  checksum: 614e2c1c3717cb8d30b6128ef12ea110e06fd7d75ad77091ca1c5dbfb00da130e62e4bbbbbdda190eada098a22b27fe0f99ae5a1171dac2c8663b1e8be8a3a9b
  languageName: node
  linkType: hard

"es-get-iterator@npm:^1.1.3":
  version: 1.1.3
  resolution: "es-get-iterator@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.3
    has-symbols: ^1.0.3
    is-arguments: ^1.1.1
    is-map: ^2.0.2
    is-set: ^2.0.2
    is-string: ^1.0.7
    isarray: ^2.0.5
    stop-iteration-iterator: ^1.0.0
  checksum: 8fa118da42667a01a7c7529f8a8cca514feeff243feec1ce0bb73baaa3514560bd09d2b3438873cf8a5aaec5d52da248131de153b28e2638a061b6e4df13267d
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.3.0
  resolution: "es-module-lexer@npm:1.3.0"
  checksum: 48fd9f504a9d2a894126f75c8b7ccc6273a289983e9b67255f165bfd9ae765d50100218251e94e702ca567826905ea2f7b3b4a0c4d74d3ce99cce3a2a606a238
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.1":
  version: 2.0.1
  resolution: "es-set-tostringtag@npm:2.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
    has: ^1.0.3
    has-tostringtag: ^1.0.0
  checksum: ec416a12948cefb4b2a5932e62093a7cf36ddc3efd58d6c58ca7ae7064475ace556434b869b0bbeb0c365f1032a8ccd577211101234b69837ad83ad204fff884
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-shim-unscopables@npm:1.0.0"
  dependencies:
    has: ^1.0.3
  checksum: 83e95cadbb6ee44d3644dfad60dcad7929edbc42c85e66c3e99aefd68a3a5c5665f2686885cddb47dfeabfd77bd5ea5a7060f2092a955a729bbd8834f0d86fa1
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escodegen@npm:^2.0.0":
  version: 2.1.0
  resolution: "escodegen@npm:2.1.0"
  dependencies:
    esprima: ^4.0.1
    estraverse: ^5.2.0
    esutils: ^2.0.2
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 096696407e161305cd05aebb95134ad176708bc5cb13d0dcc89a5fcbb959b8ed757e7f2591a5f8036f8f4952d4a724de0df14cd419e29212729fa6df5ce16bf6
  languageName: node
  linkType: hard

"eslint-config-important-stuff@npm:^1.1.0":
  version: 1.1.0
  resolution: "eslint-config-important-stuff@npm:1.1.0"
  checksum: 17986ec3fda91d9b2e08603817d2ff5aaba23651fb6c722e69df02290e31075f62349e16f4f03f1772f08c678904de51e82b735945f3a895f5d29aae7ef8b397
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.3.0":
  version: 8.8.0
  resolution: "eslint-config-prettier@npm:8.8.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 1e94c3882c4d5e41e1dcfa2c368dbccbfe3134f6ac7d40101644d3bfbe3eb2f2ffac757f3145910b5eacf20c0e85e02b91293d3126d770cbf3dc390b3564681c
  languageName: node
  linkType: hard

"eslint-config-react-important-stuff@npm:^3.0.0":
  version: 3.0.0
  resolution: "eslint-config-react-important-stuff@npm:3.0.0"
  dependencies:
    eslint-config-important-stuff: ^1.1.0
    eslint-plugin-jsx-a11y: ^6.3.1
    eslint-plugin-react-hooks: ^4.0.8
  checksum: 542547f0be617d27d828cf8d55ac3c640fa928a18b223b5865d943c575999ec8905a2bc581d184fa59f7b633f8b7d14b362ba6d9d5686d4406f03a68c4d534b7
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.3.1":
  version: 6.7.1
  resolution: "eslint-plugin-jsx-a11y@npm:6.7.1"
  dependencies:
    "@babel/runtime": ^7.20.7
    aria-query: ^5.1.3
    array-includes: ^3.1.6
    array.prototype.flatmap: ^1.3.1
    ast-types-flow: ^0.0.7
    axe-core: ^4.6.2
    axobject-query: ^3.1.1
    damerau-levenshtein: ^1.0.8
    emoji-regex: ^9.2.2
    has: ^1.0.3
    jsx-ast-utils: ^3.3.3
    language-tags: =1.0.5
    minimatch: ^3.1.2
    object.entries: ^1.1.6
    object.fromentries: ^2.0.6
    semver: ^6.3.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: f166dd5fe7257c7b891c6692e6a3ede6f237a14043ae3d97581daf318fc5833ddc6b4871aa34ab7656187430170500f6d806895747ea17ecdf8231a666c3c2fd
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^3.4.1":
  version: 3.4.1
  resolution: "eslint-plugin-prettier@npm:3.4.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
  peerDependencies:
    eslint: ">=5.0.0"
    prettier: ">=1.13.0"
  peerDependenciesMeta:
    eslint-config-prettier:
      optional: true
  checksum: fa6a89f0d7cba1cc87064352f5a4a68dc3739448dd279bec2bced1bfa3b704467e603d13b69dcec853f8fa30b286b8b715912898e9da776e1b016cf0ee48bd99
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.0.8":
  version: 4.6.0
  resolution: "eslint-plugin-react-hooks@npm:4.6.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 23001801f14c1d16bf0a837ca7970d9dd94e7b560384b41db378b49b6e32dc43d6e2790de1bd737a652a86f81a08d6a91f402525061b47719328f586a57e86c3
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-utils@npm:2.1.0"
  dependencies:
    eslint-visitor-keys: ^1.1.0
  checksum: 27500938f348da42100d9e6ad03ae29b3de19ba757ae1a7f4a087bdcf83ac60949bbb54286492ca61fac1f5f3ac8692dd21537ce6214240bf95ad0122f24d71d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^1.1.0, eslint-visitor-keys@npm:^1.3.0":
  version: 1.3.0
  resolution: "eslint-visitor-keys@npm:1.3.0"
  checksum: 37a19b712f42f4c9027e8ba98c2b06031c17e0c0a4c696cd429bd9ee04eb43889c446f2cd545e1ff51bef9593fcec94ecd2c2ef89129fcbbf3adadbef520376a
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.0.0, eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint@npm:^7.32.0":
  version: 7.32.0
  resolution: "eslint@npm:7.32.0"
  dependencies:
    "@babel/code-frame": 7.12.11
    "@eslint/eslintrc": ^0.4.3
    "@humanwhocodes/config-array": ^0.5.0
    ajv: ^6.10.0
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.0.1
    doctrine: ^3.0.0
    enquirer: ^2.3.5
    escape-string-regexp: ^4.0.0
    eslint-scope: ^5.1.1
    eslint-utils: ^2.1.0
    eslint-visitor-keys: ^2.0.0
    espree: ^7.3.1
    esquery: ^1.4.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    functional-red-black-tree: ^1.0.1
    glob-parent: ^5.1.2
    globals: ^13.6.0
    ignore: ^4.0.6
    import-fresh: ^3.0.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    js-yaml: ^3.13.1
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.0.4
    natural-compare: ^1.4.0
    optionator: ^0.9.1
    progress: ^2.0.0
    regexpp: ^3.1.0
    semver: ^7.2.1
    strip-ansi: ^6.0.0
    strip-json-comments: ^3.1.0
    table: ^6.0.9
    text-table: ^0.2.0
    v8-compile-cache: ^2.0.3
  bin:
    eslint: bin/eslint.js
  checksum: cc85af9985a3a11085c011f3d27abe8111006d34cc274291b3c4d7bea51a4e2ff6135780249becd919ba7f6d6d1ecc38a6b73dacb6a7be08d38453b344dc8d37
  languageName: node
  linkType: hard

"espree@npm:^7.3.0, espree@npm:^7.3.1":
  version: 7.3.1
  resolution: "espree@npm:7.3.1"
  dependencies:
    acorn: ^7.4.0
    acorn-jsx: ^5.3.1
    eslint-visitor-keys: ^1.3.0
  checksum: aa9b50dcce883449af2e23bc2b8d9abb77118f96f4cb313935d6b220f77137eaef7724a83c3f6243b96bc0e4ab14766198e60818caad99f9519ae5a336a39b45
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: aefb0d2596c230118656cd4ec7532d447333a410a48834d80ea648b1e7b5c9bc9ed8b5e33a89cb04e487b60d622f44cf5713bf4abed7c97343edefdc84a35900
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"execa@npm:^4.0.0":
  version: 4.1.0
  resolution: "execa@npm:4.1.0"
  dependencies:
    cross-spawn: ^7.0.0
    get-stream: ^5.0.0
    human-signals: ^1.1.1
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.0
    onetime: ^5.1.0
    signal-exit: ^3.0.2
    strip-final-newline: ^2.0.0
  checksum: e30d298934d9c52f90f3847704fd8224e849a081ab2b517bbc02f5f7732c24e56a21f14cb96a08256deffeb2d12b2b7cb7e2b014a12fb36f8d3357e06417ed55
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: abc407f07a875c3961e4781dfcb743b58d6c93de9ab263f4f8c9d23bb6da5f9b7764fc773f86b43dd88030444d5ab8abcb611cb680fba8ca075362b77114bba3
  languageName: node
  linkType: hard

"expect@npm:^27.5.1":
  version: 27.5.1
  resolution: "expect@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    jest-get-type: ^27.5.1
    jest-matcher-utils: ^27.5.1
    jest-message-util: ^27.5.1
  checksum: b2c66beb52de53ef1872165aace40224e722bca3c2274c54cfa74b6d617d55cf0ccdbf36783ccd64dbea501b280098ed33fd0b207d4f15bc03cd3c7a24364a6a
  languageName: node
  linkType: hard

"expect@npm:^29.0.0":
  version: 29.6.1
  resolution: "expect@npm:29.6.1"
  dependencies:
    "@jest/expect-utils": ^29.6.1
    "@types/node": "*"
    jest-get-type: ^29.4.3
    jest-matcher-utils: ^29.6.1
    jest-message-util: ^29.6.1
    jest-util: ^29.6.1
  checksum: 4e712e52c90f6c54e748fd2876be33c43ada6a59088ddf6a1acb08b18b3b97b3a672124684abe32599986d2f2a438d5afad148837ee06ea386d2a4bf0348de78
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"express@npm:^4.17.3":
  version: 4.18.2
  resolution: "express@npm:4.18.2"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.1
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.5.0
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.2.0
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.1
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.7
    proxy-addr: ~2.0.7
    qs: 6.11.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.18.0
    serve-static: 1.15.0
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 3c4b9b076879442f6b968fe53d85d9f1eeacbb4f4c41e5f16cc36d77ce39a2b0d81b3f250514982110d815b2f7173f5561367f9110fcc541f9371948e8c8b037
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.12":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: ">=0.5.1"
  checksum: d49a62caf027f871149fc2b3f3c7104dc6d62744277eb6f9f36e2d5714e847d846b9f7f0d0b7169b25a012e24a594cde11a93034b30732e4c683f20b8a5019fa
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: 2.1.1
  checksum: b15a124cef28916fe07b400eb87cbc73ca082c142abf7ca8e8de6af43eca79ca7bd13eb4d4d48240b3bd3136eaac40d16e42d6edf87a8e5d1dd8070626860c78
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"finalhandler@npm:1.2.0":
  version: 1.2.0
  resolution: "finalhandler@npm:1.2.0"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: 2.4.1
    parseurl: ~1.3.3
    statuses: 2.0.1
    unpipe: ~1.0.0
  checksum: 92effbfd32e22a7dff2994acedbd9bcc3aa646a3e919ea6a53238090e87097f8ef07cced90aa2cc421abdf993aefbdd5b00104d55c7c5479a8d00ed105b45716
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^3.0.2
    pkg-dir: ^4.1.0
  checksum: 1e61c2e64f5c0b1c535bd85939ae73b0e5773142713273818cc0b393ee3555fb0fd44e1a5b161b8b6c3e03e98c2fcc9c227d784850a13a90a8ab576869576817
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.0.4
  resolution: "flat-cache@npm:3.0.4"
  dependencies:
    flatted: ^3.1.0
    rimraf: ^3.0.2
  checksum: 4fdd10ecbcbf7d520f9040dd1340eb5dfe951e6f0ecf2252edeec03ee68d989ec8b9a20f4434270e71bcfd57800dc09b3344fca3966b2eb8f613072c7d9a2365
  languageName: node
  linkType: hard

"flatted@npm:^3.1.0":
  version: 3.2.7
  resolution: "flatted@npm:3.2.7"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0":
  version: 1.15.2
  resolution: "follow-redirects@npm:1.15.2"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: faa66059b66358ba65c234c2f2a37fcec029dc22775f35d9ad6abac56003268baf41e55f9ee645957b32c7d9f62baf1f0b906e68267276f54ec4b4c597c2b190
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 139d270bc82dc9e6f8bc045fe2aae4001dc2472157044fdfad376d0a3457f77857fa883c1c8b21b491c6caade9a926a4bed3d3d2e8d3c9202b151a4cbbd0bcd5
  languageName: node
  linkType: hard

"form-data@npm:^3.0.0":
  version: 3.0.1
  resolution: "form-data@npm:3.0.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    mime-types: ^2.1.12
  checksum: b019e8d35c8afc14a2bd8a7a92fa4f525a4726b6d5a9740e8d2623c30e308fbb58dc8469f90415a856698933c8479b01646a9dff33c87cc4e76d72aedbbf860d
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.2
  resolution: "fs-minipass@npm:3.0.2"
  dependencies:
    minipass: ^5.0.0
  checksum: e9cc0e1f2d01c6f6f62f567aee59530aba65c6c7b2ae88c5027bc34c711ebcfcfaefd0caf254afa6adfe7d1fba16bc2537508a6235196bac7276747d078aef0a
  languageName: node
  linkType: hard

"fs-monkey@npm:^1.0.4":
  version: 1.0.4
  resolution: "fs-monkey@npm:1.0.4"
  checksum: 8b254c982905c0b7e028eab22b410dc35a5c0019c1c860456f5f54ae6a61666e1cb8c6b700d6c88cc873694c00953c935847b9959cc4dcf274aacb8673c1e8bf
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: latest
  checksum: 97ade64e75091afee5265e6956cb72ba34db7819b4c3e94c431d4be2b19b8bb7a2d4116da417950c3425f17c8fe693d25e20212cac583ac1521ad066b77ae31f
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^2.3.2#~builtin<compat/fsevents>, fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#~builtin<compat/fsevents>::version=2.3.2&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.5":
  version: 1.1.5
  resolution: "function.prototype.name@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.0
    functions-have-names: ^1.2.2
  checksum: acd21d733a9b649c2c442f067567743214af5fa248dbeee69d8278ce7df3329ea5abac572be9f7470b4ec1cd4d8f1040e3c5caccf98ebf2bf861a0deab735c27
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: ca6c170f37640e2d94297da8bb4bf27a1d12bea3e00e6a3e007fd7aa32e37e000f5772acf941b4e4f3cf1c95c3752033d0c509af157ad8f526e7f00723b9eb9f
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.2, functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.1, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.0, get-intrinsic@npm:^1.2.1":
  version: 1.2.1
  resolution: "get-intrinsic@npm:1.2.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
  checksum: 5b61d88552c24b0cf6fa2d1b3bc5459d7306f699de060d76442cce49a4721f52b8c560a33ab392cf5575b7810277d54ded9d4d39a1ea61855619ebc005aa7e5f
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: bba0811116d11e56d702682ddef7c73ba3481f114590e705fc549f4d868972263896af313c57a25c076e3c0d567e11d919a64ba1b30c879be985fc9d44f96148
  languageName: node
  linkType: hard

"get-stream@npm:^5.0.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 9ceff8fe968f9270a37a1f73bf3f1f7bda69ca80f4f80850670e0e7b9444ff99323f7ac52f96567f8b5f5fbe7ac717a0d81d3407c7313e82810c6199446a5247
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.3.3
  resolution: "glob@npm:10.3.3"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^2.0.3
    minimatch: ^9.0.1
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
    path-scurry: ^1.10.1
  bin:
    glob: dist/cjs/src/bin.js
  checksum: 29190d3291f422da0cb40b77a72fc8d2c51a36524e99b8bf412548b7676a6627489528b57250429612b6eec2e6fe7826d328451d3e694a9d15e575389308ec53
  languageName: node
  linkType: hard

"glob@npm:^7.1.1, glob@npm:^7.1.2, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.6.0, globals@npm:^13.9.0":
  version: 13.20.0
  resolution: "globals@npm:13.20.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: ad1ecf914bd051325faad281d02ea2c0b1df5d01bd94d368dcc5513340eac41d14b3c61af325768e3c7f8d44576e72780ec0b6f2d366121f8eec6e03c3a3b97a
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.3":
  version: 1.0.3
  resolution: "globalthis@npm:1.0.3"
  dependencies:
    define-properties: ^1.1.3
  checksum: fbd7d760dc464c886d0196166d92e5ffb4c84d0730846d6621a39fbbc068aeeb9c8d1421ad330e94b7bca4bb4ea092f5f21f3d36077812af5d098b4dc006c998
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 68071f313062315cd9dce55710e9496873945f1dd425107007058fc1629f93002a7649fcc3e464281ce02c7e809a35f5925504ab8105d972cf649f1f47cb7d6c
  languageName: node
  linkType: hard

"harmony-reflect@npm:^1.4.6":
  version: 1.6.2
  resolution: "harmony-reflect@npm:1.6.2"
  checksum: 2e5bae414cd2bfae5476147f9935dc69ee9b9a413206994dcb94c5b3208d4555da3d4313aff6fd14bd9991c1e3ef69cdda5c8fac1eb1d7afc064925839339b8c
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1, has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-property-descriptors@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.1.1
  checksum: a6d3f0a266d0294d972e354782e872e2fe1b6495b321e6ef678c9b7a06a40408a6891817350c62e752adced73a94ac903c54734fee05bf65b1905ee1368194bb
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: febc5b5b531de8022806ad7407935e2135f1cc9e64636c3916c6842bd7995994ca3b29871ecd7954bd35f9e2986c17b3b227880484d22259e2f8e6ce63fd383e
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"history@npm:^4.9.0":
  version: 4.10.1
  resolution: "history@npm:4.10.1"
  dependencies:
    "@babel/runtime": ^7.1.2
    loose-envify: ^1.2.0
    resolve-pathname: ^3.0.0
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
    value-equal: ^1.0.1
  checksum: addd84bc4683929bae4400419b5af132ff4e4e9b311a0d4e224579ea8e184a6b80d7f72c55927e4fa117f69076a9e47ce082d8d0b422f1a9ddac7991490ca1d0
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.1.0":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: ^16.7.0
  checksum: b1538270429b13901ee586aa44f4cc3ecd8831c061d06cb8322e50ea17b3f5ce4d0e2e66394761e6c8e152cd8c34fb3b4b690116c6ce2bd45b18c746516cb9e8
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: ^2.0.1
    obuf: ^1.0.0
    readable-stream: ^2.0.1
    wbuf: ^1.1.0
  checksum: 2de144115197967ad6eeee33faf41096c6ba87078703c5cb011632dcfbffeb45784569e0cf02c317bd79c48375597c8ec88c30fff5bb0b023e8f654fb6e9c06e
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^2.0.1":
  version: 2.0.1
  resolution: "html-encoding-sniffer@npm:2.0.1"
  dependencies:
    whatwg-encoding: ^1.0.5
  checksum: bf30cce461015ed7e365736fcd6a3063c7bc016a91f74398ef6158886970a96333938f7c02417ab3c12aa82e3e53b40822145facccb9ddfbcdc15a879ae4d7ba
  languageName: node
  linkType: hard

"html-entities@npm:^2.3.2":
  version: 2.4.0
  resolution: "html-entities@npm:2.4.0"
  checksum: 25bea32642ce9ebd0eedc4d24381883ecb0335ccb8ac26379a0958b9b16652fdbaa725d70207ce54a51db24103436a698a8e454397d3ba8ad81460224751f1dc
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: d2df2da3ad40ca9ee3a39c5cc6475ef67c8f83c234475f24d8e9ce0dc80a2c82df8e1d6fa78ddd1e9022a586ea1bd247a615e80a5cd9273d90111ddda7d9e974
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^6.0.2":
  version: 6.1.0
  resolution: "html-minifier-terser@npm:6.1.0"
  dependencies:
    camel-case: ^4.1.2
    clean-css: ^5.2.2
    commander: ^8.3.0
    he: ^1.2.0
    param-case: ^3.0.4
    relateurl: ^0.2.7
    terser: ^5.10.0
  bin:
    html-minifier-terser: cli.js
  checksum: ac52c14006476f773204c198b64838477859dc2879490040efab8979c0207424da55d59df7348153f412efa45a0840a1ca3c757bf14767d23a15e3e389d37a93
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:^5.3.2":
  version: 5.5.3
  resolution: "html-webpack-plugin@npm:5.5.3"
  dependencies:
    "@types/html-minifier-terser": ^6.0.0
    html-minifier-terser: ^6.0.2
    lodash: ^4.17.21
    pretty-error: ^4.0.0
    tapable: ^2.0.0
  peerDependencies:
    webpack: ^5.20.0
  checksum: ccf685195739c372ad641bbd0c9100a847904f34eedc7aff3ece7856cd6c78fd3746d2d615af1bb71e5727993fe711b89e9b744f033ed3fde646540bf5d5e954
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 64d7d1ae3a6933eb0e9a94e6f27be4af45a53a96c3c34e84ff57113787105a89fff9d1c3df263ef63add823df019b0e8f52f7121e32393bb5ce9a713bf100b41
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.3
    setprototypeof: 1.1.0
    statuses: ">= 1.4.0 < 2"
  checksum: a9654ee027e3d5de305a56db1d1461f25709ac23267c6dc28cdab8323e3f96caa58a9a6a5e93ac15d7285cee0c2f019378c3ada9026e7fe19c872d695f27de7c
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.8
  resolution: "http-parser-js@npm:0.5.8"
  checksum: 6bbdf2429858e8cf13c62375b0bfb6dc3955ca0f32e58237488bc86cd2378f31d31785fd3ac4ce93f1c74e0189cf8823c91f5cb061696214fd368d2452dc871d
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^4.0.1":
  version: 4.0.1
  resolution: "http-proxy-agent@npm:4.0.1"
  dependencies:
    "@tootallnate/once": 1
    agent-base: 6
    debug: 4
  checksum: c6a5da5a1929416b6bbdf77b1aca13888013fe7eb9d59fc292e25d18e041bb154a8dfada58e223fc7b76b9b2d155a87e92e608235201f77d34aa258707963a82
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:^2.0.3":
  version: 2.0.6
  resolution: "http-proxy-middleware@npm:2.0.6"
  dependencies:
    "@types/http-proxy": ^1.17.8
    http-proxy: ^1.18.1
    is-glob: ^4.0.1
    is-plain-obj: ^3.0.0
    micromatch: ^4.0.2
  peerDependencies:
    "@types/express": ^4.17.13
  peerDependenciesMeta:
    "@types/express":
      optional: true
  checksum: 2ee85bc878afa6cbf34491e972ece0f5be0a3e5c98a60850cf40d2a9a5356e1fc57aab6cff33c1fc37691b0121c3a42602d2b1956c52577e87a5b77b62ae1c3a
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: ^4.0.0
    follow-redirects: ^1.0.0
    requires-port: ^1.0.0
  checksum: f5bd96bf83e0b1e4226633dbb51f8b056c3e6321917df402deacec31dd7fe433914fc7a2c1831cf7ae21e69c90b3a669b8f434723e9e8b71fd68afe30737b6a5
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"human-signals@npm:^1.1.1":
  version: 1.1.1
  resolution: "human-signals@npm:1.1.1"
  checksum: d587647c9e8ec24e02821b6be7de5a0fc37f591f6c4e319b3054b43fd4c35a70a94c46fc74d8c1a43c47fde157d23acd7421f375e1c1365b09a16835b8300205
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"husky@npm:^7.0.2":
  version: 7.0.4
  resolution: "husky@npm:7.0.4"
  bin:
    husky: lib/bin.js
  checksum: c6ec4af63da2c9522da8674a20ad9b48362cc92704896cc8a58c6a2a39d797feb2b806f93fbd83a6d653fbdceb2c3b6e0b602c6b2e8565206ffc2882ef7db9e9
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"identity-obj-proxy@npm:^3.0.0":
  version: 3.0.0
  resolution: "identity-obj-proxy@npm:3.0.0"
  dependencies:
    harmony-reflect: ^1.4.6
  checksum: 97559f8ea2aeaa1a880d279d8c49550dce01148321e00a2102cda5ddf9ce622fa1d7f3efc7bed63458af78889de888fdaebaf31c816312298bb3fdd0ef8aaf2c
  languageName: node
  linkType: hard

"ignore@npm:^4.0.6":
  version: 4.0.6
  resolution: "ignore@npm:4.0.6"
  checksum: 248f82e50a430906f9ee7f35e1158e3ec4c3971451dd9f99c9bc1548261b4db2b99709f60ac6c6cac9333494384176cc4cc9b07acbe42d52ac6a09cad734d800
  languageName: node
  linkType: hard

"ignore@npm:^5.1.4":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 3d4c309c6006e2621659311783eaea7ebcd41fe4ca1d78c91c473157ad6666a57a2df790fe0d07a12300d9aac2888204d7be8d59f9aaf665b1c7fcdb432517ef
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0, import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.1.0
  resolution: "import-local@npm:3.1.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: bfcdb63b5e3c0e245e347f3107564035b128a414c4da1172a20dc67db2504e05ede4ac2eee1252359f78b0bfd7b19ef180aec427c2fce6493ae782d73a04cddd
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 78cb8d7d850d20a5e9a7f3620db31483aa00ad5f722ce03a55b110e5a723539b3716a3b463e2b96ce3fe286f33afc7c131fa2f91407528ba80cea98a7545d4c0
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.4, internal-slot@npm:^1.0.5":
  version: 1.0.5
  resolution: "internal-slot@npm:1.0.5"
  dependencies:
    get-intrinsic: ^1.2.0
    has: ^1.0.3
    side-channel: ^1.0.4
  checksum: 97e84046bf9e7574d0956bd98d7162313ce7057883b6db6c5c7b5e5f05688864b0978ba07610c726d15d66544ffe4b1050107d93f8a39ebc59b15d8b429b497a
  languageName: node
  linkType: hard

"interpret@npm:^2.2.0":
  version: 2.2.0
  resolution: "interpret@npm:2.2.0"
  checksum: f51efef7cb8d02da16408ffa3504cd6053014c5aeb7bb8c223727e053e4235bf565e45d67028b0c8740d917c603807aa3c27d7bd2f21bf20b6417e2bb3e5fd6e
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: cfcfac6b873b701996d71ec82a7dd27ba92450afdb421e356f44044ed688df04567344c36cbacea7d01b1c39a4c732dc012570ebe9bebfb06f27314bca625349
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"ipaddr.js@npm:^2.0.1":
  version: 2.1.0
  resolution: "ipaddr.js@npm:2.1.0"
  checksum: 807a054f2bd720c4d97ee479d6c9e865c233bea21f139fb8dabd5a35c4226d2621c42e07b4ad94ff3f82add926a607d8d9d37c625ad0319f0e08f9f2bd1968e2
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 7f02700ec2171b691ef3e4d0e3e6c0ba408e8434368504bb593d0d7c891c0dbfda6d19d30808b904a6cb1929bca648c061ba438c39f296c2a8ca083229c49f27
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.1, is-array-buffer@npm:^3.0.2":
  version: 3.0.2
  resolution: "is-array-buffer@npm:3.0.2"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.0
    is-typed-array: ^1.1.10
  checksum: dcac9dda66ff17df9cabdc58214172bf41082f956eab30bb0d86bc0fab1e44b690fc8e1f855cf2481245caf4e8a5a006a982a71ddccec84032ed41f9d8da8c14
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.12.0":
  version: 2.12.1
  resolution: "is-core-module@npm:2.12.1"
  dependencies:
    has: ^1.0.3
  checksum: f04ea30533b5e62764e7b2e049d3157dc0abd95ef44275b32489ea2081176ac9746ffb1cdb107445cf1ff0e0dfcad522726ca27c27ece64dadf3795428b8e468
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1, is-date-object@npm:^1.0.5":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: a6ad5492cf9d1746f73b6744e0c43c0020510b59d56ddcb78a91cbc173f09b5e6beff53d75c9c5a29feb618bfef2bf458e025ecf3a57ad2268e2fb2569f56215
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-map@npm:^2.0.1, is-map@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-map@npm:2.0.2"
  checksum: ace3d0ecd667bbdefdb1852de601268f67f2db725624b1958f279316e13fecb8fa7df91fd60f690d7417b4ec180712f5a7ee967008e27c65cfd475cc84337728
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: f3232194c47a549da60c3d509c9a09be442507616b69454716692e37ae9f37c4dea264fb208ad0c9f3efd15a796a46b79df07c7e53c6227c32170608b809149a
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.7
  resolution: "is-number-object@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d1e8d01bb0a7134c74649c4e62da0c6118a0bfc6771ea3c560914d52a627873e6920dd0fd0ebc0e12ad2ff4687eac4c308f7e80320b973b2c8a2c8f97a7524f7
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-plain-obj@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-plain-obj@npm:3.0.0"
  checksum: a6ebdf8e12ab73f33530641972a72a4b8aed6df04f762070d823808303e4f76d87d5ea5bd76f96a7bbe83d93f04ac7764429c29413bd9049853a69cb630fb21c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: ced7bbbb6433a5b684af581872afe0e1767e2d1146b2207ca0068a648fb5cab9d898495d1ac0583524faaf24ca98176a7d9876363097c2d14fee6dd324f3a1ab
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-set@npm:^2.0.1, is-set@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-set@npm:2.0.2"
  checksum: b64343faf45e9387b97a6fd32be632ee7b269bd8183701f3b3f5b71a7cf00d04450ed8669d0bd08753e08b968beda96fca73a10fd0ff56a32603f64deba55a57
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-shared-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 9508929cf14fdc1afc9d61d723c6e8d34f5e117f0bffda4d97e7a5d88c3a8681f633a74f8e3ad1fe92d5113f9b921dc5ca44356492079612f9a247efbce7032a
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.10, is-typed-array@npm:^1.1.9":
  version: 1.1.12
  resolution: "is-typed-array@npm:1.1.12"
  dependencies:
    which-typed-array: ^1.1.11
  checksum: 4c89c4a3be07186caddadf92197b17fda663a9d259ea0d44a85f171558270d36059d1c386d34a12cba22dfade5aba497ce22778e866adc9406098c8fc4771796
  languageName: node
  linkType: hard

"is-typedarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.1":
  version: 2.0.1
  resolution: "is-weakmap@npm:2.0.1"
  checksum: 1222bb7e90c32bdb949226e66d26cb7bce12e1e28e3e1b40bfa6b390ba3e08192a8664a703dff2a00a84825f4e022f9cd58c4599ff9981ab72b1d69479f4f7f6
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-weakset@npm:2.0.2"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 5d8698d1fa599a0635d7ca85be9c26d547b317ed8fd83fc75f03efbe75d50001b5eececb1e9971de85fcde84f69ae6f8346bc92d20d55d46201d328e4c74a367
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 49191f1425681df4a18c2f0f93db3adb85573bcdd6a4482539d98eac9e705d8961317b01175627e860516a2fc45f8f9302db26e5a380a97a520e272e2a40a8d4
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.0
  resolution: "istanbul-lib-coverage@npm:3.2.0"
  checksum: a2a545033b9d56da04a8571ed05c8120bf10e9bce01cf8633a3a2b0d1d83dff4ac4fe78d6d5673c27fc29b7f21a41d75f83a36be09f82a61c367b56aa73c1ff9
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4, istanbul-lib-instrument@npm:^5.1.0":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": ^7.12.3
    "@babel/parser": ^7.14.7
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-coverage: ^3.2.0
    semver: ^6.3.0
  checksum: bf16f1803ba5e51b28bbd49ed955a736488381e09375d830e42ddeb403855b2006f850711d95ad726f2ba3f1ae8e7366de7e51d2b9ac67dc4d80191ef7ddf272
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.0
  resolution: "istanbul-lib-report@npm:3.0.0"
  dependencies:
    istanbul-lib-coverage: ^3.0.0
    make-dir: ^3.0.0
    supports-color: ^7.1.0
  checksum: 3f29eb3f53c59b987386e07fe772d24c7f58c6897f34c9d7a296f4000de7ae3de9eb95c3de3df91dc65b134c84dee35c54eee572a56243e8907c48064e34ff1b
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: ^4.1.1
    istanbul-lib-coverage: ^3.0.0
    source-map: ^0.6.1
  checksum: 21ad3df45db4b81852b662b8d4161f6446cd250c1ddc70ef96a585e2e85c26ed7cd9c2a396a71533cfb981d1a645508bc9618cae431e55d01a0628e7dec62ef2
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.5
  resolution: "istanbul-reports@npm:3.1.5"
  dependencies:
    html-escaper: ^2.0.0
    istanbul-lib-report: ^3.0.0
  checksum: 7867228f83ed39477b188ea07e7ccb9b4f5320b6f73d1db93a0981b7414fa4ef72d3f80c4692c442f90fc250d9406e71d8d7ab65bb615cb334e6292b73192b89
  languageName: node
  linkType: hard

"jackspeak@npm:^2.0.3":
  version: 2.2.1
  resolution: "jackspeak@npm:2.2.1"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: e29291c0d0f280a063fa18fbd1e891ab8c2d7519fd34052c0ebde38538a15c603140d60c2c7f432375ff7ee4c5f1c10daa8b2ae19a97c3d4affe308c8360c1df
  languageName: node
  linkType: hard

"jest-changed-files@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-changed-files@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    execa: ^5.0.0
    throat: ^6.0.1
  checksum: 95e9dc74c3ca688ef85cfeab270f43f8902721a6c8ade6ac2459459a77890c85977f537d6fb809056deaa6d9c3f075fa7d2699ff5f3bf7d3fda17c3760b79b15
  languageName: node
  linkType: hard

"jest-circus@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-circus@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    dedent: ^0.7.0
    expect: ^27.5.1
    is-generator-fn: ^2.0.0
    jest-each: ^27.5.1
    jest-matcher-utils: ^27.5.1
    jest-message-util: ^27.5.1
    jest-runtime: ^27.5.1
    jest-snapshot: ^27.5.1
    jest-util: ^27.5.1
    pretty-format: ^27.5.1
    slash: ^3.0.0
    stack-utils: ^2.0.3
    throat: ^6.0.1
  checksum: 6192dccbccb3a6acfa361cbb97bdbabe94864ccf3d885932cfd41f19534329d40698078cf9be1489415e8234255d6ea9f9aff5396b79ad842a6fca6e6fc08fd0
  languageName: node
  linkType: hard

"jest-cli@npm:^27.0.6, jest-cli@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-cli@npm:27.5.1"
  dependencies:
    "@jest/core": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/types": ^27.5.1
    chalk: ^4.0.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    import-local: ^3.0.2
    jest-config: ^27.5.1
    jest-util: ^27.5.1
    jest-validate: ^27.5.1
    prompts: ^2.0.1
    yargs: ^16.2.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 6c0a69fb48e500241409e09ff743ed72bc6578d7769e2c994724e7ef1e5587f6c1f85dc429e93b98ae38a365222993ee70f0acc2199358992120900984f349e5
  languageName: node
  linkType: hard

"jest-config@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-config@npm:27.5.1"
  dependencies:
    "@babel/core": ^7.8.0
    "@jest/test-sequencer": ^27.5.1
    "@jest/types": ^27.5.1
    babel-jest: ^27.5.1
    chalk: ^4.0.0
    ci-info: ^3.2.0
    deepmerge: ^4.2.2
    glob: ^7.1.1
    graceful-fs: ^4.2.9
    jest-circus: ^27.5.1
    jest-environment-jsdom: ^27.5.1
    jest-environment-node: ^27.5.1
    jest-get-type: ^27.5.1
    jest-jasmine2: ^27.5.1
    jest-regex-util: ^27.5.1
    jest-resolve: ^27.5.1
    jest-runner: ^27.5.1
    jest-util: ^27.5.1
    jest-validate: ^27.5.1
    micromatch: ^4.0.4
    parse-json: ^5.2.0
    pretty-format: ^27.5.1
    slash: ^3.0.0
    strip-json-comments: ^3.1.1
  peerDependencies:
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    ts-node:
      optional: true
  checksum: 1188fd46c0ed78cbe3175eb9ad6712ccf74a74be33d9f0d748e147c107f0889f8b701fbff1567f31836ae18597dacdc43d6a8fc30dd34ade6c9229cc6c7cb82d
  languageName: node
  linkType: hard

"jest-diff@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-diff@npm:27.5.1"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^27.5.1
    jest-get-type: ^27.5.1
    pretty-format: ^27.5.1
  checksum: 8be27c1e1ee57b2bb2bef9c0b233c19621b4c43d53a3c26e2c00a4e805eb4ea11fe1694a06a9fb0e80ffdcfdc0d2b1cb0b85920b3f5c892327ecd1e7bd96b865
  languageName: node
  linkType: hard

"jest-diff@npm:^29.6.1":
  version: 29.6.1
  resolution: "jest-diff@npm:29.6.1"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^29.4.3
    jest-get-type: ^29.4.3
    pretty-format: ^29.6.1
  checksum: c6350178ca27d92c7fd879790fb2525470c1ff1c5d29b1834a240fecd26c6904fb470ebddb98dc96dd85389c56c3b50e6965a1f5203e9236d213886ed9806219
  languageName: node
  linkType: hard

"jest-docblock@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-docblock@npm:27.5.1"
  dependencies:
    detect-newline: ^3.0.0
  checksum: c0fed6d55b229d8bffdd8d03f121dd1a3be77c88f50552d374f9e1ea3bde57bf6bea017a0add04628d98abcb1bfb48b456438eeca8a74ef0053f4dae3b95d29c
  languageName: node
  linkType: hard

"jest-each@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-each@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    chalk: ^4.0.0
    jest-get-type: ^27.5.1
    jest-util: ^27.5.1
    pretty-format: ^27.5.1
  checksum: b5a6d8730fd938982569c9e0b42bdf3c242f97b957ed8155a6473b5f7b540970f8685524e7f53963dc1805319f4b6602abfc56605590ca19d55bd7a87e467e63
  languageName: node
  linkType: hard

"jest-environment-jsdom@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-environment-jsdom@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/fake-timers": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    jest-mock: ^27.5.1
    jest-util: ^27.5.1
    jsdom: ^16.6.0
  checksum: bc104aef7d7530d0740402aa84ac812138b6d1e51fe58adecce679f82b99340ddab73e5ec68fa079f33f50c9ddec9728fc9f0ddcca2ad6f0b351eed2762cc555
  languageName: node
  linkType: hard

"jest-environment-node@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-environment-node@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/fake-timers": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    jest-mock: ^27.5.1
    jest-util: ^27.5.1
  checksum: 0f988330c4f3eec092e3fb37ea753b0c6f702e83cd8f4d770af9c2bf964a70bc45fbd34ec6fdb6d71ce98a778d9f54afd673e63f222e4667fff289e8069dba39
  languageName: node
  linkType: hard

"jest-get-type@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-get-type@npm:27.5.1"
  checksum: 63064ab70195c21007d897c1157bf88ff94a790824a10f8c890392e7d17eda9c3900513cb291ca1c8d5722cad79169764e9a1279f7c8a9c4cd6e9109ff04bbc0
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-get-type@npm:29.4.3"
  checksum: 6ac7f2dde1c65e292e4355b6c63b3a4897d7e92cb4c8afcf6d397f2682f8080e094c8b0b68205a74d269882ec06bf696a9de6cd3e1b7333531e5ed7b112605ce
  languageName: node
  linkType: hard

"jest-haste-map@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-haste-map@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    "@types/graceful-fs": ^4.1.2
    "@types/node": "*"
    anymatch: ^3.0.3
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.9
    jest-regex-util: ^27.5.1
    jest-serializer: ^27.5.1
    jest-util: ^27.5.1
    jest-worker: ^27.5.1
    micromatch: ^4.0.4
    walker: ^1.0.7
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: e092a1412829a9254b4725531ee72926de530f77fda7b0d9ea18008fb7623c16f72e772d8e93be71cac9e591b2c6843a669610887dd2c89bd9eb528856e3ab47
  languageName: node
  linkType: hard

"jest-jasmine2@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-jasmine2@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/source-map": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    expect: ^27.5.1
    is-generator-fn: ^2.0.0
    jest-each: ^27.5.1
    jest-matcher-utils: ^27.5.1
    jest-message-util: ^27.5.1
    jest-runtime: ^27.5.1
    jest-snapshot: ^27.5.1
    jest-util: ^27.5.1
    pretty-format: ^27.5.1
    throat: ^6.0.1
  checksum: b716adf253ceb73db661936153394ab90d7f3a8ba56d6189b7cd4df8e4e2a4153b4e63ebb5d36e29ceb0f4c211d5a6f36ab7048c6abbd881c8646567e2ab8e6d
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-leak-detector@npm:27.5.1"
  dependencies:
    jest-get-type: ^27.5.1
    pretty-format: ^27.5.1
  checksum: 5c9689060960567ddaf16c570d87afa760a461885765d2c71ef4f4857bbc3af1482c34e3cce88e50beefde1bf35e33530b020480752057a7e3dbb1ca0bae359f
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-matcher-utils@npm:27.5.1"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^27.5.1
    jest-get-type: ^27.5.1
    pretty-format: ^27.5.1
  checksum: bb2135fc48889ff3fe73888f6cc7168ddab9de28b51b3148f820c89fdfd2effdcad005f18be67d0b9be80eda208ad47290f62f03d0a33f848db2dd0273c8217a
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.6.1":
  version: 29.6.1
  resolution: "jest-matcher-utils@npm:29.6.1"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^29.6.1
    jest-get-type: ^29.4.3
    pretty-format: ^29.6.1
  checksum: d2efa6aed6e4820758b732b9fefd315c7fa4508ee690da656e1c5ac4c1a0f4cee5b04c9719ee1fda9aeb883b4209186c145089ced521e715b9fa70afdfa4a9c6
  languageName: node
  linkType: hard

"jest-message-util@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-message-util@npm:27.5.1"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^27.5.1
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^27.5.1
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: eb6d637d1411c71646de578c49826b6da8e33dd293e501967011de9d1916d53d845afbfb52a5b661ff1c495be7c13f751c48c7f30781fd94fbd64842e8195796
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.6.1":
  version: 29.6.1
  resolution: "jest-message-util@npm:29.6.1"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^29.6.1
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^29.6.1
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 3e7cb2ff087fe72255292e151d24e4fbb4cd6134885c0a67a4b302f233fe4110bf7580b176f427f05ad7550eb878ed94237209785d09d659a7d171ffa59c068f
  languageName: node
  linkType: hard

"jest-mock@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-mock@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    "@types/node": "*"
  checksum: f5b5904bb1741b4a1687a5f492535b7b1758dc26534c72a5423305f8711292e96a601dec966df81bb313269fb52d47227e29f9c2e08324d79529172f67311be0
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: db1a8ab2cb97ca19c01b1cfa9a9c8c69a143fde833c14df1fab0766f411b1148ff0df878adea09007ac6a2085ec116ba9a996a6ad104b1e58c20adbf88eed9b2
  languageName: node
  linkType: hard

"jest-regex-util@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-regex-util@npm:27.5.1"
  checksum: d45ca7a9543616a34f7f3079337439cf07566e677a096472baa2810e274b9808b76767c97b0a4029b8a5b82b9d256dee28ef9ad4138b2b9e5933f6fac106c418
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-resolve-dependencies@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    jest-regex-util: ^27.5.1
    jest-snapshot: ^27.5.1
  checksum: c67af97afad1da88f5530317c732bbd1262d1225f6cd7f4e4740a5db48f90ab0bd8564738ac70d1a43934894f9aef62205c1b8f8ee89e5c7a737e6a121ee4c25
  languageName: node
  linkType: hard

"jest-resolve@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-resolve@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^27.5.1
    jest-pnp-resolver: ^1.2.2
    jest-util: ^27.5.1
    jest-validate: ^27.5.1
    resolve: ^1.20.0
    resolve.exports: ^1.1.0
    slash: ^3.0.0
  checksum: 735830e7265b20a348029738680bb2f6e37f80ecea86cda869a4c318ba3a45d39c7a3a873a22f7f746d86258c50ead6e7f501de043e201c095d7ba628a1c440f
  languageName: node
  linkType: hard

"jest-runner@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-runner@npm:27.5.1"
  dependencies:
    "@jest/console": ^27.5.1
    "@jest/environment": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    emittery: ^0.8.1
    graceful-fs: ^4.2.9
    jest-docblock: ^27.5.1
    jest-environment-jsdom: ^27.5.1
    jest-environment-node: ^27.5.1
    jest-haste-map: ^27.5.1
    jest-leak-detector: ^27.5.1
    jest-message-util: ^27.5.1
    jest-resolve: ^27.5.1
    jest-runtime: ^27.5.1
    jest-util: ^27.5.1
    jest-worker: ^27.5.1
    source-map-support: ^0.5.6
    throat: ^6.0.1
  checksum: 5bbe6cf847dd322b3332ec9d6977b54f91bd5f72ff620bc1a0192f0f129deda8aa7ca74c98922187a7aa87d8e0ce4f6c50e99a7ccb2a310bf4d94be2e0c3ce8e
  languageName: node
  linkType: hard

"jest-runtime@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-runtime@npm:27.5.1"
  dependencies:
    "@jest/environment": ^27.5.1
    "@jest/fake-timers": ^27.5.1
    "@jest/globals": ^27.5.1
    "@jest/source-map": ^27.5.1
    "@jest/test-result": ^27.5.1
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    chalk: ^4.0.0
    cjs-module-lexer: ^1.0.0
    collect-v8-coverage: ^1.0.0
    execa: ^5.0.0
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^27.5.1
    jest-message-util: ^27.5.1
    jest-mock: ^27.5.1
    jest-regex-util: ^27.5.1
    jest-resolve: ^27.5.1
    jest-snapshot: ^27.5.1
    jest-util: ^27.5.1
    slash: ^3.0.0
    strip-bom: ^4.0.0
  checksum: 929e3df0c53dab43f831f2af4e2996b22aa8cb2d6d483919d6b0426cbc100098fd5b777b998c6568b77f8c4d860b2e83127514292ff61416064f5ef926492386
  languageName: node
  linkType: hard

"jest-serializer@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-serializer@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    graceful-fs: ^4.2.9
  checksum: 803e03a552278610edc6753c0dd9fa5bb5cd3ca47414a7b2918106efb62b79fd5e9ae785d0a21f12a299fa599fea8acc1fa6dd41283328cee43962cf7df9bb44
  languageName: node
  linkType: hard

"jest-snapshot@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-snapshot@npm:27.5.1"
  dependencies:
    "@babel/core": ^7.7.2
    "@babel/generator": ^7.7.2
    "@babel/plugin-syntax-typescript": ^7.7.2
    "@babel/traverse": ^7.7.2
    "@babel/types": ^7.0.0
    "@jest/transform": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/babel__traverse": ^7.0.4
    "@types/prettier": ^2.1.5
    babel-preset-current-node-syntax: ^1.0.0
    chalk: ^4.0.0
    expect: ^27.5.1
    graceful-fs: ^4.2.9
    jest-diff: ^27.5.1
    jest-get-type: ^27.5.1
    jest-haste-map: ^27.5.1
    jest-matcher-utils: ^27.5.1
    jest-message-util: ^27.5.1
    jest-util: ^27.5.1
    natural-compare: ^1.4.0
    pretty-format: ^27.5.1
    semver: ^7.3.2
  checksum: a5cfadf0d21cd76063925d1434bc076443ed6d87847d0e248f0b245f11db3d98ff13e45cc03b15404027dabecd712d925f47b6eae4f64986f688640a7d362514
  languageName: node
  linkType: hard

"jest-util@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-util@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: ac8d122f6daf7a035dcea156641fd3701aeba245417c40836a77e35b3341b9c02ddc5d904cfcd4ddbaa00ab854da76d3b911870cafdcdbaff90ea471de26c7d7
  languageName: node
  linkType: hard

"jest-util@npm:^29.6.1":
  version: 29.6.1
  resolution: "jest-util@npm:29.6.1"
  dependencies:
    "@jest/types": ^29.6.1
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: fc553556c1350c443449cadaba5fb9d604628e8b5ceb6ceaf4e7e08975b24277d0a14bf2e0f956024e03c23e556fcb074659423422a06fbedf2ab52978697ac7
  languageName: node
  linkType: hard

"jest-validate@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-validate@npm:27.5.1"
  dependencies:
    "@jest/types": ^27.5.1
    camelcase: ^6.2.0
    chalk: ^4.0.0
    jest-get-type: ^27.5.1
    leven: ^3.1.0
    pretty-format: ^27.5.1
  checksum: 82e870f8ee7e4fb949652711b1567f05ae31c54be346b0899e8353e5c20fad7692b511905b37966945e90af8dc0383eb41a74f3ffefb16140ea4f9164d841412
  languageName: node
  linkType: hard

"jest-watcher@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-watcher@npm:27.5.1"
  dependencies:
    "@jest/test-result": ^27.5.1
    "@jest/types": ^27.5.1
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    jest-util: ^27.5.1
    string-length: ^4.0.1
  checksum: 191c4e9c278c0902ade1a8a80883ac244963ba3e6e78607a3d5f729ccca9c6e71fb3b316f87883658132641c5d818aa84202585c76752e03c539e6cbecb820bd
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5, jest-worker@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"jest@npm:^27.0.6":
  version: 27.5.1
  resolution: "jest@npm:27.5.1"
  dependencies:
    "@jest/core": ^27.5.1
    import-local: ^3.0.2
    jest-cli: ^27.5.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 96f1d69042b3c6dfc695f2a4e4b0db38af6fb78582ad1a02beaa57cfcd77cbd31567d7d865c1c85709b7c3e176eefa3b2035ffecd646005f15d8ef528eccf205
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsdom@npm:^16.6.0":
  version: 16.7.0
  resolution: "jsdom@npm:16.7.0"
  dependencies:
    abab: ^2.0.5
    acorn: ^8.2.4
    acorn-globals: ^6.0.0
    cssom: ^0.4.4
    cssstyle: ^2.3.0
    data-urls: ^2.0.0
    decimal.js: ^10.2.1
    domexception: ^2.0.1
    escodegen: ^2.0.0
    form-data: ^3.0.0
    html-encoding-sniffer: ^2.0.1
    http-proxy-agent: ^4.0.1
    https-proxy-agent: ^5.0.0
    is-potential-custom-element-name: ^1.0.1
    nwsapi: ^2.2.0
    parse5: 6.0.1
    saxes: ^5.0.1
    symbol-tree: ^3.2.4
    tough-cookie: ^4.0.0
    w3c-hr-time: ^1.0.2
    w3c-xmlserializer: ^2.0.0
    webidl-conversions: ^6.1.0
    whatwg-encoding: ^1.0.5
    whatwg-mimetype: ^2.3.0
    whatwg-url: ^8.5.0
    ws: ^7.4.6
    xml-name-validator: ^3.0.0
  peerDependencies:
    canvas: ^2.5.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 454b83371857000763ed31130a049acd1b113e3b927e6dcd75c67ddc30cdd242d7ebcac5c2294b7a1a6428155cb1398709c573b3c6d809218692ea68edd93370
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.2":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^3.3.3":
  version: 3.3.4
  resolution: "jsx-ast-utils@npm:3.3.4"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: a6a00d324e38f0d47e04f973d79670248a663422a4dccdc02efd6f1caf1c00042fb0aafcff1023707c85dea6f013d435b90db67c1c6841bf345628f0a720d8b3
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"language-subtag-registry@npm:~0.3.2":
  version: 0.3.22
  resolution: "language-subtag-registry@npm:0.3.22"
  checksum: 8ab70a7e0e055fe977ac16ea4c261faec7205ac43db5e806f72e5b59606939a3b972c4bd1e10e323b35d6ffa97c3e1c4c99f6553069dad2dfdd22020fa3eb56a
  languageName: node
  linkType: hard

"language-tags@npm:=1.0.5":
  version: 1.0.5
  resolution: "language-tags@npm:1.0.5"
  dependencies:
    language-subtag-registry: ~0.3.2
  checksum: c81b5d8b9f5f9cfd06ee71ada6ddfe1cf83044dd5eeefcd1e420ad491944da8957688db4a0a9bc562df4afdc2783425cbbdfd152c01d93179cf86888903123cf
  languageName: node
  linkType: hard

"launch-editor@npm:^2.6.0":
  version: 2.6.0
  resolution: "launch-editor@npm:2.6.0"
  dependencies:
    picocolors: ^1.0.0
    shell-quote: ^1.7.3
  checksum: 48e4230643e8fdb5c14c11314706d58d9f3fbafe2606be3d6e37da1918ad8bfe39dd87875c726a1b59b9f4da99d87ec3e36d4c528464f0b820f9e91e5cb1c02d
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^2.1.2
  checksum: a5281f5fff1eaa310ad5e1164095689443630f3411e927f95031ab4fb83b4a98f388185bb1fe949e8ab8d4247004336a625e9255c22122b815bb9a4c5d8fc3b7
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: b463d8a382cfb5f0e71c504dcb6f807a7bd379ff1ea216669aa42c52fc28c54e404bfbd96791aa09e6df0de2c1d7b8f1b7f4b1a61f324d38fe98bc535aeee4f5
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:^4.7.0":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.2.0, loose-envify@npm:^1.3.1, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.0.0
  resolution: "lru-cache@npm:10.0.0"
  checksum: 18f101675fe283bc09cda0ef1e3cc83781aeb8373b439f086f758d1d91b28730950db785999cd060d3c825a8571c03073e8c14512b6655af2188d623031baf50
  languageName: node
  linkType: hard

"lz-string@npm:^1.5.0":
  version: 1.5.0
  resolution: "lz-string@npm:1.5.0"
  bin:
    lz-string: bin/bin.js
  checksum: 1ee98b4580246fd90dd54da6e346fb1caefcf05f677c686d9af237a157fdea3fd7c83a4bc58f858cd5b10a34d27afe0fdcbd0505a47e0590726a873dc8b8f65d
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.0, make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^11.0.3":
  version: 11.1.1
  resolution: "make-fetch-happen@npm:11.1.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^17.0.0
    http-cache-semantics: ^4.1.1
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^5.0.0
    minipass-fetch: ^3.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^10.0.0
  checksum: 7268bf274a0f6dcf0343829489a4506603ff34bd0649c12058753900b0eb29191dce5dba12680719a5d0a983d3e57810f594a12f3c18494e93a1fbc6348a4540
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b38a025a12c8146d6eeea5a7f2bf27d51d8ad6064da8ca9405fcf7bf9b54acd43e3b30ddd7abb9b1bfa4ddb266019133313482570ddb207de568f71ecfcf6060
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"memfs@npm:^3.4.3":
  version: 3.5.3
  resolution: "memfs@npm:3.5.3"
  dependencies:
    fs-monkey: ^1.0.4
  checksum: 18dfdeacad7c8047b976a6ccd58bc98ba76e122ad3ca0e50a21837fe2075fc0d9aafc58ab9cf2576c2b6889da1dd2503083f2364191b695273f40969db2ecc44
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 5abc259d2ae25bb06d19ce2b94a21632583c74e2a9109ee1ba7fd147aa7362b380d971e0251069f8b3eb7d48c21ac839e21fa177b335e82c76ec172e30c31a26
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:^2.1.31, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 253487976bf485b612f16bf57463520a14f512662e592e95c571afdab1442a6a6864b6c88f248ce6fc4ff0b6de04ac7aa6c8bb51e868e99d1d65eb0658a708b5
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.3
  resolution: "minipass-fetch@npm:3.0.3"
  dependencies:
    encoding: ^0.1.13
    minipass: ^5.0.0
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: af5ab2552a16fcf505d35fd7ffb84b57f4a0eeb269e6e1d9a2a75824dda48b36e527083250b7cca4a4def21d9544e2ade441e4730e233c0bc2133f6abda31e18
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0":
  version: 7.0.2
  resolution: "minipass@npm:7.0.2"
  checksum: 46776de732eb7cef2c7404a15fb28c41f5c54a22be50d47b03c605bf21f5c18d61a173c0a20b49a97e7a65f78d887245066410642551e45fffe04e9ac9e325bc
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mri@npm:^1.1.5":
  version: 1.2.0
  resolution: "mri@npm:1.2.0"
  checksum: 83f515abbcff60150873e424894a2f65d68037e5a7fcde8a9e2b285ee9c13ac581b63cfc1e6826c4732de3aeb84902f7c1e16b7aff46cd3f897a0f757a894e85
  languageName: node
  linkType: hard

"mrmime@npm:^1.0.0":
  version: 1.0.1
  resolution: "mrmime@npm:1.0.1"
  checksum: cc979da44bbbffebaa8eaf7a45117e851f2d4cb46a3ada6ceb78130466a04c15a0de9a9ce1c8b8ba6f6e1b8618866b1352992bf1757d241c0ddca558b9f28a77
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multicast-dns@npm:^7.2.5":
  version: 7.2.5
  resolution: "multicast-dns@npm:7.2.5"
  dependencies:
    dns-packet: ^5.2.2
    thunky: ^1.0.2
  bin:
    multicast-dns: cli.js
  checksum: 00b8a57df152d4cd0297946320a94b7c3cdf75a46a2247f32f958a8927dea42958177f9b7fdae69fab2e4e033fb3416881af1f5e9055a3e1542888767139e2fb
  languageName: node
  linkType: hard

"multimatch@npm:^4.0.0":
  version: 4.0.0
  resolution: "multimatch@npm:4.0.0"
  dependencies:
    "@types/minimatch": ^3.0.3
    array-differ: ^3.0.0
    array-union: ^2.1.0
    arrify: ^2.0.1
    minimatch: ^3.0.4
  checksum: bdb6a98dad4e919d9a1a2a0db872f44fa2337315f2fd5827d91ae005cf22f4425782bdfa97c10b80d567f0cb3c226c31f4e85f8f6a4a4be4facf9af0de1bb0c2
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.6
  resolution: "nanoid@npm:3.3.6"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 7d0eda657002738aa5206107bd0580aead6c95c460ef1bdd0b1a87a9c7ae6277ac2e9b945306aaa5b32c6dcb7feaf462d0f552e7f8b5718abfc6ead5c94a71b3
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-forge@npm:^1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 08fb072d3d670599c89a1704b3e9c649ff1b998256737f0e06fbd1a5bf41cae4457ccaee32d95052d80bbafd9ffe01284e078c8071f0267dc9744e51c5ed42a9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 9.4.0
  resolution: "node-gyp@npm:9.4.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^11.0.3
    nopt: ^6.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 78b404e2e0639d64e145845f7f5a3cb20c0520cdaf6dda2f6e025e9b644077202ea7de1232396ba5bde3fee84cdc79604feebe6ba3ec84d464c85d407bb5da99
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: d0b30b1ee6d961851c60d5eaa745d30b5c95d94bc0e74b81e5292f7c42a49e3af87f1eb9e89f59456f80645d679202537de751b7d72e9e40ceea40c5e449057e
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.12":
  version: 2.0.13
  resolution: "node-releases@npm:2.0.13"
  checksum: 17ec8f315dba62710cae71a8dad3cd0288ba943d2ece43504b3b1aa8625bf138637798ab470b1d9035b0545996f63000a8a926e0f6d35d0996424f8b6d36dda3
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.0, npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.0":
  version: 2.2.7
  resolution: "nwsapi@npm:2.2.7"
  checksum: cab25f7983acec7e23490fec3ef7be608041b460504229770e3bfcf9977c41d6fe58f518994d3bd9aa3a101f501089a3d4a63536f4ff8ae4b8c4ca23bdbfda4e
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.12.3, object-inspect@npm:^1.9.0":
  version: 1.12.3
  resolution: "object-inspect@npm:1.12.3"
  checksum: dabfd824d97a5f407e6d5d24810d888859f6be394d8b733a77442b277e0808860555176719c5905e765e3743a7cada6b8b0a3b85e5331c530fd418cc8ae991db
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.5
  resolution: "object-is@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 989b18c4cba258a6b74dc1d74a41805c1a1425bce29f6cabb50dcb1a6a651ea9104a1b07046739a49a5bb1bc49727bcb00efd5c55f932f6ea04ec8927a7901fe
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4":
  version: 4.1.4
  resolution: "object.assign@npm:4.1.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: 76cab513a5999acbfe0ff355f15a6a125e71805fcf53de4e9d4e082e1989bdb81d1e329291e1e4e0ae7719f0e4ef80e88fb2d367ae60500d79d25a6224ac8864
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.6":
  version: 1.1.6
  resolution: "object.entries@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 0f8c47517e6a9a980241eafe3b73de11e59511883173c2b93d67424a008e47e11b77c80e431ad1d8a806f6108b225a1cab9223e53e555776c612a24297117d28
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.6":
  version: 2.0.6
  resolution: "object.fromentries@npm:2.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 453c6d694180c0c30df451b60eaf27a5b9bca3fb43c37908fd2b78af895803dc631242bcf05582173afa40d8d0e9c96e16e8874b39471aa53f3ac1f98a085d85
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6":
  version: 1.1.6
  resolution: "object.values@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: f6fff9fd817c24cfd8107f50fb33061d81cd11bacc4e3dbb3852e9ff7692fde4dbce823d4333ea27cd9637ef1b6690df5fbb61f1ed314fa2959598dc3ae23d8e
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 41a2ba310e7b6f6c3b905af82c275bf8854896e2e4c5752966d64cbcd2f599cfffd5932006bcf3b8b419dfdacebb3a3912d5d94e10f1d0acab59876c8757f27f
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"open@npm:^8.0.9":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: ^2.0.0
    is-docker: ^2.1.1
    is-wsl: ^2.2.0
  checksum: 6388bfff21b40cb9bd8f913f9130d107f2ed4724ea81a8fd29798ee322b361ca31fa2cdfb491a5c31e43a3996cfe9566741238c7a741ada8d7af1cb78d85cf26
  languageName: node
  linkType: hard

"opener@npm:^1.5.2":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"optionator@npm:^0.9.1":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": ^1.2.3
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
  checksum: 09281999441f2fe9c33a5eeab76700795365a061563d66b098923eb719251a42bdbe432790d35064d0816ead9296dbeb1ad51a733edf4167c96bd5d0882e428a
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-retry@npm:^4.5.0":
  version: 4.6.2
  resolution: "p-retry@npm:4.6.2"
  dependencies:
    "@types/retry": 0.12.0
    retry: ^0.13.1
  checksum: 45c270bfddaffb4a895cea16cb760dcc72bdecb6cb45fef1971fa6ea2e91ddeafddefe01e444ac73e33b1b3d5d29fb0dd18a7effb294262437221ddc03ce0f2e
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5@npm:6.0.1":
  version: 6.0.1
  resolution: "parse5@npm:6.0.1"
  checksum: 7d569a176c5460897f7c8f3377eff640d54132b9be51ae8a8fa4979af940830b2b0c296ce75e5bd8f4041520aadde13170dbdec44889975f906098ea0002f4bd
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: ^9.1.1 || ^10.0.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: e2557cff3a8fb8bc07afdd6ab163a92587884f9969b05bbbaf6fe7379348bfb09af9ed292af12ed32398b15fb443e81692047b786d1eeb6d898a51eb17ed7d90
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 69a14ea24db543e8b0f4353305c5eac6907917031340e5a8b37df688e52accd09e3cebfe1660b70d76b6bd89152f52183f28c74813dbf454ba1a01c82a38abce
  languageName: node
  linkType: hard

"path-to-regexp@npm:^1.7.0":
  version: 1.8.0
  resolution: "path-to-regexp@npm:1.8.0"
  dependencies:
    isarray: 0.0.1
  checksum: 709f6f083c0552514ef4780cb2e7e4cf49b0cc89a97439f2b7cc69a608982b7690fb5d1720a7473a59806508fc2dae0be751ba49f495ecf89fd8fbc62abccbcd
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 46a65fefaf19c6f57460388a5af9ab81e3d7fd0e7bc44ca59d753cb5c4d0df97c6c6e583674869762101836d68675f027d60f841c105d72734df9dfca97cbcc6
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0, pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-extract-imports@npm:3.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 4b65f2f1382d89c4bc3c0a1bdc5942f52f3cb19c110c57bd591ffab3a5fee03fcf831604168205b0c1b631a3dce2255c70b61aaae3ef39d69cd7eb450c2552d2
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.0":
  version: 4.0.3
  resolution: "postcss-modules-local-by-default@npm:4.0.3"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: 2f8083687f3d6067885f8863dd32dbbb4f779cfcc7e52c17abede9311d84faf6d3ed8760e7c54c6380281732ae1f78e5e56a28baf3c271b33f450a11c9e30485
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-scope@npm:3.0.0"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 330b9398dbd44c992c92b0dc612c0626135e2cc840fee41841eb61247a6cfed95af2bd6f67ead9dd9d0bb41f5b0367129d93c6e434fa3e9c58ade391d9a5a138
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4":
  version: 6.0.13
  resolution: "postcss-selector-parser@npm:6.0.13"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: f89163338a1ce3b8ece8e9055cd5a3165e79a15e1c408e18de5ad8f87796b61ec2d48a2902d179ae0c4b5de10fccd3a325a4e660596549b040bc5ad1b465f096
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^8.2.15":
  version: 8.4.27
  resolution: "postcss@npm:8.4.27"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1cdd0c298849df6cd65f7e646a3ba36870a37b65f55fd59d1a165539c263e9b4872a402bf4ed1ca1bc31f58b68b2835545e33ea1a23b161a1f8aa6d5ded81e78
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:^2.3.2":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: b49e409431bf129dd89238d64299ba80717b57ff5a6d1c1a8b1a28b590d998a34e083fa13573bc732bb8d2305becb4c9a4407f8486c81fa7d55100eb08263cf8
  languageName: node
  linkType: hard

"pretty-error@npm:^4.0.0":
  version: 4.0.0
  resolution: "pretty-error@npm:4.0.0"
  dependencies:
    lodash: ^4.17.20
    renderkid: ^3.0.0
  checksum: a5b9137365690104ded6947dca2e33360bf55e62a4acd91b1b0d7baa3970e43754c628cc9e16eafbdd4e8f8bcb260a5865475d4fc17c3106ff2d61db4e72cdf3
  languageName: node
  linkType: hard

"pretty-format@npm:^27.0.2, pretty-format@npm:^27.5.1":
  version: 27.5.1
  resolution: "pretty-format@npm:27.5.1"
  dependencies:
    ansi-regex: ^5.0.1
    ansi-styles: ^5.0.0
    react-is: ^17.0.1
  checksum: cf610cffcb793885d16f184a62162f2dd0df31642d9a18edf4ca298e909a8fe80bdbf556d5c9573992c102ce8bf948691da91bf9739bee0ffb6e79c8a8a6e088
  languageName: node
  linkType: hard

"pretty-format@npm:^29.0.0, pretty-format@npm:^29.6.1":
  version: 29.6.1
  resolution: "pretty-format@npm:29.6.1"
  dependencies:
    "@jest/schemas": ^29.6.0
    ansi-styles: ^5.0.0
    react-is: ^18.0.0
  checksum: 6f923a2379a37a425241dc223d76f671c73c4f37dba158050575a54095867d565c068b441843afdf3d7c37bed9df4bbadf46297976e60d4149972b779474203a
  languageName: node
  linkType: hard

"pretty-quick@npm:^3.1.1":
  version: 3.1.3
  resolution: "pretty-quick@npm:3.1.3"
  dependencies:
    chalk: ^3.0.0
    execa: ^4.0.0
    find-up: ^4.1.0
    ignore: ^5.1.4
    mri: ^1.1.5
    multimatch: ^4.0.0
  peerDependencies:
    prettier: ">=2.0.0"
  bin:
    pretty-quick: bin/pretty-quick.js
  checksum: 28bdc32571e6308e049497f58a9245f272275973782b6ed7fbcf98937101cc605a81b3ab48629dba4687b7e86c87a3733febacdc0746ca4da5d1c80a0b88cf45
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"progress@npm:^2.0.0":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: f67403fe7b34912148d9252cb7481266a354bd99ce82c835f79070643bb3c6583d10dbcfda4d41e04bbc1d8437e9af0fb1e1f2135727878f5308682a579429b7
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.2":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"psl@npm:^1.1.33":
  version: 1.9.0
  resolution: "psl@npm:1.9.0"
  checksum: 20c4277f640c93d393130673f392618e9a8044c6c7bf61c53917a0fddb4952790f5f362c6c730a9c32b124813e173733f9895add8d26f566ed0ea0654b2e711d
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 39f760e09a2a3bbfe8f5287cf733ecdad69d6af2fe6f97ca95f24b8921858b91e9ea3c9eeec6e08cede96181b3bb33f95c6ffd8c77e63986508aa2e8159fa200
  languageName: node
  linkType: hard

"qs@npm:6.11.0":
  version: 6.11.0
  resolution: "qs@npm:6.11.0"
  dependencies:
    side-channel: ^1.0.4
  checksum: 6e1f29dd5385f7488ec74ac7b6c92f4d09a90408882d0c208414a34dd33badc1a621019d4c799a3df15ab9b1d0292f97c1dd71dc7c045e69f81a8064e5af7297
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 5641ea231bad7ef6d64d9998faca95611ed4b11c2591a8cae741e178a974f6a8e0ebde008475259abe1621cb15e692404e6b6626e927f7b849d5c09392604b15
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.5.1":
  version: 2.5.1
  resolution: "raw-body@npm:2.5.1"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: 5362adff1575d691bb3f75998803a0ffed8c64eabeaa06e54b4ada25a0cd1b2ae7f4f5ec46565d1bec337e08b5ac90c76eaa0758de6f72a633f025d754dec29e
  languageName: node
  linkType: hard

"react-dom@npm:^17.0.2":
  version: 17.0.2
  resolution: "react-dom@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
    scheduler: ^0.20.2
  peerDependencies:
    react: 17.0.2
  checksum: 1c1eaa3bca7c7228d24b70932e3d7c99e70d1d04e13bb0843bbf321582bc25d7961d6b8a6978a58a598af2af496d1cedcfb1bf65f6b0960a0a8161cb8dab743c
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.6.0, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 9d6d111d8990dc98bc5402c1266a808b0459b5d54830bbea24c12d908b536df7883f268a7868cfaedde3dd9d4e0d574db456f84d2e6df9c4526f99bb4b5344d8
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: e72d0ba81b5922759e4aff17e0252bd29988f9642ed817f56b25a3e217e13eea8a7f2322af99a06edb779da12d5d636e9fda473d620df9a3da0df2a74141d53e
  languageName: node
  linkType: hard

"react-router-dom@npm:^5.2.0":
  version: 5.3.4
  resolution: "react-router-dom@npm:5.3.4"
  dependencies:
    "@babel/runtime": ^7.12.13
    history: ^4.9.0
    loose-envify: ^1.3.1
    prop-types: ^15.6.2
    react-router: 5.3.4
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
  peerDependencies:
    react: ">=15"
  checksum: b86a6f2f5222f041e38adf4e4b32c7643d6735a1a915ef25855b2db285fd059d72ba8d62e5bcd5d822b8ef9520a80453209e55077f5a90d0f72e908979b8f535
  languageName: node
  linkType: hard

"react-router@npm:5.3.4, react-router@npm:^5.2.0":
  version: 5.3.4
  resolution: "react-router@npm:5.3.4"
  dependencies:
    "@babel/runtime": ^7.12.13
    history: ^4.9.0
    hoist-non-react-statics: ^3.1.0
    loose-envify: ^1.3.1
    path-to-regexp: ^1.7.0
    prop-types: ^15.6.2
    react-is: ^16.6.0
    tiny-invariant: ^1.0.2
    tiny-warning: ^1.0.0
  peerDependencies:
    react: ">=15"
  checksum: 892d4e274a23bf4f39abc2efca54472fb646d3aed4b584020cf49654d2f50d09a2bacebe7c92b4ec7cb8925077376dfcd0664bad6442a73604397cefec9f01f9
  languageName: node
  linkType: hard

"react@npm:^17.0.2":
  version: 17.0.2
  resolution: "react@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: b254cc17ce3011788330f7bbf383ab653c6848902d7936a87b09d835d091e3f295f7e9dd1597c6daac5dc80f90e778c8230218ba8ad599f74adcc11e33b9d61b
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"rechoir@npm:^0.7.0":
  version: 0.7.1
  resolution: "rechoir@npm:0.7.1"
  dependencies:
    resolve: ^1.9.0
  checksum: 2a04aab4e28c05fcd6ee6768446bc8b859d8f108e71fc7f5bcbc5ef25e53330ce2c11d10f82a24591a2df4c49c4f61feabe1fd11f844c66feedd4cd7bb61146a
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: ^4.0.0
    strip-indent: ^3.0.0
  checksum: fa1ef20404a2d399235e83cc80bd55a956642e37dd197b4b612ba7327bf87fa32745aeb4a1634b2bab25467164ab4ed9c15be2c307923dd08b0fe7c52431ae6b
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.0
  resolution: "regenerate-unicode-properties@npm:10.1.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: b1a8929588433ab8b9dc1a34cf3665b3b472f79f2af6ceae00d905fc496b332b9af09c6718fb28c730918f19a00dc1d7310adbaa9b72a2ec7ad2f435da8ace17
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.11":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 27481628d22a1c4e3ff551096a683b424242a216fee44685467307f14d58020af1e19660bf2e26064de946bad7eff28950eae9f8209d55723e2d9351e632bbb4
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.1":
  version: 0.15.1
  resolution: "regenerator-transform@npm:0.15.1"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 2d15bdeadbbfb1d12c93f5775493d85874dbe1d405bec323da5c61ec6e701bc9eea36167483e1a5e752de9b2df59ab9a2dfff6bf3784f2b28af2279a673d29a4
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.0":
  version: 1.5.0
  resolution: "regexp.prototype.flags@npm:1.5.0"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    functions-have-names: ^1.2.3
  checksum: c541687cdbdfff1b9a07f6e44879f82c66bbf07665f9a7544c5fd16acdb3ec8d1436caab01662d2fbcad403f3499d49ab0b77fbc7ef29ef961d98cc4bc9755b4
  languageName: node
  linkType: hard

"regexpp@npm:^3.1.0":
  version: 3.2.0
  resolution: "regexpp@npm:3.2.0"
  checksum: a78dc5c7158ad9ddcfe01aa9144f46e192ddbfa7b263895a70a5c6c73edd9ce85faf7c0430e59ac38839e1734e275b9c3de5c57ee3ab6edc0e0b1bdebefccef8
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.3.1":
  version: 5.3.2
  resolution: "regexpu-core@npm:5.3.2"
  dependencies:
    "@babel/regjsgen": ^0.8.0
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.1.0
    regjsparser: ^0.9.1
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 95bb97088419f5396e07769b7de96f995f58137ad75fac5811fb5fe53737766dfff35d66a0ee66babb1eb55386ef981feaef392f9df6d671f3c124812ba24da2
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 5e1b76afe8f1d03c3beaf9e0d935dd467589c3625f6d65fb8ffa14f224d783a0fed4bf49c2c1b8211043ef92b6117313419edf055a098ed8342e340586741afc
  languageName: node
  linkType: hard

"relateurl@npm:^0.2.7":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 5891e792eae1dfc3da91c6fda76d6c3de0333a60aa5ad848982ebb6dccaa06e86385fb1235a1582c680a3d445d31be01c6bfc0804ebbcab5aaf53fa856fde6b6
  languageName: node
  linkType: hard

"renderkid@npm:^3.0.0":
  version: 3.0.0
  resolution: "renderkid@npm:3.0.0"
  dependencies:
    css-select: ^4.1.3
    dom-converter: ^0.2.0
    htmlparser2: ^6.1.0
    lodash: ^4.17.21
    strip-ansi: ^6.0.1
  checksum: 77162b62d6f33ab81f337c39efce0439ff0d1f6d441e29c35183151f83041c7850774fb904da163d6c844264d440d10557714e6daa0b19e4561a5cd4ef305d41
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve-pathname@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-pathname@npm:3.0.0"
  checksum: 6147241ba42c423dbe83cb067a2b4af4f60908c3af57e1ea567729cc71416c089737fe2a73e9e79e7a60f00f66c91e4b45ad0d37cd4be2d43fec44963ef14368
  languageName: node
  linkType: hard

"resolve.exports@npm:^1.1.0":
  version: 1.1.1
  resolution: "resolve.exports@npm:1.1.1"
  checksum: 485aa10082eb388a569d696e17ad7b16f4186efc97dd34eadd029d95b811f21ffee13b1b733198bb4584dbb3cb296aa6f141835221fb7613b9606b84f1386655
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.20.0, resolve@npm:^1.9.0":
  version: 1.22.3
  resolution: "resolve@npm:1.22.3"
  dependencies:
    is-core-module: ^2.12.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: fb834b81348428cb545ff1b828a72ea28feb5a97c026a1cf40aa1008352c72811ff4d4e71f2035273dc536dcfcae20c13604ba6283c612d70fa0b6e44519c374
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.9.0#~builtin<compat/resolve>":
  version: 1.22.3
  resolution: "resolve@patch:resolve@npm%3A1.22.3#~builtin<compat/resolve>::version=1.22.3&hash=07638b"
  dependencies:
    is-core-module: ^2.12.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ad59734723b596d0891321c951592ed9015a77ce84907f89c9d9307dd0c06e11a67906a3e628c4cae143d3e44898603478af0ddeb2bba3f229a9373efe342665
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 47c4d5be674f7c13eee4cfe927345023972197dbbdfba5d3af7e461d13b44de1bfd663bfc80d2f601f8ef3fc8164c16dd99655a221921954a65d044a2fc1233b
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.0, rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rxjs@npm:^6.6.3":
  version: 6.6.7
  resolution: "rxjs@npm:6.6.7"
  dependencies:
    tslib: ^1.9.0
  checksum: bc334edef1bb8bbf56590b0b25734ba0deaf8825b703256a93714308ea36dff8a11d25533671adf8e104e5e8f256aa6fdfe39b2e248cdbd7a5f90c260acbbd1b
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-array-concat@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.0
    has-symbols: ^1.0.3
    isarray: ^2.0.5
  checksum: f43cb98fe3b566327d0c09284de2b15fb85ae964a89495c1b1a5d50c7c8ed484190f4e5e71aacc167e16231940079b326f2c0807aea633d47cc7322f40a6b57f
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2, safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.1.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-regex-test@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.3
    is-regex: ^1.1.4
  checksum: bc566d8beb8b43c01b94e67de3f070fd2781685e835959bbbaaec91cc53381145ca91f69bd837ce6ec244817afa0a5e974fc4e40a2957f0aca68ac3add1ddd34
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"saxes@npm:^5.0.1":
  version: 5.0.1
  resolution: "saxes@npm:5.0.1"
  dependencies:
    xmlchars: ^2.2.0
  checksum: 5636b55cf15f7cf0baa73f2797bf992bdcf75d1b39d82c0aa4608555c774368f6ac321cb641fd5f3d3ceb87805122cd47540da6a7b5960fe0dbdb8f8c263f000
  languageName: node
  linkType: hard

"scheduler@npm:^0.20.2":
  version: 0.20.2
  resolution: "scheduler@npm:0.20.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: c4b35cf967c8f0d3e65753252d0f260271f81a81e427241295c5a7b783abf4ea9e905f22f815ab66676f5313be0a25f47be582254db8f9241b259213e999b8fc
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0, schema-utils@npm:^3.1.1, schema-utils@npm:^3.2.0":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: ea56971926fac2487f0757da939a871388891bc87c6a82220d125d587b388f1704788f3706e7f63a7b70e49fc2db974c41343528caea60444afd5ce0fe4b85c0
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0":
  version: 4.2.0
  resolution: "schema-utils@npm:4.2.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: 26a0463d47683258106e6652e9aeb0823bf0b85843039e068b57da1892f7ae6b6b1094d48e9ed5ba5cbe9f7166469d880858b9d91abe8bd249421eb813850cde
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: d7e5fcc695a4804209d232a1b18624a5134be334d4e1114b0721f7a5e72bd73da483dcf41528c1af4f4f4892ad7cfd6a1e55c8ffb83f9c9fe723b738db609dbb
  languageName: node
  linkType: hard

"selfsigned@npm:^2.1.1":
  version: 2.1.1
  resolution: "selfsigned@npm:2.1.1"
  dependencies:
    node-forge: ^1
  checksum: aa9ce2150a54838978d5c0aee54d7ebe77649a32e4e690eb91775f71fdff773874a4fbafd0ac73d8ec3b702ff8a395c604df4f8e8868528f36fd6c15076fb43a
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.2.1, semver@npm:^7.3.2, semver@npm:^7.3.5":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 12d8ad952fa353b0995bf180cdac205a4068b759a140e5d3c608317098b3575ac2f1e09182206bf2eb26120e1c0ed8fb92c48c592f6099680de56bb071423ca3
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 74fc07ebb58566b87b078ec63e5a3e41ecd987e4272ba67b7467e86c6ad51bc6b0b0154133b6d8b08a2ddda360464f71382f7ef864700f34844a76c8027817a8
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.1":
  version: 6.0.1
  resolution: "serialize-javascript@npm:6.0.1"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3c4f4cb61d0893b988415bdb67243637333f3f574e9e9cc9a006a2ced0b390b0b3b44aef8d51c951272a9002ec50885eefdc0298891bc27eb2fe7510ea87dc4f
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: ~1.3.4
    batch: 0.6.1
    debug: 2.6.9
    escape-html: ~1.0.3
    http-errors: ~1.6.2
    mime-types: ~2.1.17
    parseurl: ~1.3.2
  checksum: e2647ce13379485b98a53ba2ea3fbad4d44b57540d00663b02b976e426e6194d62ac465c0d862cb7057f65e0de8ab8a684aa095427a4b8612412eca0d300d22f
  languageName: node
  linkType: hard

"serve-static@npm:1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.18.0
  checksum: af57fc13be40d90a12562e98c0b7855cf6e8bd4c107fe9a45c212bf023058d54a1871b1c89511c3958f70626fff47faeb795f5d83f8cf88514dbaeb2b724464d
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 27cb44304d6c9e1a23bc6c706af4acaae1a7aa1054d4ec13c05f01a99fd4887109a83a8042b67ad90dbfcd100d43efc171ee036eb080667172079213242ca36e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.7.3":
  version: 1.8.1
  resolution: "shell-quote@npm:1.8.1"
  checksum: 5f01201f4ef504d4c6a9d0d283fa17075f6770bfbe4c5850b074974c68062f37929ca61700d95ad2ac8822e14e8c4b990ca0e6e9272e64befd74ce5e19f0736b
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.0.2
  resolution: "signal-exit@npm:4.0.2"
  checksum: 41f5928431cc6e91087bf0343db786a6313dd7c6fd7e551dbc141c95bb5fb26663444fd9df8ea47c5d7fc202f60aa7468c3162a9365cbb0615fc5e1b1328fe31
  languageName: node
  linkType: hard

"single-spa-react@npm:^4.3.1":
  version: 4.6.1
  resolution: "single-spa-react@npm:4.6.1"
  dependencies:
    browserslist-config-single-spa: ^1.0.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: "*"
  checksum: 12383d3b1dfc328efb7386f1094cbd255cf7e4785685416cd854ff3e1ba5da7828a2edaada94859593c8bc0405c5982c9655d1f51b5b67a2a94a6c1908983428
  languageName: node
  linkType: hard

"sirv@npm:^1.0.7":
  version: 1.0.19
  resolution: "sirv@npm:1.0.19"
  dependencies:
    "@polka/url": ^1.0.0-next.20
    mrmime: ^1.0.0
    totalist: ^1.0.0
  checksum: c943cfc61baf85f05f125451796212ec35d4377af4da90ae8ec1fa23e6d7b0b4d9c74a8fbf65af83c94e669e88a09dc6451ba99154235eead4393c10dda5b07c
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.24":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: ^0.11.3
    uuid: ^8.3.2
    websocket-driver: ^0.7.4
  checksum: 355309b48d2c4e9755349daa29cea1c0d9ee23e49b983841c6bf7a20276b00d3c02343f9f33f26d2ee8b261a5a02961b52a25c8da88b2538c5b68d3071b4934c
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: ^2.0.0
    smart-buffer: ^4.2.0
  checksum: 259d9e3e8e1c9809a7f5c32238c3d4d2a36b39b83851d0f573bfde5f21c4b1288417ce1af06af1452569cd1eb0841169afd4998f0e04ba04656f6b7f0e46d748
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.6, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:^0.7.3":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 01cc5a74b1f0e1d626a58d36ad6898ea820567e87f18dfc9d24a9843a351aaa2ec09b87422589906d6ff1deed29693e176194dc88bcae7c9a852dc74b311dbf5
  languageName: node
  linkType: hard

"spawn-command@npm:^0.0.2-1":
  version: 0.0.2
  resolution: "spawn-command@npm:0.0.2"
  checksum: e35c5d28177b4d461d33c88cc11f6f3a5079e2b132c11e1746453bbb7a0c0b8a634f07541a2a234fa4758239d88203b758def509161b651e81958894c0b4b64b
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: ^4.1.0
    detect-node: ^2.0.4
    hpack.js: ^2.1.6
    obuf: ^1.1.2
    readable-stream: ^3.0.6
    wbuf: ^1.7.3
  checksum: 0fcaad3b836fb1ec0bdd39fa7008b9a7a84a553f12be6b736a2512613b323207ffc924b9551cef0378f7233c85916cff1118652e03a730bdb97c0e042243d56c
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: ^4.1.0
    handle-thing: ^2.0.0
    http-deceiver: ^1.2.7
    select-hose: ^2.0.0
    spdy-transport: ^3.0.0
  checksum: 2c739d0ff6f56ad36d2d754d0261d5ec358457bea7cbf77b1b05b0c6464f2ce65b85f196305f50b7bd9120723eb94bae9933466f28e67e5cd8cde4e27f1d75f8
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.4
  resolution: "ssri@npm:10.0.4"
  dependencies:
    minipass: ^5.0.0
  checksum: fb14da9f8a72b04eab163eb13a9dda11d5962cd2317f85457c4e0b575e9a6e0e3a6a87b5bf122c75cb36565830cd5f263fb457571bf6f1587eb5f95d095d6165
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: 052bf4d25bbf5f78e06c1d5e67de2e088b06871fa04107ca8d3f0e9d9263326e2942c8bedee3545795fc77d787d443a538345eef74db2f8e35db3558c6f91ff7
  languageName: node
  linkType: hard

"standalone-single-spa-webpack-plugin@npm:^4.0.0":
  version: 4.0.0
  resolution: "standalone-single-spa-webpack-plugin@npm:4.0.0"
  peerDependencies:
    html-webpack-plugin: "*"
    webpack: "*"
  checksum: b03c37587542ca2ff0ec53873cb35c865b639cf9550977cbdd71be3b2c12fc85722cacbe21de4660140918ea53d0f80f5b7cb98330f2f027e948d057853c9c1a
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.0.0":
  version: 1.0.0
  resolution: "stop-iteration-iterator@npm:1.0.0"
  dependencies:
    internal-slot: ^1.0.4
  checksum: d04173690b2efa40e24ab70e5e51a3ff31d56d699550cfad084104ab3381390daccb36652b25755e420245f3b0737de66c1879eaa2a8d4fc0a78f9bf892fcb42
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: ^1.0.2
    strip-ansi: ^6.0.0
  checksum: ce85533ef5113fcb7e522bcf9e62cb33871aa99b3729cec5595f4447f660b0cefd542ca6df4150c97a677d58b0cb727a3fe09ac1de94071d05526c73579bf505
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.7":
  version: 1.2.7
  resolution: "string.prototype.trim@npm:1.2.7"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 05b7b2d6af63648e70e44c4a8d10d8cc457536df78b55b9d6230918bde75c5987f6b8604438c4c8652eb55e4fc9725d2912789eb4ec457d6995f3495af190c09
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.6":
  version: 1.0.6
  resolution: "string.prototype.trimend@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 0fdc34645a639bd35179b5a08227a353b88dc089adf438f46be8a7c197fc3f22f8514c1c9be4629b3cd29c281582730a8cbbad6466c60f76b5f99cf2addb132e
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.6":
  version: 1.0.6
  resolution: "string.prototype.trimstart@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.4
    es-abstract: ^1.20.4
  checksum: 89080feef416621e6ef1279588994305477a7a91648d9436490d56010a1f7adc39167cddac7ce0b9884b8cdbef086987c4dcb2960209f2af8bac0d23ceff4f41
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 9dbcfbaf503c57c06af15fe2c8176fb1bf3af5ff65003851a102749f875a6dbe0ab3b30115eccf6e805e9d756830d3e40ec508b62b3f1ddf3761a20ebe29d3f3
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: ^1.0.0
  checksum: 18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.0, strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"style-loader@npm:^3.2.1":
  version: 3.3.3
  resolution: "style-loader@npm:3.3.3"
  peerDependencies:
    webpack: ^5.0.0
  checksum: f59c953f56f6a935bd6a1dfa409f1128fed2b66b48ce4a7a75b85862a7156e5e90ab163878962762f528ec4d510903d828da645e143fbffd26f055dc1c094078
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0, supports-color@npm:^8.1.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.0.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: ^4.0.0
    supports-color: ^7.0.0
  checksum: 9ee0de3c8ce919d453511b2b1588a8205bd429d98af94a01df87411391010fe22ca463f268c84b2ce2abad019dfff8452aa02806eeb5c905a8d7ad5c4f4c52b8
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 6e8fc7e1486b8b54bea91199d9535bb72f10842e40c79e882fc94fb7b14b89866adf2fd79efa5ebb5b658bc07fb459ccce5ac0e99ef3d72f474e74aaf284029d
  languageName: node
  linkType: hard

"systemjs-webpack-interop@npm:^2.3.7":
  version: 2.3.7
  resolution: "systemjs-webpack-interop@npm:2.3.7"
  peerDependencies:
    webpack: "*"
  checksum: 6a294aa45281b208a43a2e8e358a58e1f37605c322c40180d5cc3bd7a283d4b5c310110274f8b2e96e6d8bf081a101bda6301f962669a1b0722bbd9043ce6933
  languageName: node
  linkType: hard

"table@npm:^6.0.9":
  version: 6.8.1
  resolution: "table@npm:6.8.1"
  dependencies:
    ajv: ^8.0.1
    lodash.truncate: ^4.4.2
    slice-ansi: ^4.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  checksum: 08249c7046125d9d0a944a6e96cfe9ec66908d6b8a9db125531be6eb05fa0de047fd5542e9d43b4f987057f00a093b276b8d3e19af162a9c40db2681058fd306
  languageName: node
  linkType: hard

"tapable@npm:^2.0.0, tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.1.15
  resolution: "tar@npm:6.1.15"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f23832fceeba7578bf31907aac744ae21e74a66f4a17a9e94507acf460e48f6db598c7023882db33bab75b80e027c21f276d405e4a0322d58f51c7088d428268
  languageName: node
  linkType: hard

"terminal-link@npm:^2.0.0":
  version: 2.1.1
  resolution: "terminal-link@npm:2.1.1"
  dependencies:
    ansi-escapes: ^4.2.1
    supports-hyperlinks: ^2.0.0
  checksum: ce3d2cd3a438c4a9453947aa664581519173ea40e77e2534d08c088ee6dda449eabdbe0a76d2a516b8b73c33262fedd10d5270ccf7576ae316e3db170ce6562f
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.7":
  version: 5.3.9
  resolution: "terser-webpack-plugin@npm:5.3.9"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.17
    jest-worker: ^27.4.5
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.1
    terser: ^5.16.8
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 41705713d6f9cb83287936b21e27c658891c78c4392159f5148b5623f0e8c48559869779619b058382a4c9758e7820ea034695e57dc7c474b4962b79f553bc5f
  languageName: node
  linkType: hard

"terser@npm:^5.10.0, terser@npm:^5.16.8":
  version: 5.19.2
  resolution: "terser@npm:5.19.2"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: e059177775b4d4f4cff219ad89293175aefbd1b081252270444dc83e42a2c5f07824eb2a85eae6e22ef6eb7ef04b21af36dd7d1dd7cfb93912310e57d416a205
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": ^0.1.2
    glob: ^7.1.4
    minimatch: ^3.0.4
  checksum: 3b34a3d77165a2cb82b34014b3aba93b1c4637a5011807557dc2f3da826c59975a5ccad765721c4648b39817e3472789f9b0fa98fc854c5c1c7a1e632aacdc28
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"throat@npm:^6.0.1":
  version: 6.0.2
  resolution: "throat@npm:6.0.2"
  checksum: 463093768d4884772020bb18b0f33d3fec8a2b4173f7da3958dfbe88ff0f1e686ffadf0f87333bf6f6db7306b1450efc7855df69c78bf0bfa61f6d84a3361fe8
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 993096c472b6b8f30e29dc777a8d17720e4cab448375041f20c0cb802a09a7fb2217f2a3e8cdc11851faa71c957e2db309357367fc9d7af3cb7a4d00f4b66034
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.0.2":
  version: 1.3.1
  resolution: "tiny-invariant@npm:1.3.1"
  checksum: 872dbd1ff20a21303a2fd20ce3a15602cfa7fcf9b228bd694a52e2938224313b5385a1078cb667ed7375d1612194feaca81c4ecbe93121ca1baebe344de4f84c
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.0":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: da62c4acac565902f0624b123eed6dd3509bc9a8d30c06e017104bedcf5d35810da8ff72864400ad19c5c7806fc0a8323c68baf3e326af7cb7d969f846100d71
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"totalist@npm:^1.0.0":
  version: 1.1.0
  resolution: "totalist@npm:1.1.0"
  checksum: dfab80c7104a1d170adc8c18782d6c04b7df08352dec452191208c66395f7ef2af7537ddfa2cf1decbdcfab1a47afbbf0dec6543ea191da98c1c6e1599f86adc
  languageName: node
  linkType: hard

"tough-cookie@npm:^4.0.0":
  version: 4.1.3
  resolution: "tough-cookie@npm:4.1.3"
  dependencies:
    psl: ^1.1.33
    punycode: ^2.1.1
    universalify: ^0.2.0
    url-parse: ^1.5.3
  checksum: c9226afff36492a52118432611af083d1d8493a53ff41ec4ea48e5b583aec744b989e4280bcf476c910ec1525a89a4a0f1cae81c08b18fb2ec3a9b3a72b91dcc
  languageName: node
  linkType: hard

"tr46@npm:^2.1.0":
  version: 2.1.0
  resolution: "tr46@npm:2.1.0"
  dependencies:
    punycode: ^2.1.1
  checksum: ffe6049b9dca3ae329b059aada7f515b0f0064c611b39b51ff6b53897e954650f6f63d9319c6c008d36ead477c7b55e5f64c9dc60588ddc91ff720d64eb710b3
  languageName: node
  linkType: hard

"tree-kill@npm:^1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 49117f5f410d19c84b0464d29afb9642c863bc5ba40fcb9a245d474c6d5cc64d1b177a6e6713129eb346b40aebb9d4631d967517f9fbe8251c35b21b13cd96c7
  languageName: node
  linkType: hard

"tslib@npm:^1.9.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3":
  version: 2.6.0
  resolution: "tslib@npm:2.6.0"
  checksum: c01066038f950016a18106ddeca4649b4d76caa76ec5a31e2a26e10586a59fceb4ee45e96719bf6c715648e7c14085a81fee5c62f7e9ebee68e77a5396e5538f
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 62b5628bff67c0eb0b66afa371bd73e230399a8d2ad30d852716efcc4656a7516904570cd8631a49a3ce57c10225adf5d0cbdcb47f6b0255fe6557c453925a15
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-buffer@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
    is-typed-array: ^1.1.10
  checksum: 3e0281c79b2a40cd97fe715db803884301993f4e8c18e8d79d75fd18f796e8cd203310fec8c7fdb5e6c09bedf0af4f6ab8b75eb3d3a85da69328f28a80456bd3
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-length@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: b03db16458322b263d87a702ff25388293f1356326c8a678d7515767ef563ef80e1e67ce648b821ec13178dd628eb2afdc19f97001ceae7a31acf674c849af94
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-offset@npm:1.0.0"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    has-proto: ^1.0.1
    is-typed-array: ^1.1.10
  checksum: 04f6f02d0e9a948a95fbfe0d5a70b002191fae0b8fe0fe3130a9b2336f043daf7a3dda56a31333c35a067a97e13f539949ab261ca0f3692c41603a46a94e960b
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-length@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    for-each: ^0.3.3
    is-typed-array: ^1.1.9
  checksum: 2228febc93c7feff142b8c96a58d4a0d7623ecde6c7a24b2b98eb3170e99f7c7eff8c114f9b283085cd59dcd2bd43aadf20e25bba4b034a53c5bb292f71f8956
  languageName: node
  linkType: hard

"typedarray-to-buffer@npm:^3.1.5":
  version: 3.1.5
  resolution: "typedarray-to-buffer@npm:3.1.5"
  dependencies:
    is-typedarray: ^1.0.0
  checksum: 99c11aaa8f45189fcfba6b8a4825fd684a321caa9bd7a76a27cf0c7732c174d198b99f449c52c3818107430b5f41c0ccbbfb75cb2ee3ca4a9451710986d61a60
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
    has-bigints: ^1.0.2
    has-symbols: ^1.0.3
    which-boxed-primitive: ^1.0.2
  checksum: b7a1cf5862b5e4b5deb091672ffa579aa274f648410009c81cca63fed3b62b610c4f3b773f912ce545bb4e31edc3138975b5bc777fc6e4817dca51affb6380e9
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 8d6f5f586b9ce1ed0e84a37df6b42fdba1317a05b5df0c249962bd5da89528771e2d149837cad11aa26bcb84c35355cb9f58a10c3d41fa3b899181ece6c85220
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: ^4.0.0
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 0884b58365af59f89739e6f71e3feacb5b1b41f2df2d842d0757933620e6de08eff347d27e9d499b43c40476cbaf7988638d3acb2ffbcb9d35fd035591adfd15
  languageName: node
  linkType: hard

"universalify@npm:^0.2.0":
  version: 0.2.0
  resolution: "universalify@npm:0.2.0"
  checksum: e86134cb12919d177c2353196a4cc09981524ee87abf621f7bc8d249dbbbebaec5e7d1314b96061497981350df786e4c5128dbf442eba104d6e765bc260678b5
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.11":
  version: 1.0.11
  resolution: "update-browserslist-db@npm:1.0.11"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: b98327518f9a345c7cad5437afae4d2ae7d865f9779554baf2a200fdf4bac4969076b679b1115434bd6557376bdd37ca7583d0f9b8f8e302d7d4cc1e91b5f231
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-parse@npm:^1.5.3":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: ^2.1.1
    requires-port: ^1.0.0
  checksum: fbdba6b1d83336aca2216bbdc38ba658d9cfb8fc7f665eb8b17852de638ff7d1a162c198a8e4ed66001ddbf6c9888d41e4798912c62b4fd777a31657989f7bdf
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 97ffd3bd2bb80c773429d3fb8396469115cd190dded1e733f190d8b602bd0a1bcd6216b7ce3c4395ee3c79e3c879c19d268dbaae3093564cb169ad1212d436f4
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"v8-compile-cache@npm:^2.0.3":
  version: 2.3.0
  resolution: "v8-compile-cache@npm:2.3.0"
  checksum: adb0a271eaa2297f2f4c536acbfee872d0dd26ec2d76f66921aa7fc437319132773483344207bdbeee169225f4739016d8d2dbf0553913a52bb34da6d0334f8e
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^8.1.0":
  version: 8.1.1
  resolution: "v8-to-istanbul@npm:8.1.1"
  dependencies:
    "@types/istanbul-lib-coverage": ^2.0.1
    convert-source-map: ^1.6.0
    source-map: ^0.7.3
  checksum: 54ce92bec2727879626f623d02c8d193f0c7e919941fa373ec135189a8382265117f5316ea317a1e12a5f9c13d84d8449052a731fe3306fa4beaafbfa4cab229
  languageName: node
  linkType: hard

"value-equal@npm:^1.0.1":
  version: 1.0.1
  resolution: "value-equal@npm:1.0.1"
  checksum: bb7ae1facc76b5cf8071aeb6c13d284d023fdb370478d10a5d64508e0e6e53bb459c4bbe34258df29d82e6f561f874f0105eba38de0e61fe9edd0bdce07a77a2
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"w3c-hr-time@npm:^1.0.2":
  version: 1.0.2
  resolution: "w3c-hr-time@npm:1.0.2"
  dependencies:
    browser-process-hrtime: ^1.0.0
  checksum: ec3c2dacbf8050d917bbf89537a101a08c2e333b4c19155f7d3bedde43529d4339db6b3d049d9610789cb915f9515f8be037e0c54c079e9d4735c50b37ed52b9
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "w3c-xmlserializer@npm:2.0.0"
  dependencies:
    xml-name-validator: ^3.0.0
  checksum: ae25c51cf71f1fb2516df1ab33a481f83461a117565b95e3d0927432522323f93b1b2846cbb60196d337970c421adb604fc2d0d180c6a47a839da01db5b9973b
  languageName: node
  linkType: hard

"walker@npm:^1.0.7":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.0":
  version: 2.4.0
  resolution: "watchpack@npm:2.4.0"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 23d4bc58634dbe13b86093e01c6a68d8096028b664ab7139d58f0c37d962d549a940e98f2f201cecdabd6f9c340338dc73ef8bf094a2249ef582f35183d1a131
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: ^1.0.0
  checksum: 2abc306c96930b757972a1c4650eb6b25b5d99f24088714957f88629e137db569368c5de0e57986c89ea70db2f1df9bba11a87cb6d0c8694b6f53a0159fab3bf
  languageName: node
  linkType: hard

"webidl-conversions@npm:^5.0.0":
  version: 5.0.0
  resolution: "webidl-conversions@npm:5.0.0"
  checksum: ccf1ec2ca7c0b5671e5440ace4a66806ae09c49016ab821481bec0c05b1b82695082dc0a27d1fe9d804d475a408ba0c691e6803fd21be608e710955d4589cd69
  languageName: node
  linkType: hard

"webidl-conversions@npm:^6.1.0":
  version: 6.1.0
  resolution: "webidl-conversions@npm:6.1.0"
  checksum: 1f526507aa491f972a0c1409d07f8444e1d28778dfa269a9971f2e157182f3d496dc33296e4ed45b157fdb3bf535bb90c90bf10c50dcf1dd6caacb2a34cc84fb
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^4.4.2":
  version: 4.9.0
  resolution: "webpack-bundle-analyzer@npm:4.9.0"
  dependencies:
    "@discoveryjs/json-ext": 0.5.7
    acorn: ^8.0.4
    acorn-walk: ^8.0.0
    chalk: ^4.1.0
    commander: ^7.2.0
    gzip-size: ^6.0.0
    lodash: ^4.17.20
    opener: ^1.5.2
    sirv: ^1.0.7
    ws: ^7.3.1
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: e439aea4e88e18bfdc16eb69782c1bb17b2e581905a5cfa8d34058dc6677f6e202f896189268e58b49fa14ae12f5ef4c25cdca9f98f3de7e6699ac62def2f0af
  languageName: node
  linkType: hard

"webpack-cli@npm:^4.10.0":
  version: 4.10.0
  resolution: "webpack-cli@npm:4.10.0"
  dependencies:
    "@discoveryjs/json-ext": ^0.5.0
    "@webpack-cli/configtest": ^1.2.0
    "@webpack-cli/info": ^1.5.0
    "@webpack-cli/serve": ^1.7.0
    colorette: ^2.0.14
    commander: ^7.0.0
    cross-spawn: ^7.0.3
    fastest-levenshtein: ^1.0.12
    import-local: ^3.0.2
    interpret: ^2.2.0
    rechoir: ^0.7.0
    webpack-merge: ^5.7.3
  peerDependencies:
    webpack: 4.x.x || 5.x.x
  peerDependenciesMeta:
    "@webpack-cli/generators":
      optional: true
    "@webpack-cli/migrate":
      optional: true
    webpack-bundle-analyzer:
      optional: true
    webpack-dev-server:
      optional: true
  bin:
    webpack-cli: bin/cli.js
  checksum: 2ff5355ac348e6b40f2630a203b981728834dca96d6d621be96249764b2d0fc01dd54edfcc37f02214d02935de2cf0eefd6ce689d970d154ef493f01ba922390
  languageName: node
  linkType: hard

"webpack-config-single-spa-react@npm:^4.0.0":
  version: 4.0.4
  resolution: "webpack-config-single-spa-react@npm:4.0.4"
  dependencies:
    webpack-config-single-spa: 5.3.0
  checksum: 61bf455460792bd8e261bbfb283a70e5f35a9c6c19aaaae4b23d8dc86edfafd16663dedd894386a393cc4cea45763c128f096c554d333bc619fcb4014238385b
  languageName: node
  linkType: hard

"webpack-config-single-spa@npm:5.3.0":
  version: 5.3.0
  resolution: "webpack-config-single-spa@npm:5.3.0"
  dependencies:
    babel-loader: ^8.2.2
    css-loader: ^5.2.7
    html-webpack-plugin: ^5.3.2
    standalone-single-spa-webpack-plugin: ^4.0.0
    style-loader: ^3.2.1
    systemjs-webpack-interop: ^2.3.7
    webpack-bundle-analyzer: ^4.4.2
  checksum: 688571f45b0e43260637967584660436fb66dc4c4b956c80bc08c55836eac93764024a6b2dbcb95addfb46a45b8c16265e081ae718c27e01f42ea080033d976e
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^5.3.1":
  version: 5.3.3
  resolution: "webpack-dev-middleware@npm:5.3.3"
  dependencies:
    colorette: ^2.0.10
    memfs: ^3.4.3
    mime-types: ^2.1.31
    range-parser: ^1.2.1
    schema-utils: ^4.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: dd332cc6da61222c43d25e5a2155e23147b777ff32fdf1f1a0a8777020c072fbcef7756360ce2a13939c3f534c06b4992a4d659318c4a7fe2c0530b52a8a6621
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^4.0.0":
  version: 4.15.1
  resolution: "webpack-dev-server@npm:4.15.1"
  dependencies:
    "@types/bonjour": ^3.5.9
    "@types/connect-history-api-fallback": ^1.3.5
    "@types/express": ^4.17.13
    "@types/serve-index": ^1.9.1
    "@types/serve-static": ^1.13.10
    "@types/sockjs": ^0.3.33
    "@types/ws": ^8.5.5
    ansi-html-community: ^0.0.8
    bonjour-service: ^1.0.11
    chokidar: ^3.5.3
    colorette: ^2.0.10
    compression: ^1.7.4
    connect-history-api-fallback: ^2.0.0
    default-gateway: ^6.0.3
    express: ^4.17.3
    graceful-fs: ^4.2.6
    html-entities: ^2.3.2
    http-proxy-middleware: ^2.0.3
    ipaddr.js: ^2.0.1
    launch-editor: ^2.6.0
    open: ^8.0.9
    p-retry: ^4.5.0
    rimraf: ^3.0.2
    schema-utils: ^4.0.0
    selfsigned: ^2.1.1
    serve-index: ^1.9.1
    sockjs: ^0.3.24
    spdy: ^4.0.2
    webpack-dev-middleware: ^5.3.1
    ws: ^8.13.0
  peerDependencies:
    webpack: ^4.37.0 || ^5.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: cd0063b068d2b938fd76c412d555374186ac2fa84bbae098265212ed50a5c15d6f03aa12a5a310c544a242943eb58c0bfde4c296d5c36765c182f53799e1bc71
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3, webpack-merge@npm:^5.8.0":
  version: 5.9.0
  resolution: "webpack-merge@npm:5.9.0"
  dependencies:
    clone-deep: ^4.0.1
    wildcard: ^2.0.0
  checksum: 64fe2c23aacc5f19684452a0e84ec02c46b990423aee6fcc5c18d7d471155bd14e9a6adb02bd3656eb3e0ac2532c8e97d69412ad14c97eeafe32fa6d10050872
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack@npm:^5.75.0":
  version: 5.88.2
  resolution: "webpack@npm:5.88.2"
  dependencies:
    "@types/eslint-scope": ^3.7.3
    "@types/estree": ^1.0.0
    "@webassemblyjs/ast": ^1.11.5
    "@webassemblyjs/wasm-edit": ^1.11.5
    "@webassemblyjs/wasm-parser": ^1.11.5
    acorn: ^8.7.1
    acorn-import-assertions: ^1.9.0
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.15.0
    es-module-lexer: ^1.2.1
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.2.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.3.7
    watchpack: ^2.4.0
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 79476a782da31a21f6dd38fbbd06b68da93baf6a62f0d08ca99222367f3b8668f5a1f2086b7bb78e23172e31fa6df6fa7ab09b25e827866c4fc4dc2b30443ce2
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: ">=0.5.1"
    safe-buffer: ">=5.1.0"
    websocket-extensions: ">=0.1.1"
  checksum: fffe5a33fe8eceafd21d2a065661d09e38b93877eae1de6ab5d7d2734c6ed243973beae10ae48c6613cfd675f200e5a058d1e3531bc9e6c5d4f1396ff1f0bfb9
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 5976835e68a86afcd64c7a9762ed85f2f27d48c488c707e67ba85e717b90fa066b98ab33c744d64255c9622d349eedecf728e65a5f921da71b58d0e9591b9038
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^1.0.5":
  version: 1.0.5
  resolution: "whatwg-encoding@npm:1.0.5"
  dependencies:
    iconv-lite: 0.4.24
  checksum: 5be4efe111dce29ddee3448d3915477fcc3b28f991d9cf1300b4e50d6d189010d47bca2f51140a844cf9b726e8f066f4aee72a04d687bfe4f2ee2767b2f5b1e6
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^2.3.0":
  version: 2.3.0
  resolution: "whatwg-mimetype@npm:2.3.0"
  checksum: 23eb885940bcbcca4ff841c40a78e9cbb893ec42743993a42bf7aed16085b048b44b06f3402018931687153550f9a32d259dfa524e4f03577ab898b6965e5383
  languageName: node
  linkType: hard

"whatwg-url@npm:^8.0.0, whatwg-url@npm:^8.5.0":
  version: 8.7.0
  resolution: "whatwg-url@npm:8.7.0"
  dependencies:
    lodash: ^4.7.0
    tr46: ^2.1.0
    webidl-conversions: ^6.1.0
  checksum: a87abcc6cefcece5311eb642858c8fdb234e51ec74196bfacf8def2edae1bfbffdf6acb251646ed6301f8cee44262642d8769c707256125a91387e33f405dd1e
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1":
  version: 1.0.1
  resolution: "which-collection@npm:1.0.1"
  dependencies:
    is-map: ^2.0.1
    is-set: ^2.0.1
    is-weakmap: ^2.0.1
    is-weakset: ^2.0.1
  checksum: c815bbd163107ef9cb84f135e6f34453eaf4cca994e7ba85ddb0d27cea724c623fae2a473ceccfd5549c53cc65a5d82692de418166df3f858e1e5dc60818581c
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.10, which-typed-array@npm:^1.1.11, which-typed-array@npm:^1.1.9":
  version: 1.1.11
  resolution: "which-typed-array@npm:1.1.11"
  dependencies:
    available-typed-arrays: ^1.0.5
    call-bind: ^1.0.2
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.0
  checksum: 711ffc8ef891ca6597b19539075ec3e08bb9b4c2ca1f78887e3c07a977ab91ac1421940505a197758fb5939aa9524976d0a5bbcac34d07ed6faa75cedbb17206
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.1
  resolution: "wildcard@npm:2.0.1"
  checksum: e0c60a12a219e4b12065d1199802d81c27b841ed6ad6d9d28240980c73ceec6f856771d575af367cbec2982d9ae7838759168b551776577f155044f5a5ba843c
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^3.0.0":
  version: 3.0.3
  resolution: "write-file-atomic@npm:3.0.3"
  dependencies:
    imurmurhash: ^0.1.4
    is-typedarray: ^1.0.0
    signal-exit: ^3.0.2
    typedarray-to-buffer: ^3.1.5
  checksum: c55b24617cc61c3a4379f425fc62a386cc51916a9b9d993f39734d005a09d5a4bb748bc251f1304e7abd71d0a26d339996c275955f527a131b1dcded67878280
  languageName: node
  linkType: hard

"ws@npm:^7.3.1, ws@npm:^7.4.6":
  version: 7.5.9
  resolution: "ws@npm:7.5.9"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: c3c100a181b731f40b7f2fddf004aa023f79d64f489706a28bc23ff88e87f6a64b3c6651fbec3a84a53960b75159574d7a7385709847a62ddb7ad6af76f49138
  languageName: node
  linkType: hard

"ws@npm:^8.13.0":
  version: 8.13.0
  resolution: "ws@npm:8.13.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 53e991bbf928faf5dc6efac9b8eb9ab6497c69feeb94f963d648b7a3530a720b19ec2e0ec037344257e05a4f35bd9ad04d9de6f289615ffb133282031b18c61c
  languageName: node
  linkType: hard

"xml-name-validator@npm:^3.0.0":
  version: 3.0.0
  resolution: "xml-name-validator@npm:3.0.0"
  checksum: b3ac459afed783c285bb98e4960bd1f3ba12754fd4f2320efa0f9181ca28928c53cc75ca660d15d205e81f92304419afe94c531c7cfb3e0649aa6d140d53ecb0
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 8c70ac94070ccca03f47a81fcce3b271bd1f37a591bf5424e787ae313fcb9c212f5f6786e1fa82076a2c632c0141552babcd85698c437506dfa6ae2d58723062
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.2":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs@npm:^16.2.0":
  version: 16.2.0
  resolution: "yargs@npm:16.2.0"
  dependencies:
    cliui: ^7.0.2
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.0
    y18n: ^5.0.5
    yargs-parser: ^20.2.2
  checksum: b14afbb51e3251a204d81937c86a7e9d4bdbf9a2bcee38226c900d00f522969ab675703bee2a6f99f8e20103f608382936034e64d921b74df82b63c07c5e8f59
  languageName: node
  linkType: hard
