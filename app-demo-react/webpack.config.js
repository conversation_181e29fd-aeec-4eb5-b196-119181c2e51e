const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "dealer",
    projectName: "app-demo-react",
    webpackConfigEnv,
    argv,
  });

  // webpack-config-single-spa-react 会在打包期间排除 react和react-dom, 导致如果页面失效.
  // 目前两种解决方法 :
  //  1是 root-config中imports 两个库
  //  2是当前采取的, 将 defaultConfig.externals = []  (目前采取)
  defaultConfig.externals = [];

  return merge(defaultConfig, {
    // modify the webpack config however you'd like to by adding to this object
  });
};
