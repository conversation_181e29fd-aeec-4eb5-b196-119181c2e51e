import React from "react";
import { HashRouter as Router, Switch, Route, Link } from "react-router-dom";
import "./main.css";

export default function Root(props) {
  return (
    <Router>
      <div class="app-demo-react-wrap">
        <h1>React Demo</h1>
        <nav>
          <Link to="/">Home</Link> <Link to="/about">About</Link>{" "}
          <Link to="/users">Users</Link>
        </nav>

        {/* A <Switch> looks through its children <Route>s and
            renders the first one that matches the current URL. */}
        <Switch>
          <Route path="/about">
            <About />
          </Route>
          <Route path="/users">
            <Users />
          </Route>
          <Route path="/">
            <Home />
          </Route>
        </Switch>
      </div>
    </Router>
  );
}

function Home() {
  return <h2>Home Page</h2>;
}

function About() {
  return <h2>About Page</h2>;
}

function Users() {
  return <h2>Users Page</h2>;
}
