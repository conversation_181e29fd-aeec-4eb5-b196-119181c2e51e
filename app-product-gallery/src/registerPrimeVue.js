import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';
import Card from 'primevue/card';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Image from 'primevue/image';



const components = {
  Card,
  Accordion,
  AccordionTab,
  DataTable,
  Column,
  Image
}

import Tooltip from 'primevue/tooltip';
const directives = {
  Tooltip
}

export default function (app) {
  // 循环注册所有组件
  for (const [name, component] of Object.entries(components)) {
    app.component(name, component)
  }
  // 循环注册所有指令
  for (const [name, directive] of Object.entries(directives)) {
    app.directive(name, directive)
  }
}