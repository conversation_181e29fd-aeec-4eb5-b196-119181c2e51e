import { h, createApp } from "vue";
import singleSpaVue from "single-spa-vue";
import VueVideoPlayer from '@videojs-player/vue'
import 'video.js/dist/video-js.css'

import App from "./App.vue";
import router from "./router";

// PrimeVue
// import "primevue/resources/themes/md-light-indigo/theme.css";
import "primevue/resources/themes/aura-light-indigo/theme.css";

import PrimeVue from 'primevue/config';
import registerPrimeVue from './registerPrimeVue'

const vueLifecycles = singleSpaVue({
  createApp,
  appOptions: {
    render() {
      return h(App, {
        // single-spa props are available on the "this" object. Forward them to your component as needed.
        // https://single-spa.js.org/docs/building-applications#lifecycle-props
        // name: this.name,
        // mountParcel: this.mountParcel,
        // singleSpa: this.singleSpa,
      });
    },
  },
  handleInstance: (app) => {
    app.use(router);
    app.use(VueVideoPlayer);
    app.use(PrimeVue, { ripple: true });
    registerPrimeVue(app)
  },
});

export const bootstrap = vueLifecycles.bootstrap;
export const mount = vueLifecycles.mount;
export const unmount = vueLifecycles.unmount;
