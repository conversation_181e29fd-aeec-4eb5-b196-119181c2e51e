import { createRouter, createWebHistory } from "vue-router";

// import Home from "../views/Home.vue";
import ChatgptIde from "../views/ChatgptIde.vue";
import ChatgptVscode from "../views/ChatgptVscode.vue";
import Chat2DB from "../views/Chat2DB.vue";
import Idea from "../views/Idea.vue";
import ChatgptImg from "../views/ChatgptImg.vue";
import Faq from "../views/Faq.vue";
import Updates from "../views/Updates.vue";

const routes = [
  {
    path: "/",
    redirect: "/chatgpt-idea",
  },
  {
    path: "/chatgpt-idea",
    name: "ChatgptIde",
    component: ChatgptIde,
  },
  {
    path: "/chatgpt-vscode",
    name: "ChatgptVscode",
    component: ChatgptVscode,
  },
  {
    path: "/chatgpt2db",
    name: "Chat2D<PERSON>",
    component: Chat2DB,
  },
  {
    path: "/idea",
    name: "idea",
    component: Idea,
  },
  {
    path: "/chatgpt-img",
    name: "ChatgptImg",
    component: ChatgptImg,
  },
  {
    path: "/faq",
    name: "FAQ",
    component: Faq,
  },
  {
    path: "/updates",
    name: "Updates",
    component: Updates,
  },
];

const router = createRouter({
  history: createWebHistory("/app-product-gallery/"),
  routes,
});

export default router;
