export const dataIdea = [
  {
    reverse: false,
    data: {
      cover: {
        img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/temp-pic-chatgpt-ide-01.png",
        video: "",
      },
      content: {
        title: "Chat功能，实时和AI进行交流",
        desc: "AutoCopilot 内置了一个Chat面板，遇到问题时，开发者可以直接在 IntelliJ IDEA 中向 AutoCopilot 提问。AutoCopilot 会根据问题提供针对性的解决方案，包括代码示例、相关文档链接等。这样，开发者无需离开开发环境就能解决问题，节省了大量时间。",
      },
    },
  },
  {
    reverse: true,
    data: {
      cover: {
        img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/temp-pic-chatgpt-ide-01.png",
        video: "",
      },
      content: {
        title: "AutoCopilot一键介入",
        desc: "遇到问题，选定代码，一键调用AutoCopilot介入，为开发人员提供实时帮助。",
        buttons: [
          {
            className: "",
            text: "下载体验",
            href: "",
          },
          {
            className: "",
            text: "使用文档",
            href: "",
          },
        ],
      },
    },
  },
];

export const dataVscode = [
  {
    reverse: true,
    data: {
      cover: {
        img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/temp-pic-chatgpt-ide-01.png",
        video: "",
      },
      content: {
        title: "vscode插件 - 功能介绍",
        desc: "可通过设置可完全定制每一个常用快捷键prompt提示词; 通过代码区域选中代码可对日常代码，重构，优化，添加单元测试等功能; 聊天窗口可将代码快速插入到光位置或替换选中代码区域; 通过上下键头可快速输入历史聊天内容，可删除聊天窗口内消息; ",
        buttons: [
          {
            className: "",
            text: "下载体验",
            href: "",
          },
          {
            className: "",
            text: "使用文档",
            href: "",
          },
        ],
      },
    },
  },
];

export const dataAutoCopilo = [
  {
    reverse: true,
    data: {
      cover: {
        img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/temp-pic-chatgpt-ide-01.png",
        video: "",
      },
      content: {
        title: "代码生成 - AutoCopilo 功能介绍",
        desc: "根据需求描述文档生成项目代码 : 选中文件（需求说明文档）右键->AutoCopilo->生成代码 【注意】中间过程已省略 ",
        buttons: [
          {
            className: "",
            text: "下载体验",
            href: "",
          },
          {
            className: "",
            text: "使用文档",
            href: "",
          },
        ],
      },
    },
  },
];
