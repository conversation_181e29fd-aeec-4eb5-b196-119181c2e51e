<template>
  <section class="banner">
    <div class="intro-text">
      <p class="sub-title">AutoCopilot IDEA插件致力于帮助开发人员</p>
      <h2>提升开发效率和质量</h2>
      <p class="typed">
        <span class="description">{{ banner.descActive }}</span>
        <span class="cursor"></span>
      </p>
      <p class="btns">
        <a class="btn-gradient" target="_blank"
          href="https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw">立即使用</a>
      </p>
    </div>
    <div class="intro-img">
      <video-player src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/plugin-idea-1017.mp4"
        controls muted fluid />
    </div>
    <ul class="intro-summary">
      <li>辅助代码设计</li>
      <li>编写单元测试</li>
      <li>提升代码质量</li>
    </ul>
  </section>

  <div class="brochure-wrap">
    <section class="brochure-1" rel="fadeup">
      <h3>{{ brochure1.title }}</h3>
      <ul>
        <li v-for="item in brochure1.items" :key="item.title">
          <h6>
            <span>{{ item.title }}</span>
            <img :src="item.icon" alt="">
          </h6>
          <p>{{ item.desc }}</p>
        </li>
      </ul>
      <p class="btns"><a target="_blank" :href="brochure1.btn.link">{{ brochure1.btn.text }}</a></p>
    </section>

    <section class="brochure-2">
      <div class="item" v-for="item in brochure2.items" :key="item.cover" :class="item.className" rel="fadeup">
        <div class="content">
          <p class="sub-title">{{ item.content.subTitle }}</p>
          <h3>{{ item.content.title }}</h3>
          <p>{{ item.content.desc }}</p>
          <ul v-if="item.content.features">
            <li v-for="feature in item.content.features" :key="feature">
              <i class="material-icons">done</i>
              <span>{{ feature }}</span>
            </li>
          </ul>
          <p class="btns">
            <a v-for="btn in item.content.btns" :key="btn.link" target="_blank" class="btn-link-primary-amination"
              :href="btn.link">{{
                btn.text
              }}</a>
          </p>
        </div>
        <div class="cover"><img :src="item.cover" alt="img"></div>
      </div>
    </section>

    <section class="brochure-3" rel="fadeup">
      <p class="sub-title">{{ brochure3.subTitle }}</p>
      <h3>{{ brochure3.title }}</h3>
      <ul>
        <li v-for="item in brochure3.items" :key="item.title" rel="fadeup">
          <p class="img"><img :src="item.icon" alt="icon"></p>
          <h4>{{ item.title }}</h4>
          <p class="description">{{ item.description }}</p>
          <!-- <p class="btns"><a :href="item.btn.link">
              <span>{{ item.btn.text }}</span>
              <i class="material-icons">
                north_east
              </i>
            </a></p> -->
        </li>
      </ul>
    </section>
  </div>
</template>

<script>
let timer1, timer2;
export default {
  name: "ChatgptIdea",
  data() {
    return {
      banner: {
        descActive: '实时交流，智能解答，开发更高效',
        descAnimation: false,
        desc: [
          '精准分析，快速定位，提升质量',
          '自动化工具，一键生成，效率与质量并重',
          '实时交流，智能解答，开发更高效'
        ]
      },
      brochure1: {
        title: '多种使用场景',
        items: [
          {
            title: '交互式AI聊天工具',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon1.png',
            desc: 'AI开发助手插件具有交互式AI聊天功能，开发人员可以直接与AI进行沟通交流，快速解答编程疑问，提升开发效率。'
          },
          {
            title: '快捷代码解决方案',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon2.png',
            desc: '用户可以选择代码片段，通过右键菜单发送给AI，AI将为用户提供解决问题的相关代码方案，避免了繁琐的查找过程，极大提高了代码开发效率。'
          },
          {
            title: '一键生成测试和文档',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon3.png',
            desc: '在编辑器上，我们为每个方法添加了一个按钮，用户点击后可以自动生成该方法的单元测试或者文档，可以将测试代码写入测试代码目录或者在方法前面添加文档，提高工作效率。'
          }
        ],
        btn: {
          text: '了解更多',
          link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
        }
      },
      brochure2: {
        items: [
          {
            content: {
              subTitle: '交互式AI聊天工具',
              title: '实时交流，智能解答，开发更高效',
              desc: '功能丰富的AI交流窗口，随时辅助您的编程顾问，随时解答您的疑惑',
              features: [
                '交互式AI聊天', '聊天记录云端保存，上下文可自由选择', '多项目、多会话并行交流',
              ],
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover1.jpg'
          },
          {
            className: 'item-revert',
            content: {
              subTitle: '快捷代码解决方案',
              title: '精准分析，快速定位，提升质量',
              desc: '用户可以选择代码片段，通过右键菜单发送给AI，AI会帮助解决相关问题。这个功能可以帮助开发人员更快地找到代码中的问题，提高代码质量。',
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover2.jpg'
          },
          {
            content: {
              subTitle: '一键生成测试和文档',
              title: '自动化工具，一键生成，效率与质量并重',
              desc: '一键生成测试和文档，让编程更高效，更有质量。',
              features: [
                '预先分析代码', '自动携带代码上下文', '全程自动化',
              ],
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover3.jpg'
          }
        ],
      },
      brochure3: {
        subTitle: '用户案例',
        title: '智能编程，高效开发，AI开发助手，您的最佳助手！',
        items: [
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon4.png',
            title: '解释代码、查找bug',
            description: '选择代码片段，帮助开发人员快速理解代码意图，找到代码中可能存在bug的地方。',
            btn: {
              text: '了解更多',
              link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon5.png',
            title: '生成单元测试、安全检测',
            description: 'AI辅助，让代码得到更多的保护，帮助扫描代码中的安全问题。',
            btn: {
              text: '了解更多',
              link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon6.png',
            title: '可读性、性能优化',
            description: '提升代码可维护性，一键发现代码中的性能问题，并给出最佳实践。',
            btn: {
              text: '了解更多',
              link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon7.png',
            title: '业务开发、量身定制',
            description: '根据开发人员习惯，深度定制开发，自动分析代码，上下文可控，辅助生成提示词。',
            btn: {
              text: '了解更多',
              link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw4ctfczw'
            }
          }
        ]
      }
    }
  },
  methods: {
    init() {
      // 打印机打字动画
      const duration = 4000;
      let index = 0;
      const desc = this.banner.desc;
      timer1 = setInterval(() => {
        this.banner.descActive = '';
        const text = desc[index % desc.length]
        this.printText(text, duration)
        index++;
      }, duration)
      // [模块动画] 升起 + 渐隐渐出
      window.addEventListener('scroll', this.checkScroll);
    },

    printText(text, duration) {
      console.log(text, duration)
      let i = 0;
      let len = text.length;
      timer2 = setInterval(() => {
        if (i < len) {
          this.banner.descActive = `${this.banner.descActive}${text[i]}`;
          i++;
        } else {
          clearInterval(timer2)
        }
      }, Math.floor((duration - 2000) / len));

    },
    checkScroll() {
      const elements = document.querySelectorAll('[rel="fadeup"]');
      elements.forEach((ele) => {
        const position = ele.getBoundingClientRect();
        if (position.top <= window.innerHeight) {
          ele.classList.add("fadeup-active");
        }
      });
    },
    // 修复当用户切换tab时, setInterval 不准确的问题
    // Solution : https://usefulangle.com/post/280/settimeout-setinterval-on-inactive-tab
    fixSetIntervalInactive() {
      if (document.hidden) {
        // 页面被切走, 清除计时器
        clearInterval(timer2);
        clearInterval(timer1);
        this.banner.descActive = this.banner.desc[this.banner.desc.length - 1]
      } else {
        // 页面切换回来, 重新设定计时器
        this.init();
        console.log('open')
      }
    }
  },
  mounted() {
    this.init();
    document.addEventListener("visibilitychange", this.fixSetIntervalInactive);
  },
  beforeUnmount() {
    clearInterval(timer2);
    clearInterval(timer1);
    // [模块动画] 升起 + 渐隐渐出
    window.removeEventListener('scroll', this.checkScroll);
    document.removeEventListener("visibilitychange", this.fixSetIntervalInactive);
  }
};
</script>
<style lang="sass" scoped>
$btn-bg: #6a4dff
$btn-hover-bg: #5a41d9
$text-primary: #6a4dff

%btn-radius
  display: inline-block
  color: #fff
  font-size: 1.6rem
  font-weight: bold
  text-align: center
  background-color: $btn-bg
  border-radius: 6px
  text-decoration: none
  padding: 1.2rem 2.4rem
%btn-radius-hover
  background-color: $btn-hover-bg

%sub-title
  font-size: 1.6rem
  color: $text-primary

%btn-link-primary
  font-size: 1.6rem
  color: $text-primary

@keyframes fadeout
  0% 
    opacity: 1
  100% 
    opacity: 0
@mixin fadeOutText
  animation-name: fadeout
  animation-duration: 0.7s
  animation-iteration-count: infinite

@keyframes fadeup
  0% 
    opacity: 0
    transform: translateY(30px)
  100% 
    opacity: 1
    transform: translateY(0px)
@mixin fadeupElement
  animation-name: fadeup
  animation-duration: 0.6s
  animation-timing-function: ease-in

.fadeup-active
  @include fadeupElement
// *[rel="fadeup"]
//   opacity: 0

.banner
  .intro-text
    text-align: center
    height: 78rem
    background: url('https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_app_product_gallery_banner_bg.jpg') no-repeat center center
    background-size: cover
    padding-top: 2rem
    p.sub-title
      @extend %sub-title
      text-align: center
      margin-bottom: 1rem
    h2
      font-size: 48px
      margin: 0
    p.typed
      display: flex
      justify-content: center
      align-items: center
      font-size: 48px
      font-weight: bold
      height: 7.7rem
      margin: -1.5rem 0 0
      span.description
        text-fill-color: transparent
        -webkit-text-fill-color: transparent
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        background-clip: text
        -webkit-background-clip: text
      span.cursor
        display: inline-block
        width: 0.6rem
        height: 5.5rem
        background: #000
        border-radius: 0.4rem
        margin-left: 0.5rem
        @include fadeOutText
    p.btns
      .btn-gradient
        display: inline-block
        color: #fff
        font-size: 18px
        text-decoration: none
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        border: 0
        border-radius: 8px
        padding: 16px 32px
        margin-top: 0.5rem
  .intro-img
    max-width: 96rem
    text-align: center
    box-shadow: 0 1rem 2rem rgba(0,0,0, 0.3)
    margin: -46rem auto 0
      
  .intro-summary
    display: flex
    justify-content: center
    margin-top: 3rem
    li
      margin-right: 30px
.brochure-wrap
  max-width: 1440px
  margin: 0 auto
  .brochure-1
    margin-top: 10rem
    h3
      font-size: 4.8rem
      text-align: center
      margin-bottom: 6rem
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      padding: 0
      margin: 0
      li
        width: calc( 33% - 13rem )
        font-size: 1.6rem
        padding-left: 7rem
        margin: 0 3rem
        h6
          position: relative
          font-size: 2.4rem
          margin: 0
          z-index: 9
          img
            position: absolute
            left: -7rem
            top: 0
            display: block
            width: 5rem
    p.btns
      margin-top: 3rem
      a
        @extend %btn-radius
      a:hover
        @extend %btn-radius-hover

    .btns
      text-align: center
  .brochure-2
    font-size: 1.6rem
    margin-top: 10rem
    .item
      display: flex
      justify-content: center
      align-items: center
      margin: 2rem 0
      .content
        margin: 0 3rem
        .sub-title
          @extend %sub-title
        h3
          font-size: 4.8rem
          line-height: 1.4
          margin: 3rem 0 
        ul
          list-style: none
          padding: 0 0 0 3rem
          margin: 3rem 0
          li
            position: relative
            margin: 1rem 0
            z-index: 9
            .material-icons
              position: absolute
              left: -3rem
              top: 0.1rem
              font-size: 2rem
              z-index: 99
        .btns
          .btn-link-primary-amination
            color: $text-primary
            text-decoration: none
      .cover
        margin: 0 3rem
        img
          display: block
          width: 52rem
    .item-revert
      .content
        order: 2
      .cover
        order: 1

  .brochure-3
    font-size: 1.6rem
    margin: 10rem
    p.sub-title
      @extend %sub-title
      text-align: center
    h3
      width: 60rem
      font-size: 4.8rem
      font-weight: bold
      text-align: center
      margin: 0 auto
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      li
        display: flex
        flex-direction: column
        justify-content: space-between
        width: calc( 25% - 4rem )
        margin: 5rem 2rem 0 
        h4, p
          margin: 2rem 0 0
        p.img
          margin-top: 0
        p.description
          flex: 1
        .btns
          a
            display: flex
            align-items: center
            color: #262838
            text-decoration: none
            .material-icons
              font-size: 1.6rem
              margin-left: 1rem
</style>