<template>
  <n-card title="naive ui 组件">
    <a
      href="https://www.naiveui.com/zh-CN/os-theme/components/card"
      target="_blank"
      >naive-ui 官网</a
    >
  </n-card>

  <n-card title="http请求示例">
    <p>{{ beck }}</p>
    <p><button @click.prevent="fetchData">发送请求</button></p>
  </n-card>
</template>

<script>
import { NCard } from "naive-ui";
export default {
  name: "Home",
  components: {
    NCard,
  },
  data() {
    return {
      beck: {},
    };
  },
  methods: {
    async fetchData() {
      try {
        const response = await fetch("/api/chat/chatSessions?account=biankai");

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const jsonData = await response.json();
        console.log(jsonData);
        this.beck = jsonData;
      } catch (err) {
        console.log(err.message);
      } finally {
        console.log("");
      }
    },
  },
};
</script>

<style scoped>
.n-card {
  max-width: 300px;
  margin-top: 10px;
}
</style>
