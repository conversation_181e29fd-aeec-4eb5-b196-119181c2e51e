<template>
  <article class="faq-wrap">
    <h2>帮助中心FAQ</h2>
    <section class="content">
      <Accordion :activeIndex="0">
        <AccordionTab header="使用 DevChat 过程中, 常见的概念有哪些 ?">
          <div class="item">
            <p><strong>1 系统角色</strong></p>
            <p>
              系统角色（也称为系统消息），此消息提供模型的初始说明。 可以在系统角色中提供各种信息，包括：
            </p>
            <ul>
              <li>助手的简要说明</li>
              <li>助手的个性特征</li>
              <li>你希望助手遵循的指令或规则</li>
              <li>模型所需的数据或信息</li>
            </ul>
            <p>
              你可以为会话自定义复杂的系统角色，也可以仅包含基本说明。 系统角是可选的，如果不提供，系统会默认使用以下系统角色：
              “你是经销商技术部研发助手，帮助技术同学更高效、更有质量地完成工作。你需要认真地遵循用户的指示并使用 Markdown 进行回答”
            </p>
            <p>
              系统消息的其他一些示例包括：
            </p>
            <ul>
              <li>你是一种智能聊天机器人，旨在帮助用户回答有关 Azure OpenAI 服务的技术问题。 仅使用以下上下文回答问题，如果不确定答案，可以说“我不知道”。</li>
              <li>你是一种智能聊天机器人，旨在帮助用户回答其税务相关问题。</li>
              <li>你是一名助手，旨在从文本中提取实体。 用户将粘贴文本字符串，你将使用从文本中提取的实体作为 JSON 对象进行响应。</li>
            </ul>
            <strong>需要了解的一个重要细节是，即使你在系统角色中指示模型在不确定答案时回答“我不知道”，这并不能保证此请求总是得到履行。
              设计良好的系统角色可以增加产生特定结果的可能性，但仍可能会生成不正确的响应，可能会与系统角色中的指令的意图相矛盾。</strong>
          </div>
          <div class="item">
            <p><strong>2 温度（适用于 ChatGPT）</strong></p>
            <p>"温度"是一个超参数，用于控制模型生成文本的随机性，它的值在 0 到 2 之间。</p>
            <p>如果温度设置得较高（接近 2），模型生成的文本将更具随机性和创造性，但可能会牺牲一些连贯性和准确性。如果温度设置得较低（接近
              0），模型生成的文本将更加确定和一致，但可能会牺牲一些创新性。一般来说虚构的故事可以使用更高的温度生成，而如果要生成法律文件的话，建议使用低得多的温度。</p>
            <p>当温度大于 1 且小于 2 时，GPT 回答内容出现更不可控的内容，越接近 2 越明显，如表达能力变差、语义错误、语法错误、乱码、无意义字符等问题，故建议将温度设置在 0-1 范围内。</p>
          </div>
          <div class="item">
            <p><strong>3 token</strong></p>
            <p>
              模型将文本分解为更小的单元（称为tokens）来处理文本，tokens可以是单词、单词块或单个字符。<br />
              <img src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/pic_faq_token_1.jpg"
                alt="token 分解示例" /> <br />
              常见的单词可以作为一个单独的token，例如cat。不常见的单词会被被拆分为多个token，例如Butterscotch，会被拆分为 “But”, “ters”, “cot”, and
              “ch”。根据粗略的经验，对于英语文本，1 个 token 大约是 4 个字符或 0.75 个单词
            </p>
            <p>
              给一些文本，模型会决定下一个最有可能的token是什么。<br />
              <img src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/pic_faq_token_2.jpg"
                alt="大模型补全 token 示例" />
            </p>
          </div>
          <div class="item">
            <p><strong>4 上下文</strong></p>
            <p>DevChat 中采用“上下文窗口”来设置和 GPT 的对话，GPT 可以理解和记忆之前的对话内容，产生更为自然的回复。上下文可以设置为不开启、开启（滚动记录 1 至 10 条上下文），每条上下文是用户和 GPT
              的一次对话记录。</p>
            <p>上下文传递也需要消耗“token”，并且影响 GPT 推理过程，所以可以按需调整开启的上下文条数。对于新问题，不要携带针对老问题和 GPT 的对话记录。</p>
            <p>当携带的上下文过多时，可能超过模型规定的最大 token 数，导致发送消息报错。</p>
          </div>
          <div class="item">
            <p><strong>5 模型</strong></p>
            <p>DevChat 服务由一组具有不同功能和价位的模型提供支持，当前主要是来自于微软 Azure 提供的模型，未来会增加更多国产和开源模型。模型一般按 token 数（提问 tokens 和回答 tokens
              数量之和）计费，使用中可以根据实际需要选择不同能力和价位的模型来完成任务：</p>
            <DataTable :value="models" showGridlines>
              <Column field="name" header="模型名称"></Column>
              <Column field="desc" header="描述"></Column>
              <Column field="token" header="最大请求（令牌）"></Column>
            </DataTable>
          </div>
        </AccordionTab>
        <AccordionTab header="使用测试环境, 是否可以避免数据安全问题 ?">
          <p>当前测试环境同生产环境一样，底层都是使用的 Azure 模型，服务资源部署在境外，所以安全问题和生产完全一致，并不存在测试环境比生产环境更安全一说。</p>
          <p>为确保信息安全，使用中严禁发送敏感数据，请严格准守使用规范！</p>
          <ul>
            <li>使用 DevChat 务必遵守相关法律法规，不得用于色情、暴力、恐怖、政治等场景，具体可参考 Azure 官方使用规范要求：<a
                href="https://learn.microsoft.com/zh-cn/legal/cognitive-services/openai/code-of-conduct?context=%2Fazure%2Fcognitive-services%2Fopenai%2Fcontext%2Fcontext"
                target="_blank">https://learn.microsoft.com/zh-cn/legal/cognitive-services/openai/code-of-conduct?context=%2Fazure%2Fcognitive-services%2Fopenai%2Fcontext%2Fcontext</a>
            </li>
            <li>
              <p>严禁上传公司相关业务数据、员工信息、代码等，如有疑问请请咨询法务部门，包含但不仅限于：</p>
              <ul>
                <li>不得在 ChatGPT、API 中发布或者分享公司内部信息，如绩效、公司汇报材料、业务数据等内部信息</li>
                <li>不得在 ChatGPT、API 中发布和分享公司内部源代码、产品规划、技术方案等</li>
                <li>不得在 ChatGPT、API中 输入个人与组织隐私数据、避免数据泄露</li>
              </ul>
            </li>
          </ul>
          <p>另外 DevChat 中内置了一些规则，若触发安全规则防护，请大家认真审查自己发送的内容是否包含敏感信息。</p>
        </AccordionTab>
        <AccordionTab header="对话框内容过多时, 该如何处理 ?">
          <p>当消息量过多时，可以选择将当前会话中的消息归档。归档后，消息会移动至归档会话中。
          </p>
        </AccordionTab>

        <AccordionTab header="在 IDEA 插件中, 如何实现免登 ?">
          <p>在 aidev（<a href="https://aidev.corpautohome.com" target="_blank">https://aidev.corpautohome.com</a>
            ）个人设置页面可以查看自己的 IDEA 登录认证码，将其 copy 到 AutoCopilot 设置页面即可</p>
          <p>
            <Image src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/pic_faq_idea_3.png"
              alt="Image" width="400" preview />
          </p>
          <p>
            <Image src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/pic_faq_idea_4.png"
              alt="Image" width="400" preview />
          </p>
        </AccordionTab>
        <AccordionTab header="我怎样可以帮助 AIDEV 变得更好 ?">
          <p>如果您在使用过程中有好的建议, 或是页面操作遇到了问题等, 都可以通过右上角 "问题反馈" , 创建 <a
              href="https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/new"
              target="_blank">Issue</a> 给我们 , 我们会跟进解决, 一起让产品变得更好。</p>
        </AccordionTab>
      </Accordion>
    </section>

  </article>
</template>

<script>
export default {
  name: "Faq",
  components: {
  },
  data() {
    return {
      models: [
        {
          name: 'gpt-4-turbo',
          desc: '最新的GPT-4模型，训练的知识更新至2023年4月，具有JSON模式、可复制的输出、并行函数调用等更多功能。',
          token: '输入：128,000；输出：4096'
        },
        {
          name: 'gpt-4',
          desc: 'DevChat 中最强模型，具有广泛的常识和先进的推理能力，比 gpt-3.5-turbo 模型能够更准确地解决难题',
          token: '8,192'
        },
        {
          name: 'gpt-3.5-turbo',
          desc: '可以理解和生成自然语言或代码。推理能力不如 gpt-4',
          token: '4,096'
        },
        {
          name: 'dalle2',
          desc: '基于用户提供的文本提示生成图像',
          token: '-'
        },
      ]
    };
  },
  methods: {
    async init() {
    },
  },
};
</script>

<style scoped lang="sass">
.faq-wrap
  max-width: 1370px
  background-color: #f1f5fb
  border-radius: 3.2rem
  padding: 3.5rem 8rem 6rem
  margin: 0 auto
  h2
    font-size: 3rem
    text-align: center
    margin: 0
  .content
    width: 100%
    margin: 3rem  auto 0

    .p-accordion .p-accordion-tab
// 覆写 Accordion 组件样式
::v-deep .p-accordion
  .p-accordion-tab
    box-shadow: none
    margin-bottom: 4px
    .p-accordion-header
      a
        font-weight: bold
  .p-accordion-tab:first-child
    .p-accordion-header 
      .p-accordion-header-link
        border-top-right-radius: 12px
        border-top-left-radius: 12px
  .p-accordion-tab:last-child 
    .p-accordion-header:not(.p-highlight) 
      .p-accordion-header-link
        border-bottom-right-radius: 12px
        border-bottom-left-radius: 12px
</style>
