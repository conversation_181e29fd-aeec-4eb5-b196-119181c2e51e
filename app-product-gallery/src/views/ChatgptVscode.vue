<template>
  <section class="banner">
    <div class="intro-text">
      <p class="sub-title">AutoCopilot VSCode插件让你更高效</p>
      <h2>沉浸式智能代码助手</h2>
      <p class="typed">
        <span class="description">{{ banner.descActive }}</span>
        <span class="cursor"></span>
      </p>
      <p class="btns">
        <a target="_blank" class="btn-gradient" :href="downloadUrl">下载插件</a>
      </p>
    </div>
    <div class="intro-img">
      <video-player fluid src="/static-bucket/video/VSCode%20Install.mov" controls muted />
    </div>
  </section>

  <div class="brochure-wrap">
    <section class="brochure-1" rel="fadeup">
      <h3>{{ brochure1.title }}</h3>
      <ul>
        <li v-for="item in brochure1.items" :key="item.title">
          <h6>
            <span>{{ item.title }}</span>
            <img :src="item.icon" alt="">
          </h6>
          <p>{{ item.desc }}</p>
        </li>
      </ul>
      <p class="btns"><a target="_blank" :href="brochure1.btn.link">{{ brochure1.btn.text }}</a></p>
    </section>
    <section class="brochure-2">
      <div class="item" v-for="item in brochure2.items" :key="item.cover" :class="item.className" rel="fadeup">
        <div class="content">
          <p class="sub-title">{{ item.content.subTitle }}</p>
          <h3>{{ item.content.title }}</h3>
          <p>{{ item.content.desc }}</p>
          <ul v-if="item.content.features">
            <li v-for="feature in item.content.features" :key="feature"><i class="material-icons">done</i>
              <span>{{ feature }}</span>
            </li>
          </ul>
          <p class="btns">
            <a target="_blank" v-for="btn in item.content.btns" :key="btn.link" class="btn-link-primary-amination"
              :href="btn.link">{{
                btn.text
              }}</a>
          </p>
        </div>
        <div class="cover"><img :src="item.cover" alt="img"></div>
      </div>
    </section>
    <section class="brochure-3" rel="fadeup">
      <p class="sub-title">{{ brochure3.subTitle }}</p>
      <h3>{{ brochure3.title }}</h3>
      <ul>
        <li v-for="item in brochure3.items" :key="item.title" rel="fadeup">
          <p class="img"><img :src="item.icon" alt="icon"></p>
          <h4>{{ item.title }}</h4>
          <p class="description">{{ item.description }}</p>
          <!-- <p class="btns"><a :href="item.btn.link">
              <span>{{ item.btn.text }}</span>
              <i class="material-icons">
                north_east
              </i>
            </a></p> -->
        </li>
      </ul>
    </section>
  </div>
</template>

<script>
let timer1, timer2;

function mergeDownloadUrl(version) {
  return `/static-bucket/vscode/auto-copilot-${version}.vsix`
}

export default {
  name: "ChatgptVscode",
  data() {
    return {
      downloadUrl: mergeDownloadUrl('0.2.2'),
      banner: {
        descActive: 'ChatGPT VSCode深度定制，结队编程好伙伴',
        descAnimation: false,
        desc: [
          '为您打造高效便捷的编程环境',
          '打开您的便捷编程新篇章',
          '集常用功能于一身',
          'ChatGPT VSCode深度定制，结队编程好伙伴'
        ]
      },
      brochure1: {
        title: '让Coding更智能更便捷',
        items: [
          {
            title: '常规操作一键集成',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon1.png',
            desc: '集常用功能于一身，为您的编程之旅注入新活力，使工作和学习成为一种享受。'
          },
          {
            title: '设置灵活',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon2.png',
            desc: '为您打造高效便捷的编程环境，让每一个与GPT交互都可以定制。'
          },
          {
            title: '便捷操作',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon3.png',
            desc: '在追求极致便捷操作的道路上，我们与您并肩前行。'
          }
        ],
        btn: {
          text: '了解更多',
          link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw5ssXGj2'
        }
      },
      brochure2: {
        items: [
          {
            content: {
              subTitle: '常用功能一键操作',
              title: '提升效率，优化体验',
              desc: '为开发者们带来更高效、更便捷的编程体验，我们设计了一款全新的VSCode插件。它不仅可以帮助您有效管理复杂的代码逻辑，而且还能为您节省大量的时间和精力，实现一键式自动操作。',
              features: [
                '生成测试：只需一击，即可自动生成冗长的测试代码，让严谨性与效率并驾齐驱。',
                '解释代码：自动解读复杂的代码逻辑，让代码的本质清晰呈现于眼前。',
                '重构：快速优化代码结构，提升软件稳定性，推动项目更上一层楼。',
                '查找问题：准确快速地定位代码中的问题，让编程过程更加流畅。',
                '优化代码：通过代码优化，使您的程序运行得更加顺滑而强大。',
                '编辑文档：辅助您编写出简洁明了的用户文档，让信息传递更直接，使用体验更出色。',
                '选中代码提问：对某段代码有疑问？只需一键选择，即可向我们的专家系统提问，获取及时的答案和建议。',
              ],
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw5ssXGj2'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover1.jpg'
          },
          {
            className: 'item-revert',
            content: {
              subTitle: '深度定制每一个细节',
              title: '为你量身打造个性化的编程环境',
              desc: '我们的目标就是提供一个灵活、高效的编程环境，帮助您更好地发挥个人能力，尽可能地提高工作效率。无论您是一个新手还是资深开发者，通过我们精心设计的界面及功能，都能找到最符合自己习惯的工作方式。',
              features: [
                '提供默认prompt：我们提供一系列实用的预设提示，帮助您在没有明确方向时，能迅速定位并开始工作。',
                '自定义常规则操作prompt：每个人的工作风格都是独特的。因此，我们的插件允许您根据自己的工作习惯和需求设置自定义的操作提示，使得日常任务变得更加流畅和自然。',
              ],
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw5ssXGj2'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover2.jpg'
          },
          {
            content: {
              subTitle: '操作更简单',
              title: '高效编程小细节',
              desc: '精心打磨细节，让日常操作更简单。',
              features: [
                '一键导入代码到编辑区域：我们知道每一次点击都是宝贵的时间。于是我们的插件提供了一键导入代码功能，让复制粘贴成为过去，让您更专注于真正重要的创造活动。',
                '重新生成答案：不满意？没问题，只需单击一下，插件立即为您生成全新的答案，让满意的解决方案始终在手边。',
                '通过上下键追窗口历史：忘记了前面的内容？别担心，简单的上下键操作就能轻松浏览窗口历史，无论何时需要，信息都能够迅速地回到您眼前。',
                '……'
              ],
              btns: [
                {
                  text: '立即使用',
                  link: 'https://doc.autohome.com.cn/docapi/page/share/share_pmw5ssXGj2'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover3.jpg'
          }
        ],
      },
      brochure3: {
        subTitle: '用户案例',
        title: '生成测试、解释复杂代码、快速重构、精准问题查找',
        items: [
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon4.png',
            title: '一键生成测试',
            description: '一键生成测试代码，让严谨性与效率并驾齐驱。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon5.png',
            title: '解释复杂代码',
            description: '自动解读代码逻辑，让复杂的本质清晰呈现。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon6.png',
            title: '快速重构',
            description: '优化重构代码，推动软件项目更上一层楼。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon7.png',
            title: '精准问题查找',
            description: '精准定位代码问题，使编程过程流畅无阻。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon8.png',
            title: '代码优化器',
            description: '代码优化功能，为程序的运行注入力量。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon9.png',
            title: '文档编辑助手',
            description: '编辑简洁用户文档，使信息传递更直观。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon10.png',
            title: '选中代码提问',
            description: '选中代码提问，及时获取专家答案和建议。',
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon11.png',
            title: '预设提示功能',
            description: '实用预设提示，帮助您快速开始工作。',
          }
        ]
      }
    }
  },
  created() {
    this.upgradeVersion()
  },
  methods: {
    async upgradeVersion() {
      const response = await fetch("/design-api/vscode/version?_appId=vscode");
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      const versionData = await response.json();
      if (versionData.returncode === 0 && versionData.result) {
        this.downloadUrl = mergeDownloadUrl(versionData.result)
      }
    },
    init() {
      // 打印机打字动画
      const duration = 4000;
      let index = 0;
      const desc = this.banner.desc;
      timer1 = setInterval(() => {
        this.banner.descActive = '';
        const text = desc[index % desc.length]
        this.printText(text, duration)
        index++;
      }, duration)
      // [模块动画] 升起 + 渐隐渐出
      window.addEventListener('scroll', this.checkScroll);
    },

    printText(text, duration) {
      console.log(text, duration)
      let i = 0;
      let len = text.length;

      timer2 = setInterval(() => {
        if (i < len) {
          this.banner.descActive = `${this.banner.descActive}${text[i]}`;
          i++;
        } else {
          clearInterval(timer2)
        }
      }, Math.floor((duration - 2000) / len));

    },
    checkScroll() {
      const elements = document.querySelectorAll('[rel="fadeup"]');
      elements.forEach((ele) => {
        const position = ele.getBoundingClientRect();
        if (position.top <= window.innerHeight) {
          ele.classList.add("fadeup-active");
        }
      });
    },
    // 修复当用户切换tab时, setInterval 不准确的问题
    // Solution : https://usefulangle.com/post/280/settimeout-setinterval-on-inactive-tab
    fixSetIntervalInactive() {
      if (document.hidden) {
        // 页面被切走, 清除计时器
        clearInterval(timer2);
        clearInterval(timer1);
        this.banner.descActive = this.banner.desc[this.banner.desc.length - 1]
      } else {
        // 页面切换回来, 重新设定计时器
        this.init();
        console.log('open')
      }
    }
  },
  mounted() {
    this.init();
    document.addEventListener("visibilitychange", this.fixSetIntervalInactive);
  },
  beforeUnmount() {
    clearInterval(timer2);
    clearInterval(timer1);
    // [模块动画] 升起 + 渐隐渐出
    window.removeEventListener('scroll', this.checkScroll);
    document.removeEventListener("visibilitychange", this.fixSetIntervalInactive);
  }
};
</script>
<style lang="sass" scoped>
$btn-bg: #6a4dff
$btn-hover-bg: #5a41d9
$text-primary: #6a4dff

%btn-radius
  display: inline-block
  color: #fff
  font-size: 1.6rem
  font-weight: bold
  text-align: center
  background-color: $btn-bg
  border-radius: 6px
  text-decoration: none
  padding: 1.2rem 2.4rem
%btn-radius-hover
  background-color: $btn-hover-bg

%sub-title
  font-size: 1.6rem
  color: $text-primary

%btn-link-primary
  font-size: 1.6rem
  color: $text-primary

@keyframes fadeout
  0% 
    opacity: 1
  100% 
    opacity: 0
@mixin fadeOutText
  animation-name: fadeout
  animation-duration: 0.7s
  animation-iteration-count: infinite

@keyframes fadeup
  0% 
    opacity: 0
    transform: translateY(30px)
  100% 
    opacity: 1
    transform: translateY(0px)
@mixin fadeupElement
  animation-name: fadeup
  animation-duration: 0.6s
  animation-timing-function: ease-in

.fadeup-active
  @include fadeupElement
// *[rel="fadeup"]
//   opacity: 0

.banner
  .intro-text
    text-align: center
    height: 78rem
    background: url('https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_app_product_gallery_banner_bg.jpg') no-repeat center center
    background-size: cover
    padding-top: 2rem
    p.sub-title
      @extend %sub-title
      text-align: center
      margin-bottom: 1rem
    h2
      font-size: 48px
      margin: 0
    p.typed
      display: flex
      justify-content: center
      align-items: center
      font-size: 48px
      font-weight: bold
      height: 7.7rem
      margin: -1.5rem 0 0
      span.description
        text-fill-color: transparent
        -webkit-text-fill-color: transparent
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        background-clip: text
        -webkit-background-clip: text
      span.cursor
        display: inline-block
        width: 0.6rem
        height: 5.5rem
        background: #000
        border-radius: 0.4rem
        margin-left: 0.5rem
        @include fadeOutText
    p.btns
      .btn-gradient
        display: inline-block
        color: #fff
        font-size: 18px
        text-decoration: none
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        border: 0
        border-radius: 8px
        padding: 16px 32px
        margin-top: 0.5rem
  .intro-img
    max-width: 96rem
    text-align: center
    box-shadow: 0 1rem 2rem rgba(0,0,0, 0.3)
    margin: -46rem auto 0
    video
      border-radius: 16px
    .video-js
      background-color: transparent
  .intro-summary
    display: flex
    justify-content: center
    margin-top: 3rem
    li
      margin-right: 30px
.brochure-wrap
  max-width: 1440px
  margin: 0 auto
  .brochure-1
    margin-top: 10rem
    h3
      font-size: 4.8rem
      text-align: center
      margin-bottom: 6rem
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      padding: 0
      margin: 0
      li
        width: calc( 33% - 13rem )
        font-size: 1.6rem
        padding-left: 7rem
        margin: 0 3rem
        h6
          position: relative
          font-size: 2.4rem
          margin: 0
          z-index: 9
          img
            position: absolute
            left: -7rem
            top: 0
            display: block
            width: 5rem
    p.btns
      margin-top: 3rem
      a
        @extend %btn-radius
      a:hover
        @extend %btn-radius-hover

    .btns
      text-align: center
  .brochure-2
    font-size: 1.6rem
    margin-top: 10rem
    .item
      display: flex
      justify-content: center
      align-items: center
      margin: 2rem 0
      .content
        margin: 0 3rem
        .sub-title
          @extend %sub-title
        h3
          font-size: 4.8rem
          line-height: 1.4
          margin: 3rem 0 
        ul
          list-style: none
          padding: 0 0 0 3rem
          margin: 3rem 0
          li
            position: relative
            margin: 1rem 0
            z-index: 9
            .material-icons
              position: absolute
              left: -3rem
              top: 0.1rem
              font-size: 2rem
              z-index: 99
        .btns
          .btn-link-primary-amination
            position: relative
            display: flex
            align-items: center
            color: $text-primary
            text-decoration: none
            transition: all .2s ease-in-out
      .cover
        margin: 0 3rem
        img
          display: block
          width: 52rem
    .item-revert
      .content
        order: 2
      .cover
        order: 1

  .brochure-3
    font-size: 1.6rem
    margin: 10rem 0
    p.sub-title
      @extend %sub-title
      text-align: center
    h3
      width: 60rem
      font-size: 4.8rem
      font-weight: bold
      text-align: center
      margin: 0 auto
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      li
        display: flex
        flex-direction: column
        justify-content: space-between
        width: calc( 25% - 4rem )
        margin: 5rem 2rem 0 
        h4, p
          margin: 2rem 0 0
        p.img
          margin-top: 0
        p.description
          flex: 1
        .btns
          a
            display: flex
            align-items: center
            color: #262838
            text-decoration: none
            .material-icons
              font-size: 1.6rem
              margin-left: 1rem
</style>

