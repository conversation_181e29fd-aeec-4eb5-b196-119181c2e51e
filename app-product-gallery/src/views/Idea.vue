<template>
  <section class="banner">
    <div class="intro-text">
      <p class="sub-title">最佳人工智能文案工具</p>
      <h2>10倍快速写作方法</h2>
      <p class="typed">
        <span class="description">{{ banner.descActive }}</span>
        <span class="cursor"></span>
      </p>
      <p class="btns">
        <a class="btn-gradient" href="/app-product-gallery/idea">立即使用</a>
      </p>
    </div>
    <div class="intro-img">
      <video poster="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_app_product_gallery_idea.jpg" controls
        muted>
        <source src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-product-gallery/plugin-idea.mp4"
          type="video/mp4">
        您的浏览器不支持HTML5 video。
      </video>
    </div>
    <ul class="intro-summary">
      <li>辅助代码设计</li>
      <li>编写单元测试</li>
      <li>提升代码质量</li>
    </ul>
  </section>

  <div class="brochure-wrap">
    <section class="brochure-1" rel="fadeup">
      <h3>{{ brochure1.title }}</h3>
      <ul>
        <li v-for="item in brochure1.items" :key="item.title">
          <h6>
            <span>{{ item.title }}</span>
            <img :src="item.icon" alt="">
          </h6>
          <p>{{ item.desc }}</p>
        </li>
      </ul>
      <p class="btns"><a :href="brochure1.btn.link">{{ brochure1.btn.text }}</a></p>
    </section>

    <section class="brochure-2" rel="fadeup">
      <div class="item" v-for="item in brochure2.items" :key="item.cover" :class="item.className" rel="fadeup">
        <div class="content">
          <p class="sub-title">{{ item.content.subTitle }}</p>
          <h3>{{ item.content.title }}</h3>
          <p>{{ item.content.desc }}</p>
          <ul v-if="item.content.features">
            <li v-for="feature in item.content.features" :key="feature">
              <i class="material-icons">done</i>
              <span>{{ feature }}</span>
            </li>
          </ul>
          <p class="btns">
            <a v-for="btn in item.content.btns" :key="btn.link" class="btn-link-primary-amination" :href="btn.link">{{
              btn.text
            }}</a>
          </p>
        </div>
        <div class="cover"><img :src="item.cover" alt="img"></div>
      </div>
    </section>

    <section class="brochure-3" rel="fadeup">
      <p class="sub-title">{{ brochure3.subTitle }}</p>
      <h3>{{ brochure3.title }}</h3>
      <ul>
        <li v-for="item in brochure3.items" :key="item.title">
          <p class="img"><img :src="item.icon" alt="icon"></p>
          <h4>{{ item.title }}</h4>
          <p class="description">{{ item.description }}</p>
          <p class="btns"><a :href="item.btn.link">
              <span>{{ item.btn.text }}</span>
              <i class="material-icons">
                north_east
              </i>
            </a></p>
        </li>
      </ul>
    </section>
  </div>
</template>

<script>
export default {
  name: "Idea",
  data() {
    return {
      banner: {
        descActive: '让我们用一次点击创建ai驱动的内容',
        descAnimation: false,
        desc: [
          '一个神奇的工具来优化你的内容的第一页排名',
          '用GenAI写博客，故事，甚至是书',
          '让我们用一次点击创建ai驱动的内容'
        ]
      },
      brochure1: {
        title: '跨越你最喜欢的工具',
        items: [
          {
            title: '生成优质内容',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon1.png',
            desc: '这个工具可以帮助你找到合适的关键词来定位你的内容。通过使用谷歌关键字规划师，你可以看到人们多久'
          },
          {
            title: '提供有用的建议',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon2.png',
            desc: 'GenAI写作工具可以分析数据并产生见解，以帮助作者创建更引人注目和信息丰富的内容。'
          },
          {
            title: '提高产品生产效率',
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon3.png',
            desc: '情感是广告的有力工具。使用与你的听众产生共鸣的情感来建立联系。'
          }
        ],
        btn: {
          text: 'Try a Demo',
          link: '/app-product-gallery/idea'
        }
      },
      brochure2: {
        items: [
          {
            content: {
              subTitle: '功能1',
              title: '让我们用一次点击创建ai驱动的内容。',
              desc: '要创建强大的内容，你需要知道你的目标用户是谁。确定你的目标受众，了解他们的需求、兴趣和痛点。',
              features: [
                '开始创建强大的内容，为您的下一个广告', '让我们用情感与客户沟通', '使用图像、视频或信息图表等视觉效果使你的内容更吸引人',
              ],
              btns: [
                {
                  text: '免费开始',
                  link: '/app-product-gallery/idea'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover1.jpg'
          },
          {
            className: 'item-revert',
            content: {
              subTitle: '功能2',
              title: '一个神奇的工具来优化你的内容的第一页排名',
              desc: '一个神奇的工具来优化你的内容，首先知道你的目标是谁。确定你的目标受众，了解他们的需求、兴趣和痛点。 ',
              btns: [
                {
                  text: '要求一个演示',
                  link: '/app-product-gallery/idea'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover2.jpg'
          },
          {
            content: {
              subTitle: '功能3',
              title: '用GenAI写博客，故事，甚至是书。',
              desc: '要创建强大的内容，你需要知道你的目标用户是谁。确定你的目标受众，了解他们的需求、兴趣和痛点。',
              features: [
                '开始创建强大的内容，为您的下一个广告', '让我们用情感与客户沟通', '使用图像、视频或信息图表等视觉效果使你的内容更吸引人',
              ],
              btns: [
                {
                  text: '免费开始',
                  link: '/app-product-gallery/idea'
                }
              ],
            },
            cover: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/img-products/pic_cover3.jpg'
          }
        ],
      },
      brochure3: {
        subTitle: '用户案例',
        title: '更快地写出更好的内容，AI写作工具的未来就在这里',
        items: [
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon4.png',
            title: '撰写博客内容',
            description: '使用GenAI编写博客内容，请确保您清楚地了解您的受众是谁。',
            btn: {
              text: '尝试博客内容',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon5.png',
            title: '数字广告文案',
            description: '一个神奇的工具来优化你的内容，首先知道你的目标是谁。确定你的目标。',
            btn: {
              text: '尝试数字广告',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon6.png',
            title: '网站上复制',
            description: '优化你的内容，首先知道你的目标是谁。确定你的目标受众。',
            btn: {
              text: '尝试网站复制',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon7.png',
            title: '社交媒体内容',
            description: '首先要知道你的目标是谁。确定你的目标受众，了解他们的需求。',
            btn: {
              text: '尝试社交媒体内容',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon8.png',
            title: '登陆页副本',
            description: '首先要知道你的目标是谁。确定你的目标受众，了解他们的需求。',
            btn: {
              text: '试着复制登陆页',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon9.png',
            title: '营销拷贝',
            description: '一个神奇的工具来优化你的内容，首先知道你的目标是谁。确定你的目标。',
            btn: {
              text: '尝试营销文案',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon10.png',
            title: '电子商务副本',
            description: '使用GenAI编写博客内容，请确保您清楚地了解您的受众是谁。',
            btn: {
              text: '尝试电子商务复制',
              link: '/app-product-gallery/idea'
            }
          },
          {
            icon: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/icons-products/pic_app_product_gallery_icon11.png',
            title: '产品描述',
            description: '优化你的内容，首先知道你的目标是谁。确定你的目标受众。',
            btn: {
              text: '试用产品说明',
              link: '/app-product-gallery/idea'
            }
          }
        ]
      }
    }
  },
  methods: {
    init() {
      // 打印机打字动画
      const duration = 4000;
      let index = 0;
      const desc = this.banner.desc;
      setInterval(() => {
        this.banner.descActive = '';
        const text = desc[index % desc.length]
        this.printText(text, duration)
        index++;
      }, duration)
      // [模块动画] 升起 + 渐隐渐出
      window.addEventListener('scroll', this.checkScroll);
    },

    printText(text, duration) {
      console.log(text, duration)
      let i = 0;
      let len = text.length;

      const timer = setInterval(() => {
        if (i < len) {
          this.banner.descActive = `${this.banner.descActive}${text[i]}`;
          i++;
        } else {
          clearInterval(timer)
        }
      }, Math.floor((duration - 2000) / len));

    },

    checkScroll() {
      const elements = document.querySelectorAll('[rel="fadeup"]');
      elements.forEach((ele) => {
        const position = ele.getBoundingClientRect();
        if (position.top <= window.innerHeight) {
          ele.classList.add("fadeup-active");
        }
      });
    }
  },
  mounted() {
    this.init();
  },
  unmounted() {
    // [模块动画] 升起 + 渐隐渐出
    window.removeEventListener('scroll', this.checkScroll);
  }
};

</script>
<style lang="sass" scoped>
$btn-bg: #6a4dff
$btn-hover-bg: #5a41d9
$text-primary: #6a4dff

%btn-radius
  display: inline-block
  color: #fff
  font-size: 1.6rem
  font-weight: bold
  text-align: center
  background-color: $btn-bg
  border-radius: 6px
  text-decoration: none
  padding: 1.2rem 2.4rem
%btn-radius-hover
  background-color: $btn-hover-bg

%sub-title
  font-size: 1.6rem
  color: $text-primary

%btn-link-primary
  font-size: 1.6rem
  color: $text-primary

@keyframes fadeout
  0% 
    opacity: 1
  100% 
    opacity: 0
@mixin fadeOutText
  animation-name: fadeout
  animation-duration: 0.7s
  animation-iteration-count: infinite

@keyframes fadeup
  0% 
    opacity: 0
    transform: translateY(30px)
  100% 
    opacity: 1
    transform: translateY(0px)
@mixin fadeupElement
  animation-name: fadeup
  animation-duration: 0.6s
  animation-timing-function: ease-in

.fadeup-active
  @include fadeupElement
// *[rel="fadeup"]
//   opacity: 0

.banner
  .intro-text
    text-align: center
    height: 78rem
    background: url('https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_app_product_gallery_banner_bg.jpg') no-repeat 0 0
    padding-top: 2rem
    p.sub-title
      @extend %sub-title
      text-align: center
      margin-bottom: 1rem
    h2
      font-size: 48px
      margin: 0
    p.typed
      display: flex
      justify-content: center
      align-items: center
      font-size: 48px
      font-weight: bold
      height: 7.7rem
      margin: -1.5rem 0 0
      span.description
        text-fill-color: transparent
        -webkit-text-fill-color: transparent
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        background-clip: text
        -webkit-background-clip: text
      span.cursor
        display: inline-block
        width: 0.6rem
        height: 5.5rem
        background: #000
        border-radius: 0.4rem
        margin-left: 0.5rem
        @include fadeOutText
    p.btns
      .btn-gradient
        display: inline-block
        color: #fff
        font-size: 18px
        text-decoration: none
        background: linear-gradient(92.73deg,#b324d7 7.07%,#fe577f 97.81%)
        border: 0
        border-radius: 8px
        padding: 16px 32px
        margin-top: 0.5rem
  .intro-img
    max-width: 96rem
    margin: -46rem auto 0
    img, video
      display: block
      width: 100%
      border: 1px solid $text-primary
      border-radius: 16px
      box-shadow: 0 1rem 3rem rgba(0,0,0,.175) 
  .intro-summary
    display: flex
    justify-content: center
    margin-top: 3rem
    li
      margin-right: 30px
.brochure-wrap
  max-width: 1440px
  margin: 0 auto
  .brochure-1
    margin-top: 10rem
    h3
      font-size: 4.8rem
      text-align: center
      margin-bottom: 6rem
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      padding: 0
      margin: 0
      li
        width: calc( 33% - 13rem )
        font-size: 1.6rem
        padding-left: 7rem
        margin: 0 3rem
        h6
          position: relative
          font-size: 2.4rem
          margin: 0
          z-index: 9
          img
            position: absolute
            left: -7rem
            top: 0
            display: block
            width: 5rem
    p.btns
      margin-top: 3rem
      a
        @extend %btn-radius
      a:hover
        @extend %btn-radius-hover

    .btns
      text-align: center
  .brochure-2
    font-size: 1.6rem
    margin-top: 10rem
    .item
      display: flex
      justify-content: center
      align-items: center
      margin: 2rem 0
      .content
        margin: 0 3rem
        .sub-title
          @extend %sub-title
        h3
          font-size: 4.8rem
          line-height: 1.4
          margin: 3rem 0 
        ul
          list-style: none
          padding: 0 0 0 3rem
          margin: 3rem 0
          li
            position: relative
            margin: 1rem 0
            z-index: 9
            .material-icons
              position: absolute
              left: -3rem
              top: 0.1rem
              font-size: 2rem
              z-index: 99
        .btns
          .btn-link-primary-amination
            position: relative
            display: flex
            align-items: center
            color: $text-primary
            text-decoration: none
            transition: all .2s ease-in-out
      .cover
        margin: 0 3rem
        img
          display: block
          width: 52rem
    .item-revert
      .content
        order: 2
      .cover
        order: 1

  .brochure-3
    font-size: 1.6rem
    margin-top: 20rem
    p.sub-title
      @extend %sub-title
      text-align: center
    h3
      width: 60rem
      font-size: 4.8rem
      font-weight: bold
      text-align: center
      margin: 0 auto
    ul
      display: flex
      flex-wrap: wrap
      list-style: none
      li
        display: flex
        flex-direction: column
        justify-content: space-between
        width: calc( 25% - 4rem )
        margin: 5rem 2rem 0 
        h4, p
          margin: 2rem 0 0
        p.img
          margin-top: 0
        p.description
          flex: 1
        .btns
          a
            display: flex
            align-items: center
            color: #262838
            text-decoration: none
            .material-icons
              font-size: 1.6rem
              margin-left: 1rem
</style>


