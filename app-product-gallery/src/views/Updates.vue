<template>
  <article class="updates-wrap">
    <h2 class="title">更新日志</h2>
    <section class="content">
      <Card>
        <template #content>
          <div class="markdown-body" v-html="renderMarkdown(contentMD)"></div>
        </template>
      </Card>
    </section>
  </article>
</template>

<script>
import MarkdownIt from "markdown-it";
const md = new MarkdownIt();

import { httpService } from "@dealer/utility-lib";

export default {
  name: "Updates",
  components: {
  },
  data() {
    return {
      contentMD: ""
    };
  },
  methods: {
    async init() {
      //  api  /strapi-api/update-page
    },
    renderMarkdown(markdownContent) {
      return md.render(markdownContent);
    },
  },
  async mounted() {
    const data = await httpService.get('/strapi-api/update-page');
    this.contentMD = data.data.attributes.contentMD;
  }
};
</script>

<style lang="sass">
.updates-wrap
  max-width: 1370px
  background-color: #f1f5fb
  border-radius: 3.2rem
  padding: 3.5rem 8rem 6rem
  margin: 0 auto
  .title
    font-size: 3rem
    text-align: center
    margin: 0
  .content
    width: 100%
    margin: 3rem  auto 0
    .p-card
      border-radius: 1.6rem
    .markdown-body
      padding: 0 2rem
      img
        display: block
        max-width: 100%
        border-radius: 0.5rem
        box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px
</style>
