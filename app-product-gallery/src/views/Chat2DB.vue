<template>
  <article>
    <product-intro
      v-for="product in products"
      :key="product.reverse"
      :reverse="product.reverse"
      :data="product.data"
    />
  </article>
</template>

<script>
import ProductIntro from "@/components/ProductIntro.vue";
export default {
  name: "Chat2DB",
  components: {
    ProductIntro,
  },
  data() {
    return {
      products: [
        {
          reverse: false,
          data: {
            cover: {
              img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_product_autoCopilo.gif",
              video: "",
            },
            content: {
              title: "代码生成 - AutoCopilo 功能介绍",
              desc: "根据需求描述文档生成项目代码 : 选中文件（需求说明文档）右键->AutoCopilo->生成代码 【注意】中间过程已省略 ",
            },
          },
        },
        {
          reverse: true,
          data: {
            cover: {
              img: "https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/pic_product_autoCopilo_result.png",
              video: "",
            },
            content: {
              title: "AutoCopilo 生成产物",
              desc: "生成产物如图所示",
              buttons: [
                // {
                //   className: "btn-primary",
                //   text: "下载体验",
                //   href: "https://sd.autohome.com.cn/uploader/f/yImSqkLzD7Rs3fCh.zip?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE2OTMzODEwOTgsImZpbGVHVUlEIjoiT1p6d2sxcXd5dWRZNHhPRSIsImlhdCI6MTY5MzI5NDY5OCwiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjoxMDAwMDAxNzc1MX0.m08pFKOCFscMhHGxL0eKpzbmcy4yZVUpxp8BWBiQWek&download=",
                // },
                {
                  className: "btn-secondary",
                  text: "使用文档",
                  href: "https://doc.autohome.com.cn/docapi/page/share/share_oTgunxlCJU",
                },
              ],
            },
          },
        },
      ],
    };
  },
};
</script>
