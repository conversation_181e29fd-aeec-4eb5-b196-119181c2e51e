<template>
  <div class="app-product-gallery-wrap">
    <!-- <nav>
      <router-link to="/">Home</router-link>
      <router-link to="/about">About</router-link>
    </nav> -->
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: "App",
  components: {},
};
</script>

<style lang="sass">
.app-product-gallery-wrap
  height: calc(100vh - 60px)
  overflow-y: scroll
  -webkit-font-smoothing: antialiased
  -moz-osx-font-smoothing: grayscale
  color: #2c3e50
  .product-card
    display: flex
    justify-content: space-between
    align-items: center
    background-color: #fff
    border-radius: 4px
    box-shadow: 0 3px 10px rgba(62,85,120,0.3)
    padding: 70px
    margin: 0 auto 40px
    .cover
      display: flex
      justify-content: center
      align-items: center
      flex: 2
      margin-right: 40px
      a.a-img
        cursor: zoom-in
      img, video
        display: block
        width: 100%
        background-color: #fff
        border-radius: 4px
        box-shadow: 0 3px 10px rgba(62,85,120,0.5)
    .content
      flex: 1
      .content-inner
        transform: translateX(0)
        opacity: 1
        transition: all .4s ease-in-out
        transition-delay: 0.1s
        h3
          margin-top: 0
        p
          white-space: pre-wrap
          margin: 40px 0
    .content.hide
      .content-inner
        transform: translateX(-60px)
        opacity: 0
  .product-card-reverse
    .cover
      order:2
      margin-left: 40px
      margin-right: 0
    .content
      order:1
</style>
