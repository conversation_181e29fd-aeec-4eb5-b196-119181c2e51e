<template>
  <section class="product-card" :class="{ 'product-card-reverse': reverse }">
    <div class="cover">
      <a class="a-img" v-if="data.cover.img" :href="data.cover.img" target="_blank">
        <img :src="data.cover.img" />
      </a>
      <video v-if="data.cover.video" :src="data.cover.video" autoplay controls muted></video>
    </div>
    <div class="content">
      <div class="content-inner hide">
        <h3>{{ data.content.title }}</h3>
        <p>
          {{ data.content.desc }}
        </p>
        <div v-if="data.content.buttons" class="btns">
          <a target="_blank" class="btn" v-for="btn in data.content.buttons" :key="btn.text" :href="btn.href"
            :class="btn.className">{{ btn.text }}</a>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: "ProductIntro",
  props: {
    reverse: {
      type: Boolean,
      required: false,
      default: false,
    },
    data: {
      type: Object,
      required: false,
    },
  },
  mounted() {
    this.animationByScroll();
    window.addEventListener("scroll", this.animationByScroll);
  },
  methods: {
    animationByScroll() {
      const elements = document.querySelectorAll(".product-card .content-inner");
      elements.forEach((ele) => {
        const position = ele.getBoundingClientRect();
        if (position.top <= window.innerHeight - 100 && position.bottom >= 0) {
          ele.classList.remove("hide");
        } else {
          ele.classList.add("hide");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btn {
  display: inline-block;
  color: #212529;
  cursor: pointer;
  font-size: 13px;
  font-weight: bold;
  text-align: center;
  vertical-align: middle;
  line-height: 1.5;
  text-decoration: none;
  background-color: transparent;
  border: none;
  border-radius: 0.25rem;
  padding: 6px 10px;
  margin-right: 20px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn.btn-primary {
  background: #037afb;
}

.btn.btn-primary:hover {
  background: #1c89ff !important;
}

.btn.btn-primary.active,
.btn.btn-primary:active,
.btn.btn-primary:focus {
  background: #1c89ff !important;
  box-shadow: 0 0 0 0.2rem rgba(3, 122, 251, 0.25);
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn.btn-secondary {
  background: #e6e6e6;
  color: #384c6d;
}

.btn.btn-secondary:hover {
  background: #dbdbdb !important;
  color: #384c6d !important;
}

.btn.btn-secondary.active,
.btn.btn-secondary:active,
.btn.btn-secondary:focus {
  background: #dbdbdb !important;
  box-shadow: 0 0 0 0.2rem rgba(230, 230, 230, 0.25);
}
</style>
