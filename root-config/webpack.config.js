const { merge } = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = (webpackConfigEnv, argv) => {
  const ACCOUNT_NAME = 'zhaixiaowei';
  const orgName = 'dealer';
  const defaultConfig = singleSpaDefaults({
    orgName,
    projectName: 'root-config',
    webpackConfigEnv,
    argv,
    disableHtmlGeneration: true,
  });

  return merge(defaultConfig, {
    // Mock 接口数据
    devServer: {
      setupMiddlewares: (middlewares, devServer) => {
        if (!devServer) {
          throw new Error('webpack-dev-server is not defined');
        }
        devServer.app.get('/sso/username', (_, response) => {
          response.json({ username: ACCOUNT_NAME });
        });
        return middlewares;
      },
      compress: false,
      proxy: {
        /**
         * AIQA 应用 @王虹艺
         * 接口文档 : http://10.168.66.18:8082/project/1379/interface/api
         */
        '/aiqa-gpt-api': {
          target: 'http://aiqa.dealer.gpt.api.dev.mulan.corpautohome.com',
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            '^/aiqa-gpt-api': '/chat', // 重写路径（可选）
          },
          onProxyReq(proxyReq, req) {
            const accountParam = `account=${ACCOUNT_NAME}&_appId=aidealerqa-gpt-web`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        '/aiqa-api': {
          target: 'http://aiqa.dealer.api.dev.mulan.corpautohome.com',
          changeOrigin: true,
          pathRewrite: {
            '^/aiqa-api': '/aiqaapi',
          },
          onProxyReq(proxyReq, req) {
            const accountParam = `account=${ACCOUNT_NAME}&_appId=aidealerqa-gpt-web`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        /**
         * Strapi CMS 应用 :  @边凯
         *  一个 Headless CMS 管理平台, 提供日常的一些接口
         */
        '/strapi-api': {
          // target: "http://localhost:1337/", // 本地
          target: 'http://cms.mulan.corpautohome.com/', // 测试
          changeOrigin: true,
          pathRewrite: {
            '^/strapi-api': '/api', // 重写路径（可选）
          },
        },
        /**
         * VScode 插件页 :  视频资源 + 插件版本接口 @晏德智
         */
        '/static-bucket': {
          target: 'http://nfiles3four.in.autohome.com.cn',
          changeOrigin: true,
          pathRewrite: {
            '^/static-bucket': '/design-backend-store', // 重写路径（可选）
          },
        },
        '/design-api': {
          target: 'http://design-api.corpautohome.com', // 目标服务器地址
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            '^/design-api': '', // 重写路径（可选）
          },
        },
        /**
         * AI找人 应用 : @张俊
         */
        '/aifinder': {
          target: 'http://gpt-api.terra.corpautohome.com', // 测试
          changeOrigin: true,
          onProxyReq(proxyReq, req) {
            const accountParam = `account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        /**
         * gpt-api 应用 : @张俊
         * 应用地址 : http://home.console.cloud.corpautohome.com/aone/#/apply/appFlow/detail/15312/gpt-api?appFlowName=gpt-api%E6%B5%81%E6%B0%B4%E7%BA%BF&pipelineId=39409
         */
        '/api-cr': {
          target: 'http://gpt-api.terra.corpautohome.com',
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            '^/api-cr': '', // 重写路径（可选）
          },
          onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        /**
         * gpt-api 应用 : @张俊
         * 应用地址 : http://home.console.cloud.corpautohome.com/aone/#/apply/appFlow/detail/15312/gpt-api?appFlowName=gpt-api%E6%B5%81%E6%B0%B4%E7%BA%BF&pipelineId=39409
         */
        '/api': {
          target: 'http://gpt-api.terra.corpautohome.com',
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            '^/api': '/chat', // 重写路径（可选）
          },
          onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        '/file-api': {
          target: 'http://gpt-api.terra.corpautohome.com',
          changeOrigin: true, // 更改请求源
          pathRewrite: {
            '^/file-api': '/file', // 重写路径（可选）
          },
          onProxyReq(proxyReq, req) {
            const accountParam = `_appId=devchat&account=${ACCOUNT_NAME}`;
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        /**
         * 埋点统计 Api : @张俊
         */
        '/stat': {
          target: 'http://gpt-api.terra.corpautohome.com',
          changeOrigin: true,
          onProxyReq(proxyReq, req) {
            const accountParam = '_appId=devchat_local';
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },

        /**
         * sonar 应用 : @张俊
         */
        '/sonar-api': {
          target: 'http://projectquality-api-online.terra.corpautohome.com',
          changeOrigin: true,
          pathRewrite: {
            '^/sonar-api': '',
          },
          onProxyReq(proxyReq, req) {
            const accountParam = '_appId=aidev_sonar';
            const delimiter = req.url.includes('?') ? '&' : '?';
            proxyReq.path += delimiter + accountParam;
          },
        },
        /**
         * AI-Agent 应用 @zhaixiaowei
         * 接口文档 :
         */
        '/langchain-ai': {
          target: 'http://llm.proxy.terra.corpautohome.com/v2',
          changeOrigin: true,
          pathRewrite: {
            '^/langchain-ai': '',
          },
        },
      },
    },
    plugins: [
      new HtmlWebpackPlugin({
        inject: false,
        template: 'src/index.ejs',
        templateParameters: {
          isLocal: webpackConfigEnv && webpackConfigEnv.isLocal,
          orgName,
        },
      }),
    ],
  });
};
