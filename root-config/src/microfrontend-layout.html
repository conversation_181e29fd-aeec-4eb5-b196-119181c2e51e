<single-spa-router mode="history">
  <!--
    This is the single-spa Layout Definition for your microfrontends.
    See https://single-spa.js.org/docs/layout-definition/ for more information.
  -->

  <div class="app-root-wrap">
    <section class="app-root-navigation">
      <route path="/">
        <application name="@dealer/app-navbar"></application>
      </route>
    </section>
    <main class="app-root-pages">
      <route path="app-demo">
        <application name="@dealer/app-demo"></application>
      </route>
      <route path="app-demo-react">
        <application name="@dealer/app-demo-react"></application>
      </route>
      <route path="app-chatgpt">
        <application name="@dealer/app-chatgpt"></application>
      </route>
      <route path="app-chatgpt-origin">
        <application name="@dealer/app-chatgpt-origin"></application>
      </route>
      <route path="app-ai-finder">
        <application name="@dealer/app-ai-finder"></application>
      </route>
      <route path="app-product-gallery">
        <application name="@dealer/app-product-gallery"></application>
      </route>
      <route path="app-prompts">
        <application name="@dealer/app-prompts"></application>
      </route>
      <route path="app-aiqa-web">
        <application name="@dealer/app-aiqa-web"></application>
      </route>
      <route path="welcome">
        <application name="@single-spa/welcome"></application>
      </route>
    </main>
  </div>
</single-spa-router>