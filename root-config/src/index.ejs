<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>aidev - GPT 辅助提效平台</title>
  <link rel="stylesheet" href="https://z.autoimg.cn/dealer_microfe_aidev/libs/aidev/highlight.min.css">
  <link rel="stylesheet" href="https://z.autoimg.cn/dealer_microfe_aidev/assets/css/primeflex.css">
  <link rel="icon" type="image/png" href="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-navbar/ai_dev_logo_blue.png">
  <link rel="ico" type="image/x-icon" href="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-navbar/ai_dev_logo_blue.ico">
  <link rel="preload"
    href="https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/single-spa@5.9.0/lib/system/single-spa.min.js" as="script">
  <!--
    This CSP allows any SSL-enabled host and for arbitrary eval(), but you should limit these directives further to increase your app's security.
    Learn more about CSP policies at https://content-security-policy.com/#directive
  -->
  <!-- <meta http-equiv="Content-Security-Policy"
    content="default-src 'self' https: localhost:*; script-src 'unsafe-inline' 'unsafe-eval' https: localhost:*; connect-src https: localhost:* ws://localhost:* http://aiqa.mulan.corpautohome.com/ http://aidev1.mulan.corpautohome.com http://aiinsight-admin.terra.corpautohome.com; style-src 'unsafe-inline' https:; object-src 'none'; frame-src 'self' http://ssotest.corpautohome.com http://chatgpt.terra.corpautohome.com https://chatgpt.terra.corpautohome.com"> -->
  <meta name=" importmap-type" content="systemjs-importmap" />
  <!-- If you wish to turn off import-map-overrides for specific environments (prod), uncomment the line below -->
  <!-- More info at https://github.com/joeldenning/import-map-overrides/blob/master/docs/configuration.md#domain-list -->
  <!-- <meta name="import-map-overrides-domains" content="denylist:prod.example.com" /> -->

  <!-- Ftwo -->
  <script src="https://x.autoimg.cn/dealer/ftwo/202108-lts/index.js" crossorigin="anonymous"> </script>
  <script>
    var isOnline = location.host.indexOf('aidev.corpautohome.com') > -1;
    if (isOnline) {
      window.ftwo && window.ftwo.init({
        appKey: "e39cb7cb0fad4d84a24fb282fb0d9291",
        performance: {
          rum: {
            sampleRate: 100
          }
        }
      });
    }
  </script>
  <!-- Ftwo -->

  <!-- Shared dependencies go into this import map. Your shared dependencies must be of one of the following formats:
    1. System.register (preferred when possible) - https://github.com/systemjs/systemjs/blob/master/docs/system-register.md
    2. UMD - https://github.com/umdjs/umd
    3. Global variable
    More information about shared dependencies can be found at https://single-spa.js.org/docs/recommended-setup#sharing-with-import-maps.
  -->

  <!--
      Remove this if you only support browsers that support async/await.
      This is needed by babel to share largeish helper code for compiling async/await in older
      browsers. More information at https://github.com/single-spa/create-single-spa/issues/112
    -->
  <script src="https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/regenerator-runtime@0.13.7/runtime.min.js"></script>
  <!--
    If you need to support Angular applications, uncomment the script tag below to ensure only one instance of ZoneJS is loaded
    Learn more about why at https://single-spa.js.org/docs/ecosystem-angular/#zonejs
  -->
  <!-- <script src="https://cdn.jsdelivr.net/npm/zone.js@0.11.3/dist/zone.min.js"></script> -->
  <script
    src="https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/import-map-overrides@2.2.0/dist/import-map-overrides.min.js"></script>
  <script src="https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/systemjs@6.8.3/dist/system.min.js"></script>
  <script src="https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/systemjs@6.8.3/dist/extras/amd.min.js"></script>
</head>

<body>
  <noscript>
    You need to enable JavaScript to run this app.
  </noscript>

  <script>
    // 从 Strapi 获取 systemjs-importmap
    (function () {
      const localImportmap = {
        "imports": {
          "@single-spa/welcome": "https://unpkg.com/single-spa-welcome/dist/single-spa-welcome.js",
          // "@dealer/app-demo": "//localhost:9101/js/app.js",
          // "@dealer/app-demo-react": "//localhost:9102/dealer-app-demo-react.js",
          "@dealer/app-chatgpt": "//localhost:9202/js/app.js",
          // "@dealer/app-chatgpt-origin": "//localhost:9203/js/app.js",
          // "@dealer/app-product-gallery": "//localhost:9204/js/app.js",
          // "@dealer/app-ai-finder": "//localhost:9205/js/app.js",
          "@dealer/app-aiqa-web": "//localhost:9206/js/app.js",
          "@dealer/app-navbar": "//localhost:9201/js/app.js",
          "@dealer/utility-lib": "//localhost:9301/dealer-utility-lib.js",
          "@dealer/root-config": "//localhost:9000/dealer-root-config.js"
        }
      }
      const hostname = location.hostname;
      const env = detectEnv(hostname);
      const envForStrapi = (env === 'local' ? 'test' : env)
      fetch('/strapi-api/microfrontends')
        .then(response => response.json())
        .then(data => {
          data.data.forEach(function (value) {
            if (value.attributes.name === envForStrapi) {
              const scriptTextContent = JSON.stringify(value.attributes.apps)
              appendScript(scriptTextContent);
              // Note : 本地环境环境时, 使用 localImportmap 覆盖一部分接口返回的内容
              if (env === 'local') {
                appendScript(JSON.stringify(localImportmap));
              }
              System.import('@dealer/root-config');
              localStorage.setItem('microfrontends-root-importmap', scriptTextContent)
            }
          })
        }).catch(error => {
          let scriptTextContent;
          const defaultImportmap = {
            "imports": {
              "single-spa": "https://z.autoimg.cn/dealer_microfe_aidev/libs/npm/single-spa@5.9.0/lib/system/single-spa.min.js",
              "@dealer/app-demo": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-demo/0726/dist/js/app.0d33df59.js",
              "@dealer/app-navbar": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-navbar/002024/0709/dist/js/app.2f5026ed.js",
              "@dealer/app-chatgpt": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-chatgpt/002024/0710/dist/js/app.91448f3d.js",
              "@dealer/app-prompts": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-prompts/0913/dist/js/app.fa098830.js",
              "@dealer/root-config": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/root-config/02024/0709/dist/dealer-root-config.js",
              "@dealer/utility-lib": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/utility-lib/002024/0709/dist/dealer-utility-lib.js",
              "@dealer/app-aiqa-web": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-aiqa-web/2025/0522/dist/js/app.6e3f9bcf.js",
              "@dealer/app-ai-finder": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-ai-finder/0927/dist/js/app.4f13c36c.js",
              "@dealer/app-demo-react": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-demo-react/0726/dist/dealer-app-demo-react.js",
              "@dealer/app-chatgpt-origin": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-chatgpt-origin/1023/dist/js/app.7b6ea595.js",
              "@dealer/app-product-gallery": "https://z.autoimg.cn/dealer_microfe_aidev/aidev/app-product-gallery/002024/0604/dist/js/app.443b501a.js"
            }
          }
          const cookieForApp = localStorage.getItem('microfrontends-root-importmap');
          if (cookieForApp !== null) {
            scriptTextContent = cookieForApp;
          } else {
            scriptTextContent = JSON.stringify(defaultImportmap)
          }
          appendScript(scriptTextContent);
          System.import('@dealer/root-config');
        })

      /**
       * appendScript : 增加<script>到 html <head> 标签
       * @param {string} scriptTextContent 
       */
      function appendScript(scriptTextContent) {
        const script = document.createElement('script');
        script.type = 'systemjs-importmap';
        script.textContent = scriptTextContent;
        document.head.appendChild(script);
      }

      /**
       * detectEnv : 根据url判定当前的环境
       * @param {string} url 
       */
      function detectEnv(url) {
        let result = 'test';
        const regIsLocalEnv = /localhost|127\.0\.0\.1/;
        const regIsTestEnv = /\.mulan\./;
        const regIsPreEnv = /\.thallo\.|\.cupid\./;
        const regIsOnlineEnv = /aidev\.corpautohome\.com|aidev-pre\.corpautohome\.com/;
        if (url.search(regIsLocalEnv) > -1) {
          result = 'local';
        } else if (url.search(regIsTestEnv) > -1) {
          result = 'test';
        } else if (url.search(regIsPreEnv) > -1) {
          result = 'pre';
        } else if (url.search(regIsOnlineEnv) > -1) {
          result = 'online';
        }
        return result
      }
    }())

  </script>
  <import-map-overrides-full show-when-local-storage="devtools" dev-libs></import-map-overrides-full>
</body>

</html>