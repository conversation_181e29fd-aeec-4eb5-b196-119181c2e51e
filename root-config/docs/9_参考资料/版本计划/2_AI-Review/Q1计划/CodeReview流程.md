# CodeReview流程

根据不同业务组规模，各业务组可选择两种CodeReview流程：轻Reveiw、重Reiview

## 轻评审

### 定义

- 1-2两个开发人员，

- 很少有并行开发的情况，
  
- 业务重要程度不高

- 业务每次变动小，每个功能特性代码修改量很小
  
- master不受控，可以任意接受提交

### CodeReview策略

- 自己 + AI-Review对代码进行CodeReview

### 开发流程

- 1、开发人员直接在开发分支或master编写代码，提交代码
  
- 2、commit过程中，自行review代码，同时可以在IDEA中请求AI-Review给出优化建议

![](img/2024-03-20-17-31-51.png)

- 3、修改代码，commit代码

- 4、push后，收到AI-Review给出的优化建议

![](img/2024-03-20-17-48-11.png)

![](img/2024-03-20-17-36-09.png)

- 5、按照AI-Review优化建议进行代码修改，重新commit和push代码

## 重评审

### 定义

- 2个以上开发人员、1个主程序员

- 有并行开发情况（人员并行或功能并行）

- 业务重要程度高

- 业务每次变动大，每个功能特性代码修改量很大

- master受控，主程序确认合并后才能合并代码

### 分支开发情况

- 建议使用分支合并申请流程

### CodeReview策略

- 主程序员 + GPT辅助Review

### 开发流程

- 1、开发人员创建特性（feature、fix）分支

- 2、在特性分支编写代码，提交代码，同时按照【轻评审】流程，自己 + AI-Review对代码进行CodeReview

- 3、创建合并请求：将特性分支代码合并到master
  
![](img/2024-03-20-17-38-51.png)

- 4、主程序员 + AI-Review对代码进行CodeReview，提交review意见

![](img/2024-03-20-17-37-38.png)

- 5、开发人员根据进行修改review意见重新修改代码，提交代码

- 6、主程序员审批通过合并请求，git产生“合并”节点
  
![](img/2024-03-20-17-38-16.png)

## 案例

![](img/2024-03-20-17-57-27.png)

![](img/2024-03-20-17-58-18.png)

## commit message规范

https://git.corpautohome.com/dealercloud/PJ_DEALER_PIONEER_ZHYY_API/network/master

https://git.corpautohome.com/dealerinfo/crm-corp/network/master