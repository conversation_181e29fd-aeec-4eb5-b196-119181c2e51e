[{"title": {"icon": "", "text": "解释", "isCollected": false}, "content": "解释<strong>{概念或函数}</strong>在<strong>{编程语言}</strong>中的工作原理。"}, {"title": {"icon": "", "text": "修复Bug", "isCollected": false}, "content": "如何修复以下<strong>{解释功能}</strong>的<strong>{编程语言}</strong>代码？ <strong>{代码片段}</strong>"}, {"title": {"icon": "", "text": "最佳实践", "isCollected": false}, "content": "向我展示用<strong>{编程语言}</strong>编写<strong>{概念或函数}</strong>的最佳实践。"}, {"title": {"icon": "", "text": "优化", "isCollected": false}, "content": "优化以下<strong>{编程语言}</strong>代码，该代码<strong>{解释了功能}</strong>：<strong>{代码片段}</strong>"}, {"title": {"icon": "", "text": "提出一个框架", "isCollected": false}, "content": "您能为我的网站推荐一个合适的前端框架/库吗？？我正在制作<strong>{类型的网站}</strong>。"}, {"title": {"icon": "", "text": "编写代码注释", "isCollected": false}, "content": "重新生成下面的代码片段，但请在每行代码中添加注释<strong>{输入代码}</strong>"}, {"title": {"icon": "", "text": "生成自述文件", "isCollected": false}, "content": "为下面的代码生成文档。您应该包含详细说明，以允许开发人员在本地计算机上运行它，解释代码的用途，并列出此代码中存在的漏洞。 {输入代码}"}, {"title": {"icon": "", "text": "规划阶段", "isCollected": false}, "content": "我正处于软件开发项目的规划阶段，需要有关[特定任务/挑战]的指导。以下是一些项目详细信息：[提供简要概述、要求、时间表、团队和风险]。"}, {"title": {"icon": "", "text": "设计阶段", "isCollected": false}, "content": "我需要为我的软件项目设计有效数据模型的建议。以下是一些细节：[简要描述该项目、关键功能、用户需求、数据源和关系]。"}, {"title": {"icon": "", "text": "开发阶段", "isCollected": false}, "content": "我正在寻求以下方面的建议：[架构、数据库优化、基础设施、缓存/性能优化]。请提供您的建议和最佳实践，以确保我的项目的可扩展性。"}, {"title": {"icon": "", "text": "测试阶段", "isCollected": false}, "content": "您能帮我为此功能编写测试用例<strong>{解释该功能}</strong>吗？"}, {"title": {"icon": "", "text": "部署阶段", "isCollected": false}, "content": "该软件项目的最佳部署策略是什么？ <strong>{解释一下这个项目}</strong>"}]