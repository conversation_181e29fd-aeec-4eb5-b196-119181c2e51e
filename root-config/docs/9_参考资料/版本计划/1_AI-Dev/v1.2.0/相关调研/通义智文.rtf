{\rtf1\ansi\ansicpg936\cocoartf2758
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fnil\fcharset134 PingFangSC-Regular;\f1\fswiss\fcharset0 Helvetica;\f2\froman\fcharset0 Times-Roman;
}
{\colortbl;\red255\green255\blue255;\red0\green0\blue0;}
{\*\expandedcolortbl;;\cssrgb\c0\c0\c0;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 \'cd\'a8\'d2\'e5\'d6\'c7\'ce\'c4
\f1  ->  
\f0 \'c2\'db\'ce\'c4\'bd\'e2\'b6\'c1
\f1 \
	https://tongyi.aliyun.com/zhiwen/read?templateId=8&taskId=178013\
\
\
\
Google Scholar\
	https://scholar.google.com/scholar?hl=en&as_sdt=0%2C5&as_vis=1&q=+Code+Review+with+gpt&btnG=\
\
	https://www.ccsc.org/publications/journals/NW2023.pdf#page=38\
\
\
\
\
\pard\pardeftab720\partightenfactor0

\f2 \cf0 \expnd0\expndtw0\kerning0
\outl0\strokewidth0 \strokec2 \uc0\u35770 \u25991 2 \u65306 Automating Code Review Activities by Large-Scale Pre-training
\f1 \kerning1\expnd0\expndtw0 \outl0\strokewidth0 \
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0
\cf0 \
https://arxiv.org/pdf/2203.09095.pdf}