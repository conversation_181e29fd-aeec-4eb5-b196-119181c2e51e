local http = require('resty.http')

local conf = require(ngx.var.cas_conf or "cas_conf")
local cas_uri = conf.cas_uri
local cas_front_uri = conf.cas_front_uri
local cookie_name = conf.cookie_name or "NGXCAS"
local cookie_params = conf.cookie_params or "; Path=/"
local session_lifetime = conf.session_lifetime or 3600
local store = ngx.shared[conf.store_name or "cas_store"]


local function _to_table(v)
   if v == nil then
      return {}     
   elseif type(v) == "table" then
      return v
   else
      return { v }
   end
end

local function _uri_without_ticket()
   return ngx.var.scheme .. "://" .. ngx.var.host ..  ngx.re.sub(ngx.var.request_uri, "[?&]ticket=.*", "")
end

local function _uri_without_query()
   return ngx.var.scheme .. "://" .. ngx.var.host
end

local function _cas_login()
   return cas_front_uri .. "/login?" .. ngx.encode_args({ service = _uri_without_ticket() })
end

local function first_access()
   ngx.redirect(_cas_login(), ngx.HTTP_MOVED_TEMPORARILY)
end
   
local function _set_cookie(cookie_str)    
   local h = _to_table(ngx.header['Set-Cookie'])
   table.insert(h, cookie_str)
   ngx.header['Set-Cookie'] = h
end



local function _get_sessionId()
   return ngx.var["cookie_" .. cookie_name]
end

local function _set_our_cookie(val)
   _set_cookie(cookie_name .. "=" .. val .. cookie_params)
end

local function with_sessionId(sessionId)
   -- does the cookie exist in our store?
   local user = store:get(sessionId);
   if user == nil then
      -- the sessionId has expired
      -- remove cookie immediately otherwise the client hits an infinite loop if the invalid cookie still exists.
      _set_our_cookie("deleted; Max-Age=0")
      first_access()
   else
      -- refresh the TTL
      store:set(sessionId, user, session_lifetime)
      
      -- export REMOTE_USER header to the application
      ngx.req.set_header("REMOTE_USER", user)
      ngx.req.set_header("REMOTE_USER", user)
      ngx.req.set_header("X-Forwarded-Login", user)
      ngx.req.set_header("X-Forwarded-Name", user)
      ngx.req.set_header("X-Forwarded-Email", user .. "@autohome.com.cn")

   end
end

local function _set_store_and_cookie(sessionId, user)  
   -- place cookie into cookie store
   local success, err, forcible = store:add(sessionId, user, session_lifetime)
   if success then
      if forcible then
         ngx.log(ngx.WARN, "CAS cookie store is out of memory")
      end
      _set_our_cookie(sessionId)
   else      
      if err == "no memory" then
         -- store:add will attempt to remove old entries if it is full
         -- it should only happen in case of memory segmentation
         ngx.log(ngx.EMERG, "CAS cookie store is out of memory")
      elseif err == "exists" then
         ngx.log(ngx.ERR, "Same CAS ticket validated twice, this should never happen!")
      end
   end
   return success
end

local function _validate(ticket)
   -- send a request to CAS to validate the ticket
   local httpc = http.new()
   local res, err = httpc:request_uri(cas_uri .. "/serviceValidate", { query = { ticket = ticket, service = _uri_without_ticket() }, keepalive = false  })
  
   if res and res.status == ngx.HTTP_OK and res.body ~= nil then
      if string.find(res.body, "<cas:authenticationSuccess>") then
         local m = ngx.re.match(res.body, "<cas:user>(.*?)</cas:user>");
         if m then
            return m[1]
         end
      else
         ngx.log(ngx.INFO, "CAS serviceValidate failed: " .. res.body)
      end
   else
      ngx.log(ngx.ERR, err)
   end
   return nil
end

local function validate_with_CAS(ticket)
   local user = _validate(ticket)
   if user and _set_store_and_cookie(ticket, user) then
      -- remove ticket from url
      ngx.redirect(_uri_without_ticket(), ngx.HTTP_MOVED_TEMPORARILY)
   else
      first_access()
   end
end

local function get_username()
   local sessionId = _get_sessionId()
   if sessionId ~= nil then
      local sessionValue = store:get(sessionId)
      if sessionValue ~= nil then
         return sessionValue
      else
         return "nullSessionValue"
      end
   else
      return "nullSessionId"
   end
end

local function force_authentication()
   local sessionId = _get_sessionId()
   if sessionId ~= nil then
      return with_sessionId(sessionId)
   end

   local ticket = ngx.var.arg_ticket
   if ticket ~= nil then
      validate_with_CAS(ticket)
   else
      first_access()
   end
end

local function user_name()
   local userName = get_username()
   if userName == "nullSessionValue" or userName == "nullSessionId" then
      ngx.exit(510);
   else
      ngx.header['Content-Type'] = 'application/json; charset=utf-8';
      ngx.say('{"username":"' .. userName .. '"}');
      ngx.exit(200);
   end   
end

local function user_name_redirect()
   local userName = get_username()
   if userName == "nullSessionValue" or userName == "nullSessionId" then
      ngx.redirect(_uri_without_query(), ngx.HTTP_MOVED_TEMPORARILY)
   else
      ngx.header['Content-Type'] = 'application/json; charset=utf-8';
      ngx.say('{"username":"' .. userName .. '"}');
      ngx.exit(200);
   end
end

local function logout()
   local sessionId = _get_sessionId()
   if sessionId ~= nil then
      store:delete(sessionId)
      _set_our_cookie("deleted; Max-Age=0")
   end
   ngx.redirect(cas_front_uri .. "/logout?service=" .. local_logout)
end

local function logout_redirect()
   local sessionId = _get_sessionId()
   if sessionId ~= nil then
      store:delete(sessionId)
      _set_our_cookie("deleted; Max-Age=0")
   end
   ngx.redirect(cas_front_uri .. "/logout?service=" .. cas_front_uri .. "/login?" .. ngx.encode_args({ service = _uri_without_query() }))
end

local function get_default_code(code, defaultCode)
   local n = tonumber(code);
   if n then
      return n;
   end
   return defaultCode;
end

local function login_context_user(http_code, return_code, err_message) 
   local loginUserName = get_username();
   if loginUserName == "nullSessionValue" or loginUserName == "nullSessionId" then
      local t = type(return_code);
      if t == "number" then
         ngx.header['Content-Type'] = 'application/json; charset=utf-8';
         local message = err_message or "login user is empty";
         ngx.say('{"returncode":'.. return_code ..', "message":'.. message ..'}');
         local http_return_code = get_default_code(http_code, ngx.HTTP_OK);
         ngx.exit(http_return_code);
      else 
         local http_return_code = get_default_code(http_code, 403);
         ngx.exit(http_return_code);
      end
   end 
   return loginUserName;
end

local function append_user_to_header(header_key, return_code, err_message) 
   local loginUser = login_context_user(ngx.HTTP_OK,return_code,err_message);
   ngx.req.set_header(header_key, loginUser);
end

local function append_user_to_query(key, return_code, err_message)
   local loginUser = login_context_user(ngx.HTTP_OK,return_code,err_message);
   local params_args = ngx.req.get_uri_args();
   params_args[key] = loginUser;
   ngx.req.set_uri_args(params_args);
   local sessionId = _get_sessionId()
   if sessionId ~= nil then
      store:set(sessionId, loginUser, session_lifetime)
      
      -- export REMOTE_USER header to the application
      ngx.req.set_header("REMOTE_USER", loginUser)
      ngx.req.set_header("REMOTE_USER", loginUser)
      ngx.req.set_header("X-Forwarded-Login", loginUser)
      ngx.req.set_header("X-Forwarded-Name", loginUser)
      ngx.req.set_header("X-Forwarded-Email", loginUser .. "@autohome.com.cn")
    end

end

return {
   force_authentication = force_authentication;   
   logout = logout;
   logout_redirect = logout_redirect;
   user_name = user_name;
   user_name_redirect = user_name_redirect;
   get_username = get_username;
   login_context_user = login_context_user;
   append_user_to_header = append_user_to_header;
   append_user_to_query = append_user_to_query;
}