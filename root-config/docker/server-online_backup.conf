server {
    listen       80;
    server_name  localhost;
    location / {
        if ($arg_ideType = idea) {
          rewrite ^ https://devchat.corpautohome.com$request_uri? permanent;
        }
        access_by_lua_block {
          local ideAuthCode = ngx.var.cookie_ideAuthCode
          if not ideAuth<PERSON>ode then
            require('cas').force_authentication()
          else
            local cas_store = ngx.shared.cas_store
            local account = cas_store:get(ideAuthCode)
            if not account then
              local http = require "resty.http"
              local httpc = http.new()
              local res, err = httpc:request_uri("http://gpt-api.corpautohome.com/chat/ideAuthCodeLogin", {
                method = "POST",
                body = ideAuthCode,
              })

              if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
                require('cas').force_authentication()
              else
                local _, account_start = string.find(res.body, '"account":"')
                local account_end = string.find(res.body, '"', account_start + 1)
                local account = string.sub(res.body, account_start + 1, account_end - 1)
                cas_store:set(ideAuthCode, account)
              end
            end
          end
        }
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
        try_files $uri $uri/ /index.html;
        # 清除html的缓存
        add_header Cache-Control no-cache;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址，防止sso挂了，导致站点不停重启
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }

    # 需求一、YAPI
    location ^~/mockapi/ {
      proxy_pass http://doc.phonebus.corpautohome.com/;
      rewrite ^/mockapi/(.*)$ /mock/957/$1 break;
    }

    # basicapi
    location ^~/api/ {

      access_by_lua_block {
        local ideAuthCode = ngx.var.cookie_ideAuthCode
                  if not ideAuthCode then
                    require('cas').append_user_to_query('account',403,"login error");
                  else
                    local cas_store = ngx.shared.cas_store
                    local account = cas_store:get(ideAuthCode)
                    if not account then
                      local http = require "resty.http"
                      local httpc = http.new()
                      local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
                        method = "POST",
                        body = ideAuthCode,
                      })

                      if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
                        require('cas').append_user_to_query('account',403,"login error");
                      else
                        local _, account_start = string.find(res.body, '"account":"')
                        local account_end = string.find(res.body, '"', account_start + 1)
                        local account = string.sub(res.body, account_start + 1, account_end - 1)
                        cas_store:set(ideAuthCode, account)
                        local params_args = ngx.req.get_uri_args();
                        params_args['account'] = account;
                        ngx.req.set_uri_args(params_args);
                      end
                    else
                       local params_args = ngx.req.get_uri_args();
                       params_args['account'] = account;
                       ngx.req.set_uri_args(params_args);
                    end

                  end



      }

      proxy_pass  http://aiqa.dealer.gpt.api.mulan.corpautohome.com/chat/;

    }

    # ai-finder ai找人
    location ^~/aifinder/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://gpt-api.corpautohome.com/aifinder/;
    }

    # aiqa - aiqaapi
    location ^~/aiqaapi/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        params_args['_appId'] = "aidealerqa-gpt-web";
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://aiqa.dealer.api.mulan.corpautohome.com/aiqaapi/;
    }

    # static-bucket
    location ^~/static-bucket/ {
      proxy_pass  http://nfiles3four.in.autohome.com.cn/design-backend-store/;
    }

    # strapi
    location ^~/apistrapi/ {
      proxy_pass  http://aiinsight-admin.terra.corpautohome.com/api/;
    }

    # organization Api
    location ^~/organization-api/ {
        access_by_lua_block {
          require('cas').append_user_to_query('account',403,"login error");
        }
        proxy_pass http://organization-backend.qa.terra.corpautohome.com/;
        rewrite ^/organization-api/(.*)$ /organization/$1 break;
    }

    # quality Api
    location ^~/quality-api/ {
        access_by_lua_block {
          require('cas').append_user_to_query('account',403,"login error");
        }
        proxy_pass http://projectquality-api.terra.corpautohome.com/;
        rewrite ^/quality-api/(.*)$ /$1 break;
    }

    # bug Api
    location ^~/bug-api/ {
        access_by_lua_block {
          require('cas').append_user_to_query('account',403,"login error");
        }
        proxy_pass http://bug-api.terra.corpautohome.com/;
        rewrite ^/bug-api/(.*)$ /$1 break;
    }

    # 权限管理
    location ^~/daimon/ {
        access_by_lua_block {
          require('cas').append_user_to_query('account',403,"login error");
        }
        proxy_pass http://daimon-auth.yz.test.autohome.com.cn/;
        rewrite ^/daimon/(.*)$ /$1 break;
    }

    # 需求二、地址跳转代理
    # rewrite ~*/app/(.*)/(.*) /$1.html last;

    location ~* /sso/username$ {
                access_by_lua_block {
                    local ideAuthCode = ngx.var.cookie_ideAuthCode
                                      if not ideAuthCode then
                                         require('cas').user_name();
                                      else
                                        local cas_store = ngx.shared.cas_store
                                        local account = cas_store:get(ideAuthCode)
                                        if not account then
                                          local http = require "resty.http"
                                          local httpc = http.new()
                                          local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
                                            method = "POST",
                                            body = ideAuthCode,
                                          })

                                          if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
                                             require('cas').user_name();
                                          else
                                            local _, account_start = string.find(res.body, '"account":"')
                                            local account_end = string.find(res.body, '"', account_start + 1)
                                            local account = string.sub(res.body, account_start + 1, account_end - 1)
                                            cas_store:set(ideAuthCode, account)
                                          end
                                        end
                                        account = cas_store:get(ideAuthCode)
                                        ngx.header['Content-Type'] = 'application/json; charset=utf-8';
                                        ngx.say('{"username":"' .. account .. '"}');
                                        ngx.exit(200);
                                      end


                }
            }

            location ~* /sso/username/redirect$ {
                access_by_lua_block {
                    require('cas').user_name_redirect();
                }
            }

            location ~* /sso/logout$ {
                access_by_lua_block {
                    require('cas').logout_redirect();
                }
            }
}