server {
    listen       80;
    server_name  localhost;
    
   
    
    location / {
        if ($arg_ideType = idea) {
            rewrite ^ http://chatgpt.terra.corpautohome.com$request_uri? permanent;
        }
        access_by_lua_block {
          local function set_cookie(value)
                local cookies = ngx.header['Set-Cookie']
                local new_cookie =  value
        
                if type(cookies) == "table" then
                    -- 如果已经有多个cookie，那么添加新的cookie
                    table.insert(cookies, new_cookie)
                elseif type(cookies) == "string" then
                    -- 如果已经有一个cookie，那么创建一个新的表来存储所有的cookie
                    cookies = {cookies, new_cookie}
                else
                    -- 如果没有cookie，那么设置新的cookie
                    cookies = new_cookie
                end
        
                ngx.header['Set-Cookie'] = cookies
          end
        
        
          ngx.header['Content-Type'] = 'text/html'
          require('cas').force_authentication()
          
          
          local authCode = ngx.var.arg_authCode
          
          
          if authCode then
             local login_account = require('cas').get_username();
              -- 定义一个包含你的列表的表
              local allowed_accounts = {["zhangjun1102"] = true, ["biankai"] = true, ["fanbaoyin"] = true, ["ningfangwei"] = true, ["yandezhi"] = true,  ["zhouxiaoming"] = true}
            
              -- 检查account是否在表中
              if not allowed_accounts[login_account] then
                    -- 如果不在，返回"无权限"
                    ngx.say("无权限")
                    ngx.exit(ngx.HTTP_FORBIDDEN)
              end
          
             
             local md5 = ngx.md5(authCode .. "16cj@fS90$!U2~#8#29a-1")
           

             local ideAuthCode = md5
             local cas_store = ngx.shared.cas_store
             local account = cas_store:get(ideAuthCode)
             if not account then
                  local http = require "resty.http"
                  local httpc = http.new()
                  local res, err = httpc:request_uri("http://gpt-api.terra.corpautohome.com/chat/ideAuthCodeLogin", {
                    method = "POST",
                    body = ideAuthCode,
                  })

                  if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
                    
                    require('cas').force_authentication()
                    set_cookie("ideAuthCode=deleted; Max-Age=0; Path=/")
                    
                    set_cookie("chatDate=deleted; Max-Age=0; Path=/")
                  else
                    local _, account_start = string.find(res.body, '"account":"')
                    local account_end = string.find(res.body, '"', account_start + 1)
                    local account = string.sub(res.body, account_start + 1, account_end - 1)
                    cas_store:set(ideAuthCode, account)
                    set_cookie("ideAuthCode=" .. ideAuthCode .. "; Path=/; HttpOnly")
                    
                    local chatDate = ngx.var.arg_chatDate
                    if chatDate then
                        set_cookie("chatDate=" .. chatDate .. "; Path=/; HttpOnly")
                    else
                        set_cookie("chatDate=deleted; Max-Age=0; Path=/")
                    end
                    
                  end
            else
                set_cookie("ideAuthCode=" .. ideAuthCode .. "; Path=/; HttpOnly")
                
                local chatDate = ngx.var.arg_chatDate
                if chatDate then
                    set_cookie("chatDate=" .. chatDate .. "; Path=/; HttpOnly")
                else
                    set_cookie("chatDate=deleted; Max-Age=0; Path=/")
                end
            end
         
          else
             set_cookie("ideAuthCode=deleted; Max-Age=0; Path=/")
             
             set_cookie("chatDate=deleted; Max-Age=0; Path=/")
     
          
          end
          
         
        }
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
        try_files /index.html =404;
        # 清除html的缓存
        add_header Cache-Control no-store;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址，防止sso挂了，导致站点不停重启
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }


    # AIQA 应用 @王虹艺
    # basicapi
    location ^~/aiqa-gpt-api/ {
      access_by_lua_block {
        local ideAuthCode = ngx.var.cookie_ideAuthCode
        if not ideAuthCode then
          require('cas').append_user_to_query('account',403,"\"login error\"");
        else
          local cas_store = ngx.shared.cas_store
          local account = cas_store:get(ideAuthCode)
          if not account then
            local http = require "resty.http"
            local httpc = http.new()
            local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
              method = "POST",
              body = ideAuthCode,
            })

            if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
              require('cas').append_user_to_query('account',403,"\"login error\"");
            else
              local _, account_start = string.find(res.body, '"account":"')
              local account_end = string.find(res.body, '"', account_start + 1)
              local account = string.sub(res.body, account_start + 1, account_end - 1)
              cas_store:set(ideAuthCode, account)
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              ngx.req.set_uri_args(params_args);
            end
          else
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              ngx.req.set_uri_args(params_args);
          end
        end
      }
      proxy_pass  http://aiqa.dealer.gpt.api.dev.mulan.corpautohome.com/chat/;
    }
    location ^~/aiqa-api/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        params_args['_appId'] = "aidealerqa-gpt-web";
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://aiqa.dealer.api.dev.mulan.corpautohome.com/aiqaapi/;
    }

    # AI找人 应用 : @张俊
    location ^~/aifinder/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://gpt-api.terra.corpautohome.com/aifinder/;
    }

    # CodeReview : @张俊
    location ^~/api-cr/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        params_args['_appId'] = "aidev";
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://gpt-api.terra.corpautohome.com/;
    }
    
    location ^~/design-api/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://design-api.corpautohome.com/;
    }


    # Strapi Admin 应用 :  @边凯
    location ^~/strapi-api/ {
      proxy_pass  http://cms.mulan.corpautohome.com/api/;
    }


    # gpt-api 应用 : @张俊
    location ^~/api/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['_appId'] = "aidev";
        ngx.req.set_uri_args(params_args);
        
        local ideAuthCode = ngx.var.cookie_ideAuthCode
        if not ideAuthCode then
          require('cas').append_user_to_query('account',403,"\"login error\"");
        else
          local cas_store = ngx.shared.cas_store
          local account = cas_store:get(ideAuthCode)
          if not account then
            local http = require "resty.http"
            local httpc = http.new()
            local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
              method = "POST",
              body = ideAuthCode,
            })

            if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
              require('cas').append_user_to_query('account',403,"\"login error\"");
            else
              local _, account_start = string.find(res.body, '"account":"')
              local account_end = string.find(res.body, '"', account_start + 1)
              local account = string.sub(res.body, account_start + 1, account_end - 1)
              cas_store:set(ideAuthCode, account)
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              params_args['fake'] = 1;
              local chatDate = ngx.var.cookie_chatDate
              if chatDate then
                 params_args['date'] = chatDate;
              end
              
              ngx.req.set_uri_args(params_args);
            end
          else
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              params_args['fake'] = 1;
              local chatDate = ngx.var.cookie_chatDate
              if chatDate then
                 params_args['date'] = chatDate;
              end
              ngx.req.set_uri_args(params_args);
          end
        end
      }
      proxy_pass  http://gpt-api.terra.corpautohome.com/chat/;
    }


    # VScode 插件页 :  视频资源 + 插件版本接口 @晏德智
    location ^~/static-bucket/ {
      proxy_pass  http://nfiles3four.in.autohome.com.cn/design-backend-store/;
    }

    # 埋点统计 Api
    location ^~/stat/ {
      access_by_lua_block {
      
      
        local ideAuthCode = ngx.var.cookie_ideAuthCode
        if not ideAuthCode then
          
          require('cas').append_user_to_query('account',403,"\"login error\"");
          local params_args = ngx.req.get_uri_args();
          params_args['_appId'] = "devchat_test";
          ngx.req.set_uri_args(params_args);
        else
          local cas_store = ngx.shared.cas_store
          local account = cas_store:get(ideAuthCode)
          if not account then
            local http = require "resty.http"
            local httpc = http.new()
            local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
              method = "POST",
              body = ideAuthCode,
            })

            if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
              require('cas').append_user_to_query('account',403,"\"login error\"");
            else
              local _, account_start = string.find(res.body, '"account":"')
              local account_end = string.find(res.body, '"', account_start + 1)
              local account = string.sub(res.body, account_start + 1, account_end - 1)
              cas_store:set(ideAuthCode, account)
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              params_args['fake'] = 1;
              params_args['_appId'] = "devchat";
              ngx.req.set_uri_args(params_args);
            end
          else
              local params_args = ngx.req.get_uri_args();
              params_args['account'] = account;
              params_args['fake'] = 1;
              params_args['_appId'] = "devchat";
              ngx.req.set_uri_args(params_args);
          end
        end
        
      }
      proxy_pass http://gpt-api.terra.corpautohome.com/stat/;
    }

    # sonar 应用 : @张俊
    location ^~/sonar-api/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['account'] = require('cas').get_username();
        params_args['_appId'] = "aidev_sonar";
        ngx.req.set_uri_args(params_args);
      }
      proxy_pass  http://projectquality-api-online.terra.corpautohome.com/;
    }

    # ai-agent 应用 : @zhaixiaowei
    location ^~/langchain-ai/ {
      proxy_pass  http://llm.proxy.terra.corpautohome.com/v2/;
    }


    # 需求二、地址跳转代理
    # rewrite ~*/app/(.*)/(.*) /$1.html last;

    location ~* /sso/username$ {
        access_by_lua_block {
            local ideAuthCode = ngx.var.cookie_ideAuthCode
              if not ideAuthCode then
                  require('cas').user_name();
              else
                local cas_store = ngx.shared.cas_store
                local account = cas_store:get(ideAuthCode)
                if not account then
                  local http = require "resty.http"
                  local httpc = http.new()
                  local res, err = httpc:request_uri("http://gpt-api.corpauthome.com/chat/ideAuthCodeLogin", {
                    method = "POST",
                    body = ideAuthCode,
                  })

                  if not res or res.status ~= 200 or not res.body or not string.find(res.body, '"message":"success"') then
                      require('cas').user_name();
                  else
                    local _, account_start = string.find(res.body, '"account":"')
                    local account_end = string.find(res.body, '"', account_start + 1)
                    local account = string.sub(res.body, account_start + 1, account_end - 1)
                    cas_store:set(ideAuthCode, account)
                  end
                end
                account = cas_store:get(ideAuthCode)
                ngx.header['Content-Type'] = 'application/json; charset=utf-8';
                ngx.say('{"username":"' .. account .. '"}');
                ngx.exit(200);
              end
        }
    }

    location ~* /sso/username/redirect$ {
        access_by_lua_block {
            require('cas').user_name_redirect();
        }
    }

    location ~* /sso/logout$ {
        access_by_lua_block {
           
            require('cas').logout_redirect();
            
        }
    }
}
