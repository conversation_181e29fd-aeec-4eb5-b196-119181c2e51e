#!/bin/bash
NGINX_CONF="${DIGO_OPENRESTY_HOME}/nginx/conf/nginx.conf"
NGINX_OPT_LOG="${DIGO_OPENRESTY_HOME}/nginx/logs/nginx-opt.log"

opt_log(){
    echo "$(date +%Y-%m-%d_%H:%M:%S.%N) $1" | tee -a ${NGINX_OPT_LOG}
}

show_conf() {
    cat -n ${NGINX_CONF}
    echo "server conf: ${DIGO_OPENRESTY_HOME}/nginx/conf/servers/server.conf"
    cat -n ${DIGO_OPENRESTY_HOME}/nginx/conf/servers/server.conf
}

init() {
    opt_log "init nginx"
    is_init=`cat ${NGINX_OPT_LOG} | grep 'init nginx' | wc -l`
    if [ ${is_init} = "1" ]; then
        check_is_nginx_default_configuration
        replace_placeholder
        set_server_conf
        set_cas_server
        set_cas_front_server
        set_cas_session_timeout
    else
        opt_log "init failture: openresty is initialized"
    fi
}

check_is_nginx_default_configuration() {
    opt_log "check nginx default configuration"
    is_default_set=`cat ${NGINX_CONF} | grep '#it is openresty(nginx) default setting.' | wc -l`
    if [ ${is_default_set} = "0" ]; then
        opt_log "if you replace nginx.conf then the DIGO_CONNECT_TIMEOUT DIGO_READ_TIMEOUT DIGO_SEND_TIMEOUT variable will fail."
    fi
}

replace_placeholder() {
    opt_log "replace nginx conf default setting placeholder"
    sed -i "s=#{placeholder_digo_openresty_home}=${DIGO_OPENRESTY_HOME}=" ${NGINX_CONF}
    sed -i "s/#{placeholder_digo_connect_timeout}/${DIGO_CONNECT_TIMEOUT}/" ${NGINX_CONF}
    sed -i "s/#{placeholder_digo_read_timeout}/${DIGO_READ_TIMEOUT}/" ${NGINX_CONF}
    sed -i "s/#{placeholder_digo_send_timeout}/${DIGO_SEND_TIMEOUT}/" ${NGINX_CONF}

    if [ "${DIGO_APP_ENV}" = "online" -o "${DIGO_APP_ENV}" = "prod"  ];then
        if [ "${DIGO_COLLECT_ACCESS_LOG}" != "false" ]; then
          sed -i "s=#{placeholder_digo_openresty_collect_access_log}=log_by_lua_file ${DIGO_OPENRESTY_HOME}/log_by.lua;=" ${NGINX_CONF}
          opt_log "replace nginx conf collect accesslog"
        else
          sed -i "s/#{placeholder_digo_openresty_collect_access_log}//" ${NGINX_CONF}
          opt_log "replace nginx conf not collect access log"
        fi
    else
        if [ "${DIGO_COLLECT_ACCESS_LOG}" = "true" ]; then
            sed -i "s=#{placeholder_digo_openresty_collect_access_log}=log_by_lua_file ${DIGO_OPENRESTY_HOME}/log_by.lua;=" ${NGINX_CONF}
            opt_log "replace nginx conf collect accesslog"
        else
            sed -i "s/#{placeholder_digo_openresty_collect_access_log}//" ${NGINX_CONF}
            opt_log "replace nginx conf not collect access log"
        fi
    fi

    sed -i "s=#{placeholder_digo_openresty_home}=${DIGO_OPENRESTY_HOME}=" /etc/nxlog.conf
}

set_server_conf() {
    cp ${DIGO_OPENRESTY_HOME}/server_conf/server-${DIGO_APP_ENV}.conf ${DIGO_OPENRESTY_HOME}/nginx/conf/servers/server.conf
}

set_cas_server() {
    if [ ${DIGO_CAS_SERVER_URL} ]; then
        new_cas_server=${DIGO_CAS_SERVER_URL}
    elif [ "${DIGO_APP_ENV}" = "online" -o "${DIGO_APP_ENV}" = "prod" ]; then
        new_cas_server=sso.corpautohome.com
    fi

    if [ ${new_cas_server} ]; then
        sed -i "/cas_uri = \"http:\/\/ssotest.corpautohome.com\"/ s#ssotest.corpautohome.com#${new_cas_server}#" ${DIGO_OPENRESTY_HOME}/nginx/lua/cas_conf.lua
    fi
}

set_cas_front_server(){
    if [ ${DIGO_CAS_SERVER_FRONT_URL} ]; then
        new_cas_front_server=${DIGO_CAS_SERVER_FRONT_URL}
    elif [ "${DIGO_APP_ENV}" = "online" -o "${DIGO_APP_ENV}" = "prod" ]; then
        new_cas_front_server=http://sso.corpautohome.com
    fi


    if [ ${new_cas_front_server} ]; then
        sed -i "/cas_front_uri = \"http:\/\/ssotest.corpautohome.com\"/ s#http://ssotest.corpautohome.com#${new_cas_front_server}#" ${DIGO_OPENRESTY_HOME}/nginx/lua/cas_conf.lua
    fi
}

set_cas_session_timeout() {
    if [ ${DIGO_CAS_SESSION_TIMEOUT} ]; then
        sed -i "s/3600/${DIGO_CAS_SESSION_TIMEOUT}/" ${DIGO_OPENRESTY_HOME}/nginx/lua/cas_conf.lua
    fi
}

check_conf() {
    opt_log "check configure file and execute command: openresty -t "
    openresty -t
    if [ $? -eq 1 ];then
        openresty -t &>> ${NGINX_OPT_LOG} #for save check failure log
        exit -1
    else
        openresty -t &>> ${NGINX_OPT_LOG} #for save check log
    fi
}

reload() {
    opt_log "reload and execute command: openresty -s reload "
    openresty -s reload | tee -a ${NGINX_OPT_LOG}
}

openresty_pid() {
   echo `pgrep -f "nginx: master process openresty"`
}

start() {
    pid=$(openresty_pid)
    if [ -n "${pid}" ]
    then
        opt_log "openresty is already running (pid: $pid)"
    else
        opt_log "start openresty and execute command: openresty"
        openresty | tee -a ${NGINX_OPT_LOG}
    fi
}

stop() {
    pid=$(openresty_pid)
    if [ -n "${pid}" ]
    then
        opt_log "stop openresty and execute command: openresty -s stop"
        openresty -s stop | tee -a ${NGINX_OPT_LOG}
    else
        opt_log "openresty is not running"
    fi
}

source ${DIGO_SHELL_PATH}/cron.sh
set_cron() {
    cron_add "0 * * * * ${DIGO_SHELL_PATH}/nginx/split-nginx-access-log.sh >> ${DIGO_CRON_RUN_LOG} 2>&1"
    cron_add "0 0 * * * ${DIGO_SHELL_PATH}/nginx/del-nginx-access-log.sh >> ${DIGO_CRON_RUN_LOG} 2>&1"
}

case $1 in
    init)
      init
    ;;
    pid)
      openresty_pid
    ;;
    check)
      check_conf
    ;;
    reload)
      reload
    ;;
    start)
      start
    ;;
    stop)
      stop
    ;;
    restart)
      stop
      start
    ;;
    conf)
      show_conf
    ;;
    set_cron)
      set_cron
    ;;
    *)
      echo "Usage: $0 { init | start | stop | restart | check | reload | pid}"
    ;;
esac
exit 0
