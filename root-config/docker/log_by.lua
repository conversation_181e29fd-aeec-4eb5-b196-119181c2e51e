local logger = require "resty.logger.socket"

if not logger.initted() then
    local ok, err = logger.init{
        host = 'accesslog.daimon.lq.autohome.com.cn',
        port = 6004,
        periodic_flush = 5,
        flush_limit = 4096,
        drop_limit = 8192,
    }
    if not ok then
        ngx.log(ngx.ERR, "failed to initialize the logger: ", err)
        return
    else
        ngx.log(ngx.INFO, "success to init the logger")
    end
end

local current = ngx.var.time_iso8601
local request_time = ngx.var.request_time * 1000
local status = ngx.var.status
local remote_addr = ngx.var.remote_addr
local http_x_forwarded_for = ngx.var.http_x_forwarded_for or remote_addr
local host = ngx.var.host
local server_addr = ngx.var.server_addr
local request_method = ngx.var.request_method
local uri = ngx.var.request_uri
local args = ngx.var.args or ''
local http_user_agent = ngx.var.http_user_agent or '-'
local http_referer = ngx.var.http_referer or '-'
local bytes_sent = ngx.var.bytes_sent
local request_length = ngx.var.request_length
local upstream_addr = ngx.var.upstream_addr or '-'
local upstream_status = ngx.var.upstream_status or '0'
local upstream_response_time = (ngx.var.upstream_response_time or 0) * 1000

local msgTable = { "nginx",  current, tostring(request_time), tostring(status), remote_addr, http_x_forwarded_for, host, server_addr, request_method,
uri, args, http_user_agent, http_referer, tostring(bytes_sent), tostring(request_length), upstream_addr, tostring(upstream_status),tostring(upstream_response_time)}
local msg = table.concat(msgTable, "\t") .. "\n"
local bytes, err = logger.log(msg)
-- ngx.log(ngx.ERR, msg)
if err then
    ngx.log(ngx.ERR, "failed to log message: ", err)
    return
end
