# AIDEV - 主应用

新车事业部的 AI 聊天助手，web 前端工程使用微前端架构，框架采用 single-spa，当前为 AIDEV 主应用。

## 一、开发篇

### 负责人

- 业务方
  - 范宝胤
- 前端
  - 翟晓伟、边凯
- 服务端
  - 张俊

### 版本规划

- [里程碑](https://git.corpautohome.com/groups/dealer-arch/microfrontends-ai/-/milestones?sort=due_date_desc&state=all)
- [issues](https://git.corpautohome.com/groups/dealer-arch/microfrontends-ai/-/issues)

### 本地开发

```bash
yarn          // 安装依赖
yarn develop  // 访问 http://localhost:9000/
```

### 配置文件

关于`接口转发`、`SSO`等内容，见`webpack.config.js`文件；

更高级用法可咨询 `张俊`、`德智`。

### 第三方库

- 微前端框架 [single-spa](https://single-spa.js.org/docs/5.x/getting-started-overview)

## 二、部署篇

- Aone 应用
  - [microfrontends-ai-root](http://home.console.cloud.corpautohome.com/aone/#/apply/appFlow/detail/15595/microfrontends-ai-root?appFlowName=microfrontends-ai-root-%E6%B5%81%E6%B0%B4%E7%BA%BF&pipelineId=40290)
- 域名
  - 【测试】[aidev-test](http://aidev-testqa.mulan.corpautohome.com)、[aidev-testqa](http://aidev-testqa.mulan.corpautohome.com)
  - 【线上】[aidev](https://aidev.corpautohome.com)
- CDN
  - [AIDEV](https://festatic.corpautohome.com/filelist/64b63af8343ae99bcf2930a1?prefix=aidev%2F)
  - [公共 Libs](https://festatic.corpautohome.com/filelist/64b63af8343ae99bcf2930a1?prefix=libs%2F)

## 三、运营篇

- Strapi CMS
  - 主要用于管理 “微前端发布版本”、“更新日志”、“版本通知”等
  - [测试地址](http://cms.mulan.corpautohome.com/admin/)
  - [线上地址](http://headless-cms.corpautohome.com/admin/content-manager/collectionType/api::microfrontend.microfrontend?page=1&pageSize=10&sort=name:ASC)
- 用户行为监控（echometer）
  - 账号和密码，咨询`张俊`
  - 线上环境
    - [AI-DEV](https://echometer.autohome.com.cn/d/CwQVspZSz/ai-devtong-ji)
  - 测试环境
    - host `**************  f3.echometer.autohome.com.cn`
    - [AIDEV-TEST](http://f3.echometer.autohome.com.cn/d/FE4V_YDSz/aidev-test?orgId=1)
