.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
  .page-item {
    .page-link {
      position: relative;
      display: block;
      color: #384c6d;
      line-height: 1.25;
      text-decoration: none;
      border: none;
      background: #e6e6e6;
      padding: 9px 15px;
      margin-left: -1px;
      -webkit-transition: all 0.2s ease-in-out;
      -moz-transition: all 0.2s ease-in-out;
      -o-transition: all 0.2s ease-in-out;
      transition: all 0.2s ease-in-out;
    }
    .page-link:hover {
      border: none;
      background: #037afb;
      color: #fff;
      outline: 0 !important;
      box-shadow: none !important;
    }
  }
  .page-item.active {
    .page-link {
      border: none;
      background: #037afb;
      color: #fff;
      outline: 0 !important;
      box-shadow: none !important;
    }
  }
  .page-item.disabled {
    .page-link,
    .page-link:hover {
      cursor: default;
      opacity: 0.65;
      color: #384c6d;
      background-color: #e6e6e6;
    }
  }
  .page-item:first-child {
    .page-link {
      margin-left: 0;
      border-top-left-radius: 0.25rem;
      border-bottom-left-radius: 0.25rem;
    }
  }
  .page-item:last-child {
    .page-link {
      border-top-right-radius: 0.25rem;
      border-bottom-right-radius: 0.25rem;
    }
  }
}

.justify-content-end {
  justify-content: flex-end !important;
}
.aiqa-paginator-container {
  .p-paginator-content-start {
    margin-right: 10px !important;
  }
  .p-paginator-jtp-input .p-inputtext {
    text-align: center;
    width: 50px;
    max-width: fit-content !important;
  }
}

.p-paginator-page.p-paginator-page-selected {
  background-color: var(--p-paginator-nav-button-selected-background) !important;
}

