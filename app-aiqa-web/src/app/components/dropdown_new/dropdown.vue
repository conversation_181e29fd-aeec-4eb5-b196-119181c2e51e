<template>
  <dropdownnew>
    <template #toogle>
      <button>
        <i class="material-icons"> more_vert </i>
      </button>
    </template>
    <template #content>
      <a class="dropdown-item" @click="editSession">
        <i class="material-icons">edit</i>
        <span>编辑</span>
      </a>
      <div class="dropdown-divider"></div>
      <a class="dropdown-item" @click="deleteSession"
        ><i class="material-icons">delete_outline</i> <span>删除</span></a
      >
    </template>
  </dropdownnew>
</template>

<script>
import Dropdownnew from "@/app/components/dropdown_new/index.vue";
export default {};
</script>

<style lang="scss" scoped></style>
