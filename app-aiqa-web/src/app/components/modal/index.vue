<template>
  <section class="modal" :class="{ hide: isHide }">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalCenterTitle">Modal title</h5>
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <i class="material-icons" @click.prevent="cancel">close</i>
        </button>
      </div>
      <div class="modal-body">
        Cras mattis consectetur purus sit amet fermentum. Cras justo odio,
        dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac
        consectetur ac, vestibulum at eros.
      </div>
      <div class="modal-footer">
        <Button name="secondary" @click.prevent="cancel">Close</Button>
        <Button name="primary" @click.prevent="submit">Save changes</Button>
      </div>
    </div>
    <div class="modal-backdrop"></div>
  </section>
</template>

<script>
import Button from "@/app/components/button/index.vue";
export default {
  name: "Modal",
  components: {
    Button,
  },
  props: {
    isHide: {
      type: Boolean,
    },
  },
  data() {},
  emits: ["cancel", "submit"],
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      this.$emit("submit");
    },
  },
};
</script>

<style lang="scss">
@import "./index.scss";
</style>
