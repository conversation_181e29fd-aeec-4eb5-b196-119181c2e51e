.btn {
  display: inline-block;
  color: #212529;
  cursor: pointer;
  font-size: 13px;
  font-weight: bold;
  text-align: center;
  vertical-align: middle;
  line-height: 1.5;
  background-color: transparent;
  border: none;
  border-radius: 0.25rem;
  padding: 6px 10px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn.btn-primary {
  background: #037afb;
}
.btn.btn-primary:hover {
  background: #1c89ff !important;
}
.btn.btn-primary.active,
.btn.btn-primary:active,
.btn.btn-primary:focus {
  background: #1c89ff !important;
  box-shadow: 0 0 0 0.2rem rgba(3, 122, 251, 0.25);
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn.btn-secondary {
  background: #e6e6e6;
  color: #384c6d;
}
.btn.btn-secondary:hover {
  background: #dbdbdb !important;
  color: #384c6d !important;
}
.btn.btn-secondary.active,
.btn.btn-secondary:active,
.btn.btn-secondary:focus {
  background: #dbdbdb !important;
  box-shadow: 0 0 0 0.2rem rgba(230, 230, 230, 0.25);
}

// btn-link
.btn-link {
  font-weight: 400;
  color: #007bff;
  text-decoration: none;
}
.btn.btn-link {
  color: #037afb;
  text-decoration: none !important;
}
.btn.btn-link:hover {
  background: #e9f1fe !important;
}

.btn-warning {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn.btn-warning {
  background: #ffcd36;
}
