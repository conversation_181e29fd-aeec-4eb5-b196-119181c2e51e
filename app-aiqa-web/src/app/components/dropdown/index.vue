<template>
  <section
    class="dropdown-menu"
    :class="{ show: visibility }"
    x-placement="right-start"
    style="
      position: absolute;
      will-change: transform;
      top: 0px;
      left: 0px;
      transform: translate3d(133px, 0px, 0px);
    "
    :style="style"
  >
    <a class="dropdown-item" @click="markSession">
      <i class="material-icons">bookmark</i>
      <span>标记</span>
    </a>
    <a class="dropdown-item" @click="editSession">
      <i class="material-icons">edit</i>
      <span>编辑</span>
    </a>
    <div class="dropdown-divider"></div>
    <a class="dropdown-item" @click="deleteSession"
      ><i class="material-icons">delete</i> <span>删除</span></a
    >
  </section>
</template>

<script>
export default {
  name: "Dropdown",
  props: {
    visibility: {
      type: Boolean,
      required: true,
    },
    style: {
      type: Object,
      required: true,
    },
  },
  emits: ["mark-session", "edit-session", "delete-session", "close"],
  methods: {
    markSession() {
      this.$emit("mark-session");
      this.$emit("close");
    },
    editSession() {
      this.$emit("edit-session");
      this.$emit("close");
    },
    deleteSession() {
      this.$emit("delete-session");
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
@import "./index.scss";
</style>
