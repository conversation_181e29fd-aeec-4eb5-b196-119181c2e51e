async function get(url, params = null, headers = {}) {
  const options = {
    url,
    method: "GET",
    params,
    headers,
  };
  return request(options);
}

async function post(url, body = {}, params = null, headers = {}) {
  const options = {
    url,
    method: "POST",
    body,
    params,
    headers,
  };
  return request(options);
}

async function put(url, body = {}, headers = {}) {
  const options = {
    url,
    method: "PUT",
    body,
    headers,
  };
  return request(options);
}

async function deleteRequest(url, body = {}, headers = {}) {
  const options = {
    url,
    method: "DELETE",
    body,
    headers,
  };
  return request(options);
}

async function request(options) {
  let _url = options.url;
  const _options = {
    method: options.method || "GET",
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  };
  if (options.params) {
    _url += "?" + new URLSearchParams(options.params).toString();
  }
  if (options.body) {
    _options.body = JSON.stringify(options.body);
  }
  try {
    const response = await fetch(_url, _options);
    const data = response.json();
    if (!response.ok) {
      new Error(data.message || "请求失败");
    }
    return data;
  } catch (err) {
    console.error("request error:", err.message);
    throw err;
  }
}

export default {
  get,
  post,
  put,
  delete: deleteRequest, // 注意：delete 是保留字，需要别名导出
};
