import { defineStore } from "pinia";
import httpService from "@/app/utils/httpService";

export default defineStore("require", {
  state: () => ({
    require: {},
  }),
  actions: {
    async create(body) {
      const url = "/aiqa-api/require/create";
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async transMindTree(body) {
      const url = "/aiqa-api/require/transMindTree";
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    // async scoringRequirement(body) {
    //   const url = "/aiqa-api/require/scoreRequirementStandAlone";
    //   try {
    //     const data = await httpService.post(url, body);
    //     return data;
    //   } catch (err) {
    //     console.error("Error fetching data:", err.message);
    //   }
    // },
    // async improveRequirement(body) {
    //   const url = "/aiqa-api/require/improveRequirementStandAlone";
    //   try {
    //     const data = await httpService.post(url, body);
    //     return data;
    //   } catch (err) {
    //     console.error("Error fetching data:", err.message);
    //   }
    // },
    // async analysisRequirement(body) {
    //   const url = "/aiqa-api/require/analysisRequirementStandAlone";
    //   try {
    //     const data = await httpService.post(url, body);
    //     return data;
    //   } catch (err) {
    //     console.error("Error fetching data:", err.message);
    //   }
    // },
    // async testcaseRequirement(body) {
    //   const url = "/aiqa-api/require/testcaseRequirementStandAlone";
    //   try {
    //     const data = await httpService.post(url, body);
    //     return data;
    //   } catch (err) {
    //     console.error("Error fetching data:", err.message);
    //   }
    // },
  },
});
