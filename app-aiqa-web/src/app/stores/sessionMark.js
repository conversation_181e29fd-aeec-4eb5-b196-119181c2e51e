import { defineStore } from "pinia";
import httpService from "@/app/utils/httpService";

export default defineStore("sessionMark", {
  state: () => ({
    marks: [
      // {
      //   "name": "用例生成",
      //   "tagName": "用例生成",
      //   "id": 1
      // },
    ],
  }),
  actions: {
    async getMarkList() {
      const url = "/aiqa-api/sessionmark/markType/list";
      try {
        const data = await httpService.get(url);
        this.marks = data.result;
        return data.result;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async addMarkSession(params) {
      const url = "/aiqa-api/sessionmark/mark";
      try {
        const data = await httpService.post(url, {}, params);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async deleteMarkSession(params) {
      const url = "/aiqa-api/sessionmark/clear";
      try {
        const data = await httpService.post(url, {}, params);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
  },
});
