import { defineStore } from "pinia";
import httpService from "@/app/utils/httpService";

export default defineStore("chat", {
  state: () => ({
    ideAuthCode: "",
    session: {},
    sessions: [
      // {
      //   id: 399,
      //   createdAccount: "biankai",
      //   sessionName: "test1",
      //   systemMessage: null,
      //   createdStime: "2023-07-28T15:35:43.000+0800",
      //   modelName: "gpt-4",
      //   temperature: 0.8,
      //   sessionType: 0,
      // },
    ],
    message: {},
    messages: [
      // {
      //   id: 6874,
      //   sessionId: 399,
      //   questionRole: "biankai",
      //   questionContent: "返回10字的文案 from client",
      //   questionRoleType: 1,
      //   questionTime: "2023-08-03T11:22:30.000+0800",
      //   createdStime: "2023-08-03T11:22:30.000+0800",
      //   modifiedStime: "2023-08-03T11:22:30.000+0800",
      //   isDel: 0,
      //   contextIds: "",
      //   answerRole: "gpt-4",
      //   answerContent:
      //     "亲爱的客户，感谢您的支持，我们将竭诚为您提供优质服务。**来自客户**：这是我们的十个字文案。亲爱的客户，我们致力于为您提供最优质的服务体验。亲爱的客户，欢迎使用我们的产品，感谢您的支持！**客户寄语**：感谢支持，诚挚合作，共创辉煌！亲爱的客户，您的支持是我们前进的动力。谢谢！亲爱的客户，您的满意是我们最大的追求！亲爱的客户，非常感谢您选择我们的产品，期待为您提供优质服务。**来自客户：** 优质服务，技术保障，值得信赖！**来自客户**：这次合作非常满意，期待下次再度合作！客户说：请务必提高效率，质量第一，共创美好未来！亲爱的客户，我们为您提供最专业的技术支持和优质服务。亲爱的客户，我们竭诚为您提供优质的服务体验。您好，这是一份客户提供的精彩文案，请查收！尊敬的客户，感谢您选择我们的产品与服务。亲爱的客户，我们提供优质服务，感谢您的支持！亲爱的客户，我们郑重承诺为您提供优质服务和高性价比产品。`from client` 指示可能有误，如果您需要10字的文案，请提供更详细的信息。亲爱的用户，我们已收到您的需求，将尽快为您提供满意方案。",
      //   answerRoleType: 2,
      //   answerTime: "2023-08-03T11:24:09.000+0800",
      //   messageStatus: 5,
      //   temperature: 0.8,
      //   messageType: 0,
      // },
    ],
    remarkSessions: {
      returncode: 0,
      message: "",
      result: {
        rowcount: 9,
        pagecount: 1,
        pageindex: 1,
        list: [
          // {
          //   "chatSessionId": 325,
          //   "sessionName": "uu",
          //   "sessionMarkType": 3,
          //   "sessionMarkName": "工作助手",
          //   "sessionMarkTagName": "工作助手",
          //   "createdAccount": "biankai",
          //   "createdStime": "2023-08-16 21:55:53",
          //   "sessionType": 0
          // }
        ],
      },
    },
  }),
  getters: {
    getMessageIds(state) {
      let result = [];
      let messages = state.messages.slice();
      const len = messages.length;

      return function (num) {
        if (num >= 0 && num < len) {
          messages = messages.slice(len - num);
        }
        messages.forEach((value) => {
          result.push(value.id);
        });
        return result;
      };
    },
  },
  actions: {
    async getIdeAuthCode() {
      const url = "/aiqa-gpt-api/ideAuthCode";
      try {
        const data = await httpService.get(url);
        this.ideAuthCode = data.code;
        return data.data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async getSessions() {
      const url = "/aiqa-api/chat/chatSessions";
      try {
        const data = await httpService.get(url);
        this.sessions = data.result;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async createSession(body) {
      const url = "/aiqa-gpt-api/createSession";
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async updateSession(body) {
      const url = "/aiqa-gpt-api/updateSession";
      try {
        const data = await httpService.post(url, body);
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async deleteSession(params) {
      const url = "/aiqa-gpt-api/deleteSession";
      try {
        const data = await httpService.get(url, params);
        this.session = {};
        this.messages = [];
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async getMessages(params) {
      const url = "/aiqa-gpt-api/chatMessages";
      try {
        const data = await httpService.get(url, params);
        this.messages = data.data;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    async createMessage(body) {
      const url1 = "/aiqa-gpt-api/createMessage";
      let res1;
      try {
        res1 = await httpService.post(url1, body);
        return res1;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    // ///////////////////////////////////////////////////////
    /**
     * getRemarkSessions - 允许获取他人的 chat sessions 信息
     * @param {*} params
     */
    async getRemarkSessions(params) {
      const url = "/aiqa-api/chat/remarkChatSessions";
      const _params = {
        pageIndex: 1,
        pageSize: 10,
        ...params,
      };
      try {
        const data = await httpService.get(url, _params);
        this.remarkSessions = data;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
    /**
     * getMessagesBySessionId - 允许获取他人的 chat messages 信息
     * @param {*} params
     */
    async getMessagesBySessionId(params) {
      const url = "/aiqa-gpt-api/openapi/chatMessage/getBySessionId";
      const _params = {
        _appId: "aidealerqa-gpt-web",
        ...params,
      };
      try {
        const data = await httpService.get(url, _params);
        this.messages = data.data;
        return data;
      } catch (err) {
        console.error("Error fetching data:", err.message);
      }
    },
  },
});
