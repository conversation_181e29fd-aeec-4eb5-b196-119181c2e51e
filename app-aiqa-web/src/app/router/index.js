import { createRouter, createWebHistory } from "vue-router";

import Home from "@/pages/home/<USER>";
import History from "@/pages/history/Index.vue";
import Require from "@/pages/requireassis/Index.vue";
import Improve from "@/pages/requireassis/improve/Index.vue";

const routes = [
  {
    path: "/",
    name: "Home",
    component: Home,
  },
  {
    path: "/history",
    name: "History",
    component: History,
  },
  {
    path: "/requireassis",
    name: "Require",
    component: Require,
  },
  {
    path: "/improve",
    name: "Improve",
    component: Improve,
  },
];

const router = createRouter({
  history: createWebHistory("/app-aiqa-web/"),
  routes,
});

export default router;
