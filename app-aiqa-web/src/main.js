import "./main.scss";
import "github-markdown-css/github-markdown.css";

import { h, createApp } from "vue";
import singleSpaVue from "single-spa-vue";
import { createPinia } from "pinia";
import naive from "naive-ui";
import Toaster from "@meforma/vue-toaster";

// PrimeVue
import PrimeVue from 'primevue/config';

import registerPrimeVue from './registerPrimeVue'

import App from "./App.vue";
import router from "@/app/router";
import VeeValidatePlugin from "@/app/includes/validation";

const vueLifecycles = singleSpaVue({
  createApp,
  appOptions: {
    render() {
      return h(App, {
        // single-spa props are available on the "this" object. Forward them to your component as needed.
        // https://single-spa.js.org/docs/building-applications#lifecycle-props
        // name: this.name,
        // mountParcel: this.mountParcel,
        // singleSpa: this.singleSpa,
      });
    },
  },
  handleInstance: (app) => {
    app.use(router);
    app.use(naive);
    app.use(createPinia());
    app.use(VeeValidatePlugin);
    app.use(PrimeVue, {
      ripple: true,
    });
    registerPrimeVue(app);
    app.use(Toaster, {
      position: "top-right",
      duration: 4000,
    });
  },
});

export const bootstrap = vueLifecycles.bootstrap;
export const mount = vueLifecycles.mount;
export const unmount = vueLifecycles.unmount;
