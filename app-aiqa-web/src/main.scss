@import "@/app/components/modal/index";
@import "@/app/components/button/index";
@import "@/app/components/form/index";
@import "@/app/components/table/index";
@import "@/app/components/card/index";
@import "@/app/components/pagination/index";
@import "@/app/components/toast/index";
@import "@/app/components/badge/index";
@import "@/app/components/responsive/index";

.home-wrap {
  .markdown-body pre code,
  .markdown-body pre tt {
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
  }
}

// /* Extra small devices (phones, 600px and down) */
// @media only screen and (max-width: 600px) {
//   body {
//     background-color: lightblue;
//   }
// }

// /* Small devices (portrait tablets and large phones, 600px and up) */
// @media only screen and (min-width: 600px) {
//   body {
//     background-color: pink;
//   }
// }

// /* Medium devices (landscape tablets, 768px and up) */
// @media only screen and (min-width: 768px) {
//   body {
//     background-color: orange;
//   }
// }

// /* Large devices (laptops/desktops, 992px and up) */
// @media only screen and (min-width: 992px) {
//   body {
//     background-color: green;
//   }
// }

// /* Extra large devices (large laptops and desktops, 1200px and up) */
// @media only screen and (min-width: 1200px) {
//   body {
//     background-color: red;
//   }
// }

// /* Landscape */
// @media only screen and (orientation: landscape) {
//   body {
//     background-color: lightblue;
//   }
// }

// /* Portrait */
// @media only screen and (orientation: portrait) {
//   body {
//     background-color: pink;
//   }
// }
