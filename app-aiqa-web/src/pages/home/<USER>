<template>
  <div class="home-wrap card">
    <page-home-list />
    <page-home-content />
    <button-chat-toggle />
  </div>
</template>

<script>
import PageHomeList from "./list/index.vue";
import PageHomeContent from "./content/index.vue";
import ButtonChatToggle from "@/pages/components/ButtonChatToggle/index.vue";

export default {
  name: "PageHome",
  components: {
    PageHomeList,
    PageHomeContent,
    ButtonChatToggle,
  },
};
</script>

<style lang="sass" scoped>
.home-wrap
  position: relative
  font-size: 14px
  background-color: #fff
  padding: 20px 0
  margin: 0
  z-index: 9
  height: 100%
  .messages
    height: 500px
    background-color: #eee
  .user-input
    z-index: 9
    .notice
      color: #b33100
    .message-setting
      display: flex
      align-items: center
      height: 40px
      line-height: 40px
    .message-send
      position: relative
      text-align: right
      padding-right:70px
      margin-top: 10px
      z-index: 9
      button
        position: absolute
        top: 0
        right: 0
        z-index: 99
</style>
