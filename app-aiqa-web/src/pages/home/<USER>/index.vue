<template>
  <section class="aiqa-web-chatList">
    <div class="create">
      <button @click.prevent="editSessionDialog('create')">
        <i class="material-icons"> add </i>
        <span>新建会话</span>
      </button>
      <page-home-prompt />
    </div>
    <dl class="conversation">
      <dt>最近</dt>
      <dd v-for="(session, index) in sessions" :key="session.id">
        <div class="item" :class="{ active: activeIndex === index }" @click.prevent="selectSession(session, index)">
          <i class="material-icons"> chat_bubble_outline </i>
          <span class="text">{{ session.sessionName }}</span>
          <span v-if="session.sessionMarkTagName" class="badge badge-mini badge-dark">{{ session.sessionMarkTagName
            }}<button @click.prevent.stop="deleteMarkDialog(session.id)" class="close" type="button">
              X
            </button></span>
        </div>
        <div class="more" @click.prevent="(event) => selectSectionMore(event, session, index)">
          <i class="material-icons"> more_vert </i>
        </div>
      </dd>
    </dl>
    <div class="setting">
      <img class="avatar" src="https://z.autoimg.cn/dealer_microfe_aidev/assets/pic_head_user_1.jpeg" alt="avatar" />
      <p class="name">{{ username }}</p>
      <i class="material-icons" @click.prevent="openSettingDialog">
        settings
      </i>
    </div>

    <!-- Dropdown : 会话 - 更多操作 -->
    <dropdown :visibility="dropdown.visibility" :style="dropdown.style" @mark-session="editMarkDialog(dropdown.index)"
      @edit-session="editSessionDialog('edit', dropdown.index)"
      @delete-session="dialog.confirm.deleteSession.visibility = true" @close="dropdown.visibility = false"></dropdown>

    <!-- 弹窗 : 会话 - 标记  -->
    <teleport to="body">
      <section class="modal" v-show="dialog.mark.visibility">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.mark.title }}
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <i class="material-icons" @click.prevent="dialog.mark.visibility = false">close</i>
            </button>
          </div>
          <!-- 表单组件开始 -->
          <form @submit.prevent="sumbitMarkDialog">
            <div class="modal-body">
              <input type="hidden" name="chatSessionId" v-model="dialog.mark.data.chatSessionId" />
              <div class="form-group">
                <label for="model">本次会话尝试的方式</label>
                <select name="model" class="form-control custom-select" v-model="dialog.mark.data.markType">
                  <option v-for="mark in marks" :key="mark.id" :value="mark.id">
                    {{ mark.name }}
                  </option>
                </select>
                <p>*标记了尝试方向的会话, 可以共享给其他人查看</p>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" type="button" @click.prevent="dialog.mark.visibility = false">
                取消
              </button>
              <button type="submit" class="btn btn-primary">确认</button>
            </div>
          </form>
          <!-- 表单组件结束 -->
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>

    <!-- 弹窗 : 会话 - 创建&编辑  -->
    <teleport to="body">
      <section class="modal" v-show="dialog.session.visibility">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.session.title }}
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <i class="material-icons" @click.prevent="dialog.session.visibility = false">close</i>
            </button>
          </div>
          <!-- 表单组件开始 -->
          <form @submit.prevent="sumbitSessionDialog">
            <div class="modal-body">
              <input name="type" type="hidden" v-model="dialog.session.data.type" />
              <input name="id" type="hidden" v-model="dialog.session.data.id" />
              <div class="form-group">
                <label for="name">聊天名称</label>
                <input class="form-control" name="name" type="text" placeholder="请输入名称"
                  v-model="dialog.session.data.name" />
              </div>
              <div class="form-group">
                <label for="model">GPT模型</label>
                <select name="model" class="form-control custom-select" v-model="dialog.session.data.model">
                  <option value="gpt-4-turbo">gpt-4-turbo(推荐)</option>
                  <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
                  <option value="gpt-4">gpt-4</option>
                  <option value="chatglm-6b">chatglm-6b</option>
                  <option value="dalle2">dalle2</option>
                </select>
              </div>
              <div class="form-group">
                <label for="temperature">温度</label>
                <input class="custom-range" name="temperature" type="range" min="0" max="1" step="0.1"
                  v-model="dialog.session.data.temperature" style="width: calc(100% - 30px); margin-right: 5px" />
                <span>{{ dialog.session.data.temperature }}</span>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-secondary" type="button" @click.prevent="dialog.session.visibility = false">
                取消
              </button>
              <button type="submit" class="btn btn-primary">确认</button>
            </div>
          </form>
          <!-- 表单组件结束 -->
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>

    <!-- 弹窗 : 二次确认  -->
    <teleport to="body">
      <section class="modal" v-show="dialog.confirm.deleteSession.visibility">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.confirm.deleteSession.title }}
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <i class="material-icons" @click.prevent="dialog.confirm.deleteSession.visibility = false">close</i>
            </button>
          </div>
          <div class="modal-body">
            {{ dialog.confirm.deleteSession.content }}
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" type="button"
              @click.prevent="dialog.confirm.deleteSession.visibility = false">
              取消
            </button>
            <button type="submit" class="btn btn-primary" @click.prevent="deleteSessionDialog">
              确认
            </button>
          </div>
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>

    <!-- 弹窗 : 全局设置 -->
    <teleport to="body">
      <section class="modal" v-show="dialog.setting.visibility">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ dialog.setting.title }}
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <i class="material-icons" @click.prevent="dialog.setting.visibility = false">close</i>
            </button>
          </div>
          <div class="modal-body">
            <form>
              <div class="form-group">
                <label for="ide">IDE认证码</label>
                <input disabled class="form-control" name="ideAuthCode" type="text" placeholder="请输入名称"
                  v-model="dialog.setting.data.ideAuthCode" />
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" type="button" @click.prevent="dialog.setting.visibility = false">
              取消
            </button>
            <button type="submit" class="btn btn-primary" @click.prevent="sumbitSettingDialog">
              确认
            </button>
          </div>
        </div>
        <div class="modal-backdrop"></div>
      </section>
    </teleport>

    <div class="chat-list-bg"></div>
  </section>
</template>

<script>
import { mapState, mapWritableState, mapActions } from "pinia";
import useChatStore from "@/app/stores/chat";
import useChatvmStore from "@/app/stores/chatvm";
import useAppStore from "@/app/stores/app";
import useSessionMarkStore from "@/app/stores/sessionMark";

import PageHomePrompt from "@/pages/home/<USER>/index.vue";
import Dropdown from "@/app/components/dropdown/index.vue";
import { scrollToBottom } from "@/app/utils/index";

export default {
  name: "PageHomeList",
  components: {
    PageHomePrompt,
    Dropdown,
  },
  data() {
    return {
      activeIndex: -1,
      dropdown: {
        index: -1,
        visibility: false,
        style: {
          position: "fixed",
          top: "0px",
          left: "0px",
          transform: "translate(30px, -10px)",
          "z-index": 999,
        },
      },
      dialog: {
        mark: {
          title: "标记会话",
          visibility: false,
          data: {
            markType: 1,
            chatSessionId: 0,
          },
        },
        session: {
          title: "新建",
          visibility: false,
          data: {
            type: "create",
            id: null,
            name: "",
            model: "gpt-4-turbo",
            temperature: 0.5,
          },
        },
        setting: {
          title: "全局设置",
          visibility: false,
          data: {
            ideAuthCode: "",
            contextNum: -1,
          },
        },
        confirm: {
          deleteSession: {
            title: "删除",
            content: "确认要删除吗?",
            visibility: false,
          },
        },
      },
    };
  },
  computed: {
    ...mapState(useAppStore, ["username", "appConfig"]),
    ...mapWritableState(useChatStore, ["sessions", "session"]),
    ...mapWritableState(useSessionMarkStore, ["marks"]),
  },
  methods: {
    ...mapActions(useAppStore, ["setAppConfig"]),
    ...mapActions(useChatStore, [
      "getSessions",
      "createSession",
      "updateSession",
      "deleteSession",
      "getMessages",
      "getIdeAuthCode",
    ]),
    ...mapActions(useChatvmStore, ["stopSendMessage"]),
    ...mapActions(useSessionMarkStore, [
      "getMarkList",
      "addMarkSession",
      "deleteMarkSession",
    ]),
    async init() {
      await this.getSessions();
      if (this.sessions.length > 0) {
        this.selectSession(this.sessions[0], 0);
      }
      await this.getMarkList();
    },
    async selectSession(values, index) {
      this.stopSendMessage();
      const params = {
        chatSessionId: values.id,
      };
      this.session = values;
      this.activeIndex = index;
      await this.getMessages(params);
      // 滚动到底部
      scrollToBottom(".chat-content-inner .conversation");
    },
    selectSectionMore(event, values, index) {
      if (this.session.id === values.id) {
        this.dropdown.visibility = !this.dropdown.visibility;
      } else {
        this.dropdown.visibility = true;
      }
      this.dropdown.index = index;
      this.dropdown.style.left = `${event.clientX}px`;
      this.dropdown.style.top = `${event.clientY}px`;
    },
    editSessionDialog(type, index) {
      let options;
      const data = this.sessions[index];
      if (type === "create") {
        options = {
          title: "新建",
          visibility: true,
          data: {
            type: "create",
            id: null,
            name: "",
            model: "gpt-4-turbo",
            temperature: 0.5,
          },
        };
      } else {
        options = {
          title: "编辑",
          visibility: true,
          data: {
            type: "edit",
            id: data.id,
            name: data.sessionName,
            model: data.modelName,
            temperature: data.temperature,
          },
        };
      }
      this.dialog.session = options;
    },
    async sumbitSessionDialog() {
      const values = this.dialog.session.data;
      const data = {
        id: values.id,
        modelName: values.model,
        sessionName: values.name,
        sessionType: 0,
        temperature: values.temperature,
      };
      if (values.type === "create") {
        await this.createSession(data);
      } else {
        await this.updateSession(data);
      }
      this.dialog.session.visibility = false;
      this.init();
    },
    async deleteSessionDialog() {
      const _sessionIndex = this.dropdown.index;
      const _session = this.sessions[_sessionIndex];
      const params = {
        sessionId: _session.id,
      };
      await this.deleteSession(params);
      this.dialog.confirm.deleteSession.visibility = false;
      this.init();
    },
    async openSettingDialog() {
      this.dialog.setting.visibility = true;
      this.dialog.setting.data.contextNum = this.appConfig.contextNum;
      const data = await this.getIdeAuthCode();
      this.dialog.setting.data.ideAuthCode = data.code;
    },
    async sumbitSettingDialog() {
      this.dialog.setting.visibility = false;
    },
    async editMarkDialog(index) {
      this.dialog.mark.visibility = true;
      const session = this.sessions[index];
      this.dialog.mark.data.chatSessionId = session.id;
    },
    async deleteMarkDialog(id) {
      const params = {
        chatSessionId: id,
      };
      await this.deleteMarkSession(params);
      this.init();
    },
    async sumbitMarkDialog() {
      const markDialog = this.dialog.mark;
      markDialog.visibility = false;
      await this.addMarkSession(markDialog.data);
      this.init();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="sass" scoped>
.aiqa-web-chatList
  position: absolute
  left: 0
  top: 20px
  display: flex
  width: 288px
  height: calc( 100% - 40px )
  flex-direction: column
  justify-content: space-between
  padding: 0 16px
  z-index: 10
  .create
    display: flex
    justify-content: space-between
    align-items: center
    button
      display: flex
      justify-content: flex-start
      align-items: center
      color: #000
      font-size: 14px
      font-weight: 500
      line-height: 36px
      background-color: #eff3fa
      border-radius: 30px
      border: none
      padding: 1px 22px 1px 16px
      .material-icons
        margin-right: 10px
    button:hover
      cursor: pointer
      background-color: #e6e9f0
  .conversation
    flex: 1
    overflow-y: auto
    margin-top: 16px
    dt
      font-size: 12px
      font-weight: 500
      padding: 6px 14px
    dd
      margin: 0 0 5px 0
      position: relative
      z-index: 9
      .item
        display: flex
        justify-content: flex-start
        align-items: center
        height: 34px
        overflow: hidden
        border-radius: 30px
        padding: 6px 36px 6px 14px
        .material-icons
          font-size: 16px
          margin-right: 10px
        .text
          flex:1
          width: calc(100% - 18px)
          overflow: hidden
          white-space: nowrap
          text-overflow: ellipsis
      .item.active
        background-color: #ccedd2
      .more
        position: absolute
        display: flex
        justify-content: center
        align-items: center
        top: 6px
        right: 10px
        width: 24px
        height: 24px
        border-radius: 50%
        z-index: 99
        .material-icons
          opacity: 1
          font-size: 20px
      .more:hover
        cursor: pointer
        background-color: #dadada
        .material-icons
          opacity: 1
    dd:hover
      .item
        cursor: pointer
        background-color: #f4f3f2
      .item.active
        background-color: #ccedd2
      .more
        .material-icons
          opacity: 1
    z-index: 9
  .setting
    display: flex
    justify-items: center
    align-items: center
    height: 30px
    line-height: 40px
    color: #444746
    padding: 15px
    .avatar
      display: inline-block
      width: 34px
      height: 34px
      border-radius: 50%
      margin-right: 10px
    p.name
      flex:1
      font-weight: bold
    .material-icons
      color: #5f6368
.material-icons
  color: #5f6368
.material-icons:hover
  cursor: pointer
.m-dialog
  position: fixed
  left: 0
  top: 0
  display: flex
  width: 100%
  height: 100%
  justify-content: center
  align-items: center
  z-index: 999
  .m-dialog-content
    position: relative
    background-color: #fff
    z-index: 99
  .m-dialog-bg
    position: absolute
    width: 100%
    height: 100%
    background-color: rgba(0, 0, 0, 0.6)
    z-index: 9
</style>
