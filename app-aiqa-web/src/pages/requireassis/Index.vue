<template>
  <div class="history-wrap card">
    <div class="filter">
      <form @submit.prevent="submitHistory">
        <div class="form-group">
          <label for="chatSessionId">会话ID</label>
          <input
            class="form-control"
            name="chatSessionId"
            type="text"
            placeholder="请输入会话id"
            v-model="form.data.chatSessionId"
          />
        </div>
        <div class="form-group">
          <label for="sessionName">会话标题</label>
          <input
            class="form-control"
            name="sessionName"
            type="text"
            placeholder="请输入标题"
            v-model="form.data.sessionName"
          />
        </div>
        <div class="form-group">
          <label for="sessionCreator">会话账号</label>
          <input
            class="form-control"
            name="sessionCreator"
            type="text"
            placeholder="请输入账号"
            v-model="form.data.sessionCreator"
          />
        </div>
        <div class="form-group">
          <label for="sessionMarkType">尝试方向</label>
          <select
            name="sessionMarkType"
            class="form-control custom-select"
            v-model="form.data.sessionMarkType"
          >
            <option value="">全部</option>
            <option v-for="mark in marks" :key="mark.id" :value="mark.id">
              {{ mark.name }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <button
            class="btn btn-primary"
            type="submit"
            @click.prevent="() => submitHistory()"
          >
            筛选
          </button>
        </div>
      </form>
    </div>
    <div class="content">
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th scope="col" width="100">ID</th>
              <th scope="col" width="300">标题</th>
              <th scope="col" width="300">尝试方向</th>
              <th scope="col" width="">账号</th>
              <th scope="col" width="">创建时间</th>
              <th scope="col" width="">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="session in remarkSessions.result.list"
              :key="session.chatSessionId"
            >
              <td>{{ session.chatSessionId }}</td>
              <td>{{ session.sessionName }}</td>
              <td>{{ session.sessionMarkName }}</td>
              <td>{{ session.createdAccount }}</td>
              <td>{{ session.createdStime }}</td>
              <td class="action">
                <button
                  class="btn btn-link"
                  type="button"
                  @click.prevent="showMessagesDialig(session)"
                >
                  消息列表
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <nav aria-label="Page navigation example">
        <ul class="pagination justify-content-end">
          <li
            class="page-item"
            :class="{ disabled: remarkSessions.result.pageindex === 1 }"
            @click.prevent="submitHistory(remarkSessions.result.pageindex - 1)"
          >
            <a class="page-link" href="#">上一页</a>
          </li>
          <li
            class="page-item"
            v-for="index in remarkSessions.result?.pagecount"
            :key="index"
            :class="{ active: index === remarkSessions.result.pageindex }"
            @click.prevent="submitHistory(index)"
          >
            <a class="page-link" href="#">{{ index }}</a>
          </li>
          <li
            class="page-item"
            :class="{
              disabled:
                remarkSessions.result.pageindex ===
                remarkSessions.result?.pagecount,
            }"
            @click.prevent="submitHistory(remarkSessions.result.pageindex + 1)"
          >
            <a class="page-link" href="#">下一页</a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- 弹窗 : 消息列表  -->
    <section class="modal modal-chat" v-show="dialog.messages.visibility">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-header">
          <h5 class="modal-title">
            {{ dialog.messages.title }}
          </h5>
          <button
            type="button"
            class="close"
            data-dismiss="modal"
            aria-label="Close"
          >
            <i
              class="material-icons"
              @click.prevent="dialog.messages.visibility = false"
              >close</i
            >
          </button>
        </div>
        <div class="modal-body">
          <chat-form :messages="messages" />
        </div>
      </div>
      <div class="modal-backdrop"></div>
    </section>
    <!-- 弹窗 : 消息列表 (End)  -->
  </div>
</template>

<script>
import { mapWritableState, mapActions } from "pinia";
import useChatStore from "@/app/stores/chat";
import useSessionMarkStore from "@/app/stores/sessionMark";

import ChatForm from "@/pages/components/ChatForm/index.vue";

export default {
  name: "History",
  components: {
    ChatForm,
  },
  data() {
    return {
      form: {
        data: {
          chatSessionId: "",
          sessionName: "",
          sessionCreator: "",
          sessionMarkType: "",
          pageIndex: 1,
          pageSize: 10,
        },
      },
      dialog: {
        messages: {
          title: "",
          visibility: false,
        },
      },
    };
  },
  computed: {
    ...mapWritableState(useChatStore, ["remarkSessions", "messages"]),
    ...mapWritableState(useSessionMarkStore, ["marks"]),
  },
  methods: {
    ...mapActions(useChatStore, [
      "getRemarkSessions",
      "getMessagesBySessionId",
    ]),
    ...mapActions(useSessionMarkStore, ["getMarkList"]),
    async init() {
      // 获取url中的query, 加入到接口请求参数中
      this.form.data = this.queryToParams(this.form.data);
      await this.getRemarkSessions(this.form.data);
      await this.getMarkList();
    },
    async submitHistory(pageindex) {
      if (pageindex) {
        if (pageindex > this.remarkSessions.result?.pagecount || pageindex < 1) {
          return;
        }
        this.form.data.pageIndex = pageindex;
      } else {
        this.form.data.pageIndex = 1;
      }
      const params = this.form.data;
      await this.getRemarkSessions(params);
      this.setUrlQuery(params);
    },
    async showMessagesDialig(session) {
      this.dialog.messages.visibility = true;
      const params = {
        chatSessionId: session.chatSessionId,
      };
      await this.getMessagesBySessionId(params);
    },
    // ////////////////////  自定义函数 ////////////////////
    setUrlQuery(query) {
      this.$router.push({ query });
    },
    queryToParams(defaultParams) {
      let result = {};
      const query = this.$route.query;
      result = Object.assign({}, defaultParams, query);
      return result;
    },
  },
  created() {
    this.init();
  },
};
</script>

<style lang="sass" scoped>
.history-wrap
  position: relative
  padding: 20px 16px
  z-index: 9
  .filter
    form
      display: flex
      justify-content: space-between
      align-items: center
      .form-group
        flex:1
        display: flex
        justify-content: space-between
        align-items: center
        margin-right: 20px
        label
          width: 6em
          text-align: right
          margin-right: 5px
      .form-group:last-child
        flex: none
        width: 100px
  .content
    margin-top: 20px
    table
      font-size: 14px
      .btn-link
        font-size: 14px
  .modal-chat
    .modal-dialog
      width: 90%
      .chat-content
        margin-left:16px
</style>
