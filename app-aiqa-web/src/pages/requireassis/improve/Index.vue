<template>
    <div class="improve-wrap">
        <div class="left-box">
            <div class="require-title">需求澄清助手</div>
            <div class="require-sub-title">如有问题请联系经销商质保，欢迎 <a target="_blank" href="https://zhishi.autohome.com.cn/home/<USER>/file?targetId=px8AIJCatE">反馈建议</a></div>
            <textarea v-model="requireInput.requireContent" class="text-input"
                    placeholder="请输入需求文档提交给AI需求澄清助手。然后参考/复制助手整理后的文档，综合参考待澄清问题，对需求进行修改和完善，完善后可重复提交，多轮完善需求（但资源有限请勿浪费（勿改一个字提交一次即可)"></textarea>
            <div class="require-submit">
                <button @click="dosubmit('improve')" class="button-submit"
                        :disabled="improvestatus"><i class="material-icons align-icon">play_circle_outline</i>需求细化</button>
                <button @click="dosubmit('analysis')" class="button-submit"
                        :disabled="analysisstatus"><i class="material-icons align-icon">play_circle_outline</i>需求澄清</button>
                <button @click="dosubmit('testcase')" class="button-submit"
                        :disabled="testcasestatus"><i class="material-icons align-icon">play_circle_outline</i>测试用例</button>
            </div>
        </div>
        <div class="right-box">
            <div class="tabs">
                <button @click="currentTag = 'view'" :class="{ active: currentTag === 'view' }">预览
                    <pre v-if="viewstatus">执行中...</pre>
                </button>
                <button @click="currentTag = 'improve'" :class="{ active: currentTag === 'improve' }">Step1.需求细化
                    <pre v-if="improvestatus">执行中...</pre>
                </button>
                <button @click="currentTag = 'analysis'" :class="{ active: currentTag === 'analysis' }">Step2.需求澄清
                    <pre v-if="analysisstatus">执行中...</pre>
                </button>
                <button @click="currentTag = 'testcase'" :class="{ active: currentTag === 'testcase' }">Step3.测试用例生成
                    <pre v-if="testcasestatus">执行中...</pre>
                </button>
            </div>
            <div v-if="currentTag === 'view'" class="tab-content">
                <div class="content-display-title">
                    <h4>需求文档预览(Markdown)</h4>
                </div>
                <pre>注：如输入文档不是markdown格式，会显示原文</pre>
                <hr />
                <div v-html="viewMarkdown" class="content-display-overview"></div>
            </div>
            <div v-if="currentTag === 'improve'" class="tab-content">
                <div>
                    <div class="content-display-title">
                        <h3><i class="material-icons align-icon">menu</i>需求整理细化</h3>
                    </div>
                    <pre>注：经AI处理后的需求可能发生变化，建议检查是否符合预期后再使用</pre>
                    <hr />
                    <div><button class="link-button" @click="improveSwitchShow">{{ improveSwitchTitle }}</button></div>
                    <div v-html="improvedMarkdown" v-if="improveTab.showMd"></div>
                    <textarea v-model="improveTab.improvedOrigin" class="content-input-display"
                        v-if="!improveTab.showMd"></textarea>
                </div>
                <!--            <div v-html="improveTab.improvedOrigin" class="content-display"  v-if="!improveTab.showMd" ></div>-->
            </div>
            <div v-else-if="currentTag === 'analysis'" class="tab-content">
                <div class=" content-display-title">
                    <h3><i class="material-icons align-icon">menu</i>待澄清问题分析建议</h3>
                    <hr />
                </div>
<!--                <div v-html="analyMarkdown"></div>-->
                <div v-html="analyMarkdown" v-if="analyTab.showMd"></div>
                <textarea v-model="analyTab.analyOrigin" class="content-input-display"
                          v-if="!analyTab.showMd"></textarea>
            </div>
            <div v-else-if="currentTag === 'testcase'" class="tab-content">
                <div>
                    <div class=" content-display-title">
                        <h3><i class="material-icons align-icon">menu</i>测试用例生成</h3>
                        <hr />
                    </div>
                    <div>
                        <button class="link-button" @click="testcaseSwitchShow">{{ testcaseSwitchTitle }}</button>
                        <!--                    <button v-clipboard:copy="improveTab.improvedOrigin" v-clipboard:success="copySuccess" v-clipboard:error="copyError" class="link-button">复制为用例格式</button>-->
                        <button class="link-button" @click="copyMindCase">复制为用例格式</button>
                        <span>(可粘贴到<a href="http://meter.corpautohome.com/#/track/case/all"
                                target="_blank">metershpere</a>脑图模式某模块下）</span>
                    </div>
                    <div v-html="testcaseMarkdown" v-if="testCaseTab.showMd"></div>
                    <textarea v-model="testCaseTab.testCaseOrigin" class="content-input-display"
                        v-if="!testCaseTab.showMd"></textarea>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import MarkdownIt from 'markdown-it';
    import { mapActions } from "pinia";
    import requireStore from "@/app/stores/require";
    import { copyToClip } from '@/app/utils/copy';

    let md = new MarkdownIt({
        // html: true,
        linkify: true,
        typographer: true,
        breaks: true,
        // tables: true
    });
    export default {
        data() {
            return {
                text: '',
                requireInput: {
                    requireContent: "",
                },
                mddatas: {
                    scoreMarkdown: '',
                    improveMarkdown: '',
                    analysisMarkdown: '',
                    testcaseMarkdown: '',
                },
                improveTab: {
                    improvedOrigin: '',
                    showMd: true
                },
                analyTab: {
                    analyOrigin: '',
                    showMd: true
                },
                testCaseTab: {
                    testCaseOrigin: '',
                    showMd: true
                },
                scorestatus: false,
                viewstatus: false,
                improvestatus: false,
                analysisstatus: false,
                testcasestatus: false,
                currentTag: 'view',
                processing: false,
            }
        },
        computed: {
            viewMarkdown: function () {
                return md.render(this.requireInput.requireContent)
            },
            improvedMarkdown: function () {
                return md.render(this.improveTab.improvedOrigin.replace('data:','').replace('```markdown',''))
            },
            testcaseMarkdown: function () {
                return md.render(this.testCaseTab.testCaseOrigin.replace('data:','').replace('```markdown',''))
            },
            analyMarkdown: function () {
                return md.render(this.analyTab.analyOrigin.replace('data:','').replace('```markdown',''))
            },
            improveSwitchTitle() {
                return this.improveTab.showMd ? '显示源markdown文本' : '显示预览';
            },
            testcaseSwitchTitle() {
                return this.testCaseTab.showMd ? '显示源markdown文本' : '显示预览';
            }
        },
        methods: {
            ...mapActions(requireStore, [
                // "scoringRequirement",
                // "improveRequirement",
                // "analysisRequirement",
                // "testcaseRequirement",
                "transMindTree",
                "create"
            ]),

            async sendMsg(contentData, msgId) {
                this.setStatus(contentData, true);
                this.clearMD(contentData);

                const source = new EventSource('/aiqa-api/require/doTask?msgId=' + msgId);
                // 将重连间隔设置为一个非常大的值（例如24小时）
                source.retry = 24 * 60 * 60 * 1000;

                source.onopen = (err) => {
                    console.log(err);
                    console.log("The connection has been established.");
                };
                source.onmessage = (e) => {
                  try {
                    const data = JSON.parse(e.data);
                    // console.log("why  msssssss  " + JSON.stringify(data));
                    // 解析content字段
                    if (data.choices && data.choices[0] && data.choices[0].delta) {
                      const content = data.choices[0].delta.content;
                      if (content) {
                        this.updateMDDatas(contentData, content);
                      }
                    }
                  } catch (e) {
                    console.error('解析错误:', e);
                  }
                };
                source.addEventListener("end", (e) => {
                    console.log("event : end");
                    console.log("data:", e.data);
                    // this.chatForm.submit.disabled = false;
                    source.close();
                    // this.chatForm.stop.visibility = false;
                    this.setStatus(contentData, false);
                });
                // onerror version
                source.onerror = (err) => {
                    console.log(err);
                    console.log("An error occurred while attempting to connect.");
                    // this.chatForm.submit.disabled = false;
                    source.close();
                    // this.chatForm.stop.visibility = false;
                    this.setStatus(contentData, false);
                };
            },
            async updateMDDatas(key, value) {
                switch (key) {
                    case 'scoreMarkdown':
                        this.mddatas.scoreMarkdown = this.mddatas.scoreMarkdown + "" + value;
                        break;
                    case 'improveMarkdown':
                        this.improveTab.improvedOrigin = this.improveTab.improvedOrigin + "" + value;
                        break;
                  case 'analysisMarkdown':
                        this.analyTab.analyOrigin = this.analyTab.analyOrigin + "" + value;
                        break;
                  case 'testcaseMarkdown':
                        this.testCaseTab.testCaseOrigin = this.testCaseTab.testCaseOrigin + "" + value;
                        break;
                    default:
                }
            },
           setStatus(key, value) {
                switch (key) {
                    case 'scoreMarkdown':
                        this.scorestatus = value;
                        break;
                    case 'improveMarkdown':
                        this.improvestatus = value;
                        break;
                    case 'analysisMarkdown':
                        this.analysisstatus = value;
                        break;
                    case 'testcaseMarkdown':
                        this.testcasestatus = value;
                        break;
                    default:
                }
            },
           clearMD(key) {
                switch (key) {
                    case 'scoreMarkdown':
                        this.mddatas.scoreMarkdown = "";
                        break;
                    case 'improveMarkdown':
                        this.improveTab.improvedOrigin = "";
                        break;
                    case 'analysisMarkdown':
                        this.analyTab.analyOrigin = "";
                        break;
                    case 'testcaseMarkdown':
                        this.testCaseTab.testCaseOrigin = "";
                        break;
                    default:
                }
            },
            async dosubmit(key) {
                try {
                    await this.sendText(key);
                } catch (error) {
                    console.error(error);
                }
            },
            async improveSwitchShow() {
                this.improveTab.showMd = !this.improveTab.showMd;
            },
            async testcaseSwitchShow() {
                this.testCaseTab.showMd = !this.testCaseTab.showMd;
            },
            async copyMindCase() {
                try {
                    const casereq = { "markdownText": this.testCaseTab.testCaseOrigin };
                    const mindRs = await this.transMindTree(casereq);
                    await copyToClip(mindRs.result.mindTree || '');
                    this.$toast.warning("复制成功!");
                }
                catch {
                    this.$toast.warning("转换/复制失败!");
                }
            },
            async sendText(key) {
                const requireContent = this.requireInput.requireContent;
                if (!requireContent || requireContent.length < 1) {
                    this.$toast.warning("请输入需求内容!");
                    return;
                }
                this.currentTag = key;
                const body = this.requireInput;
                const createRs = await this.create(body);
                switch (key) {
                    case 'score':
                        break;
                    case 'improve':
                        this.sendMsg("improveMarkdown", createRs.result.msgIdImprove);
                        break;
                    case 'analysis':
                        this.sendMsg("analysisMarkdown", createRs.result.msgIdAnalysis);
                        break;
                    case 'testcase':
                        this.sendMsg("testcaseMarkdown", createRs.result.msgIdTestcase);
                        break;
                    default:
                }
            },
        }
    }
</script>

<style scoped>
    .link-button {
        background: none;
        border: none;
        color: blue;
        text-decoration: underline;
        cursor: pointer;
        padding: 5px;
        display: inline-block;
    }

    .content-display-title {
        color: dodgerblue;
    }

    .require-title {
        color: dodgerblue;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .require-sub-title {
        margin-bottom: 10px;
    }

    .tabs button {
        padding: 10px;
        border: none;
        background: none;
        cursor: pointer;
    }

    .tabs button.active {
        color: dodgerblue;
        font-weight: bold;
        border-bottom: 2px solid dodgerblue;
    }

    .align-icon {
        vertical-align: middle;
    }
</style>

<style lang="sass" scoped>
    .improve-wrap
        display: flex
        width: 100%
        height: 100%
        padding: 40px 40px 0
        justify-content: space-between
        .left-box
            width: 30%
            display: flex
            flex-direction: column

            .text-input
                width: 100%
                display: flex
                flex: 1
            .require-submit
                margin: 20px 0
                width: 100%
                height: 40px
                display: flex
                justify-content: space-evenly
                .button-submit
                    width: 25%
                    height: 100%
                    font-size: 16px
                    border: none
                    background-color: #4CAF50
                    color: #fff
                .button-submit[disabled]
                    background-color: #ccc
        .right-box
            padding-bottom: 20px
            width: 65%
            display: flex
            flex-direction: column
            overflow-y: scroll
            .tabs
                display: flex
                justify-content: space-around
            .tab-content
                flex: 1
                overflow-y: scroll
            .content-input-display
                padding-top: 5px
                width: 90%
                height: 100%
                min-height: 500px
                margin-bottom: 20px

</style>