{"name": "@dealer/app-demo-vue2", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 9102", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "serve:standalone": "vue-cli-service serve --mode standalone --port 9102"}, "dependencies": {"core-js": "^3.8.3", "single-spa-vue": "^2.1.0", "vue": "^2.6.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-cli-plugin-single-spa": "~3.3.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}