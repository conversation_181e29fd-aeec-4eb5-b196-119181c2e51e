<template>
  <section class="iframe-wrap">
    <iframe width="100%" :height="iframeHeight" :src="iframeUrl" frameborder="0"></iframe>
  </section>
</template>

<script>
export default {
  name: "Home",
  components: {},
  data() {
    return {
      iframeHeight: window.innerHeight - 95 || 500,
      iframeUrl: '',
    };
  },
  methods: {
    handleResize() {
      this.iframeHeight = `${window.innerHeight - 95 || 500}px` || "500px";
    }
  },
  mounted() {
    const host = window.location.host;
    this.iframeUrl = host.indexOf('mulan') > -1 ? 'http://chatgpt.terra.corpautohome.com/' : 'https://devchat.corpautohome.com/';
    window.addEventListener('resize', this.handleResize);
  },
  unmounted() {
    window.removeEventListener('resize', this.handleResize);
  }
};
</script>

<style scoped lang="sass"></style>
